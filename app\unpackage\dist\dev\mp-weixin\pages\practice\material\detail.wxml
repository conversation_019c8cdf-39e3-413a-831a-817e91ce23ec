<view class="material-detail-ios data-v-0054fb44"><back vue-id="0a496b30-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="资料详情" class="data-v-0054fb44" bind:__l="__l"></back><block wx:if="{{isLoad}}"><scroll-view class="main-content data-v-0054fb44" scroll-y="{{true}}"><view class="info-card shadow data-v-0054fb44"><view class="info-header data-v-0054fb44"><text class="info-title data-v-0054fb44">{{info.name}}</text><view class="info-tags data-v-0054fb44"><view class="cu-tag bg-green light price-tag data-v-0054fb44"><text class="data-v-0054fb44">{{info.price}}</text><text class="cuIcon-coin text-yellow data-v-0054fb44" style="margin-left:4rpx;font-size:24rpx;"></text></view></view></view><view class="info-desc data-v-0054fb44">{{info.desc}}</view><view class="info-meta data-v-0054fb44"><view class="meta-item data-v-0054fb44"><text class="cuIcon-hotfill text-red data-v-0054fb44"></text><text class="meta-label data-v-0054fb44">兑换人数</text><text class="meta-value data-v-0054fb44">{{info.hot}}</text></view><block wx:if="{{info.expire_text}}"><view class="meta-item data-v-0054fb44"><text class="cuIcon-timefill text-blue data-v-0054fb44"></text><text class="meta-label data-v-0054fb44">有效期</text><text class="meta-value data-v-0054fb44">{{info.expire_text}}</text></view></block></view></view><block wx:if="{{info.version==2}}"><view class="preview-section data-v-0054fb44"><button data-event-opts="{{[['tap',[['openTap',['$event']]]]]}}" class="cu-btn block bg-blue radius preview-btn data-v-0054fb44" bindtap="__e"><text class="cuIcon-read data-v-0054fb44" style="margin-right:8rpx;"></text>查看内容</button></view></block><block wx:if="{{info.version==1}}"><view class="preview-section data-v-0054fb44"><button data-event-opts="{{[['tap',[['readTap',['$event']]]]]}}" class="cu-btn block bg-blue radius preview-btn data-v-0054fb44" bindtap="__e"><text class="cuIcon-read data-v-0054fb44" style="margin-right:8rpx;"></text>阅读资料</button></view></block><view class="desc-card shadow data-v-0054fb44"><view class="desc-title data-v-0054fb44">资料介绍</view><view class="desc-content data-v-0054fb44">{{info.desc}}</view></view><view class="ad-section data-v-0054fb44"><adbanner vue-id="0a496b30-2" unitId="adunit-e9f553c403a978f6" class="data-v-0054fb44" bind:__l="__l"></adbanner></view></scroll-view></block><block wx:if="{{isLoad}}"><view class="action-bar safe-area-inset-bottom data-v-0054fb44"><button data-event-opts="{{[['tap',[['exchangeTap',['$event']]]]]}}" class="cu-btn bg-green radius action-btn data-v-0054fb44" bindtap="__e">积分兑换</button><button data-event-opts="{{[['tap',[['buyTap',['$event']]]]]}}" class="cu-btn bg-orange radius action-btn data-v-0054fb44" bindtap="__e">直接购买</button></view></block><view class="{{['cu-modal','data-v-0054fb44',showProcessModal?'show':'']}}"><view class="cu-dialog data-v-0054fb44"><view class="cu-bar bg-white justify-end data-v-0054fb44"><view class="content data-v-0054fb44">正在加载资料中</view></view><view class="padding bg-white data-v-0054fb44"><view class="cu-progress data-v-0054fb44"><view class="bg-blue data-v-0054fb44" style="{{'width:'+(processRate)+';'}}">{{processRate}}</view></view></view></view></view><confirm vue-id="0a496b30-3" title="资料内容" content="{{info.content}}" status="{{showContent}}" confirmText="已复制内容" confirmTextClass="cuIcon-check" data-event-opts="{{[['^updateStatus',[['__set_sync',['$0','showContent','$event'],['']]]]]}}" bind:updateStatus="__e" class="data-v-0054fb44" bind:__l="__l"></confirm></view>