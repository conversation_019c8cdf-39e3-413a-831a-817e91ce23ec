<view class="{{['responsive-demo','data-v-4f69b280',responsiveClass]}}"><back vue-id="311a3246-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="响应式演示" class="data-v-4f69b280" bind:__l="__l"></back><responsive-container vue-id="311a3246-2" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><view class="demo-section data-v-4f69b280"><view class="section-title data-v-4f69b280">屏幕信息</view><view class="info-card data-v-4f69b280"><view class="info-item data-v-4f69b280"><text class="label data-v-4f69b280">断点:</text><text class="value data-v-4f69b280">{{screenInfo.breakpoint}}</text></view><view class="info-item data-v-4f69b280"><text class="label data-v-4f69b280">宽度:</text><text class="value data-v-4f69b280">{{screenInfo.width+"px"}}</text></view><view class="info-item data-v-4f69b280"><text class="label data-v-4f69b280">高度:</text><text class="value data-v-4f69b280">{{screenInfo.height+"px"}}</text></view><view class="info-item data-v-4f69b280"><text class="label data-v-4f69b280">大屏设备:</text><text class="value data-v-4f69b280">{{isLargeDevice?'是':'否'}}</text></view></view></view></responsive-container><responsive-container vue-id="311a3246-3" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><view class="demo-section data-v-4f69b280"><view class="section-title data-v-4f69b280">响应式网格</view><responsive-grid class="demo-grid data-v-4f69b280" vue-id="{{('311a3246-4')+','+('311a3246-3')}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{12}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><view class="grid-item data-v-4f69b280"><view class="item-content data-v-4f69b280">{{n}}</view></view></block></responsive-grid></view></responsive-container><responsive-container vue-id="311a3246-5" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><view class="demo-section data-v-4f69b280"><view class="section-title data-v-4f69b280">媒体查询演示</view><match-media breakpoint="xs" class="data-v-4f69b280"><view class="media-demo xs data-v-4f69b280">超小屏幕 (xs) 显示</view></match-media><match-media breakpoint="sm" class="data-v-4f69b280"><view class="media-demo sm data-v-4f69b280">小屏幕 (sm) 显示</view></match-media><match-media breakpoint="md" class="data-v-4f69b280"><view class="media-demo md data-v-4f69b280">中等屏幕 (md) 显示</view></match-media><match-media breakpoint="lg" class="data-v-4f69b280"><view class="media-demo lg data-v-4f69b280">大屏幕 (lg) 显示</view></match-media><match-media breakpoint="xl" class="data-v-4f69b280"><view class="media-demo xl data-v-4f69b280">超大屏幕 (xl) 显示</view></match-media></view></responsive-container><responsive-container vue-id="311a3246-6" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><view class="demo-section data-v-4f69b280"><view class="section-title data-v-4f69b280">响应式文本</view><view class="text-demo data-v-4f69b280"><text class="text-xs-sm text-sm-md text-md-lg text-lg-lg text-xl-lg data-v-4f69b280">这段文字在不同屏幕尺寸下显示不同大小</text></view></view></responsive-container><responsive-container vue-id="311a3246-7" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><view class="demo-section data-v-4f69b280"><view class="section-title data-v-4f69b280">响应式布局</view><view class="layout-demo data-v-4f69b280"><view class="sidebar d-xs-none d-sm-none d-md-block data-v-4f69b280"><view class="sidebar-content data-v-4f69b280">侧边栏 (中屏及以上显示)</view></view><view class="main-content data-v-4f69b280"><view class="content-card data-v-4f69b280"><view class="card-title data-v-4f69b280">主要内容</view><view class="card-body data-v-4f69b280">这是主要内容区域，在小屏幕上占满宽度，在大屏幕上与侧边栏并排显示。</view></view></view></view></view></responsive-container></view>