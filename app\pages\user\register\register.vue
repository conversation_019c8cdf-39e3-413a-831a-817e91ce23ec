<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="会员注册"></back>
		<view v-if="!checkAppIsAudit">
			<safeCheck :sid.sync="username" :scene="10" :status.sync="showConfirm" confirmText="确认输入"
				confirmTextClass="cuIcon-roundcheck" @complete="safeCheckCompleteTap"></safeCheck>
			<view class="cu-form-group margin-top">
				<view class="title">手机号码</view>
				<input @input="usernameInputTap" maxlength="11" placeholder="请输入手机号" placeholderStyle="color:#999;"
					type="number" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">登录密码</view>
				<input @input="passwordInputTap" maxlength="20" :password="true" placeholder="请输入登录密码"
					placeholderStyle="color:#999;" type="text" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">确认密码</view>
				<input @input="confirmPasswordInputTap" maxlength="20" :password="true" placeholder="请输入确认密码"
					placeholderStyle="color:#999;" type="text" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">短信验证</view>
				<input @input="verifyCodeInputTap" maxlength="6" placeholder="请输入短信验证码" placeholderStyle="color:#999;"
					type="number" />
				<button @tap="verifyTap" class="cu-btn bg-green shadow">{{ countDown }}</button>
			</view>
			<view v-if="appPlatform==30" class="cu-form-group margin-top" style="justify-content: space-evenly;">
				<switch  @change="agreementTap" class='red text-df' :class="isAgreement ? 'checked':''" :checked="isAgreement ? true:false" color="#e54d42"></switch>
				<view style="margin-left: 0;">
					<text>我已阅读并同意</text>
					<text class="text-bold"><a href="https://admin.5b1.cn/index/agreement/index" target="_blank">用户协议</a></text>、
					<text class="text-bold"><a href="https://admin.5b1.cn/index/agreement/privacy" target="_blank">隐私政策</a></text>
				</view>
			</view>
			<view class="padding flex flex-direction">
				<button @tap="registerTap" class="cu-btn bg-blue lg">注册</button>
			</view>
		</view>
	</view>
</template>
<style src="./register.css"></style>
<script src="./register.js"></script>