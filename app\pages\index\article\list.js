let app = getApp();
let that = null;
let service = app.globalData.service;
export default {
	data() {
		return {
			isLoad: false,
			page: 1,
			list: [],
			cateList: [],
			cateId: 0,
			cateIndex: 0,
			appIsAudit: false,
			screenHeight: 0,
			isPageFinish: false
		};
	},
	async onLoad(options) {
		that = this;
		that.appIsAudit = app.globalData.checkAppIsAudit();
		that.screenHeight = app.globalData.screenHeight;
	 	await that.getCate();
		that.getList();
	},
	onShow(options) {
		
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		upper() {
			that.page++;
			that.getList();
		},
		async getCate() {
			let response = await service.getArticleCategoryList()
			that.cateList = response.data;
			if(that.cateList.length!=0){
				that.cateId = that.cateList['0'].id;
			}
		},
		async getList() {
			if (that.isPageFinish) {
				return;
			}
			let req = {
				page: that.page,
				cate_id: that.cateId
			};
			let response = await service.getArticleList(req);
			if (response.data.length == 0) {
				that.isPageFinish = true;
			}
			that.list = that.list.concat(response.data);
			that.isLoad = true;
		},
		cateTap(index) {
			console.log(that.cateList[index]);
			let cateId = that.cateList[index].id;
			that.cateId = cateId;
			that.cateIndex = index;
			that.page = 1;
			that.list = [];
			that.isPageFinish = false;
			that.getList();
		},
		detailTap(id) {
			uni.navigateTo({
				url: `./detail?id=${id}`
			});
		},
	}
};