<block wx:if="{{isLoad}}"><view><back vue-id="2784e538-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="{{!user.email?'绑定邮箱':'解绑邮箱'}}" bind:__l="__l"></back><block wx:if="{{!checkAppIsAudit&&isLoad}}"><view><view class="cu-form-group"><view class="title">邮箱</view><input maxlength="32" placeholder="请输入邮箱地址" placeholderStyle="color:#999;" type="text" disabled="{{user.email?true:false}}" data-event-opts="{{[['input',[['emailInputTap',['$event']]]]]}}" value="{{user.email?user.email:''}}" bindinput="__e"/></view><view class="cu-form-group"><view class="title">验证</view><input maxlength="8" placeholder="请输入验证码" placeholderStyle="color:#999;" type="number" data-event-opts="{{[['input',[['inputVerifyCodeTap',['$event']]]]]}}" bindinput="__e"/><button data-event-opts="{{[['tap',[['sendVerifyCodeTap',['$event']]]]]}}" class="cu-btn bg-green shadow" bindtap="__e">{{countDown}}</button></view><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['bindMailTap',['$event']]]]]}}" class="cu-btn bg-blue lg" bindtap="__e">{{"确定"+(!user.email?'绑定':'解绑')}}</button></view><view class="cu-form-group bg-white solid-bottom margin-bottom"><view class="action text-sm"><text class="cuIcon-notice text-blue"></text>：未收到邮件请尝试在邮件垃圾箱中查找</view></view></view></block><adfootbanner vue-id="2784e538-2" unitId="adunit-e9f553c403a978f6" bind:__l="__l"></adfootbanner></view></block>