let app = getApp();
let that = null;
export default {
	data() {
		return {
			isLoad: false,
			listData: [],
			course_id: 0,
			course: {
				name: '',
				code: '',
				count: ''
			}
		};
	},
	onLoad(options) {
		that = this;
		const {
			course_id = 0
		} = options || {};
		that.course_id = course_id;
	},
	onShow() {
		that.getError();
	},
	methods: {
		getError(e) {
			app.globalData.server
				.postRequest('user/error_question', {
					course_id: that.course_id
				})
				.then(function(t) {
					that.isLoad = true;
					that.listData = t.data;
				})
				.catch(function(a) {
					app.showToast('获取错题列表失败');
				});
		},

		courseTap: function(options) {
			let item = options.currentTarget.dataset.item;
			let url = '../topic/topic?mainType=3' + '&title=【错题】' + item.name + '&id=' + item.course_id;
			uni.navigateTo({
				url: url
			});
		}
	}
}; 