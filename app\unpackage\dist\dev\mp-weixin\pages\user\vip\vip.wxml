<block wx:if="{{!appIsAudit&&isLoad}}"><view class="vip-container"><back vue-id="0c7182b4-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" showBack="{{true}}" showTitle="{{true}}" title="会员充值" bind:__l="__l"></back><view class="content-container"><view class="section profession-section"><view class="section-title"><text class="icon cuIcon-profile"></text><text>您选择的专业</text></view><view data-event-opts="{{[['tap',[['toCourseTap',['$event']]]]]}}" class="profession-card" bindtap="__e"><view class="profession-name">{{chargeInfo.professionName}}</view><view class="profession-action"><text>查看可用课程</text><text class="cuIcon-right"></text></view></view></view><view class="section package-section"><view class="section-title"><text class="icon cuIcon-vip"></text><text>选择会员套餐</text></view><view class="package-list"><block wx:for="{{$root.l0}}" wx:for-item="vip" wx:for-index="index" wx:key="index"><view class="{{['package-item',(vipComboId==vip.$orig.id)?'active':'']}}" data-index="{{index}}" data-event-opts="{{[['tap',[['onPriceClick',['$event']]]]]}}" bindtap="__e"><view class="package-price"><text class="price-symbol">¥</text><text class="price-value">{{vip.$orig.sale_price}}</text></view><view class="package-name">{{vip.$orig.name}}</view><view class="package-bonus"><text class="bonus-tag">赠送</text><text>{{vip.m0+"积分"}}</text></view></view></block></view></view><view class="action-section"><button data-event-opts="{{[['tap',[['onChargeCommit',['$event']]]]]}}" class="charge-btn" bindtap="__e">立即充值</button></view><view class="section notice-section"><view class="section-title"><text class="icon cuIcon-info"></text><text>充值说明</text></view><view class="notice-list"><block wx:for="{{chargeInfo.vipChargeNoticeList}}" wx:for-item="notice" wx:for-index="index" wx:key="index"><view class="notice-item"><text class="notice-num">{{index+1}}</text><text class="notice-text">{{notice}}</text></view></block></view></view></view></view></block>