<block wx:if="{{isLoad}}"><view><back vue-id="47ae497e-1" showBackText="{{false}}" customClass="bg-gradual-red text-white" title="微信群" bind:__l="__l"></back><block wx:if="{{!checkAppIsAudit}}"><view><view class="cu-form-group"><view class="title">来源群</view><input maxlength="32" type="text" disabled="true" value="{{wxFromData.iv?wxFromData.iv:''}}"/></view><view class="cu-form-group"><view class="title">加密串</view><input maxlength="32" disabled="true" value="{{wxFromData.encryptedData?wxFromData.encryptedData:''}}"/></view><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['submitTap',['$event']]]]]}}" class="cu-btn bg-red lg" bindtap="__e">确认领取</button></view><view class="cu-bar bg-white solid-bottom"><view class="action"><text class="cuIcon-questionfill text-gray"></text>常见问题</view></view><view class="cu-list menu"><view class="cu-item"><view class="content"><text class="cuIcon-newfill text-grey"></text><text class="text-grey">领取失败？检查是否从官方群进入</text></view></view><view class="cu-item"><view class="content"><text class="cuIcon-newfill text-grey"></text><text class="text-grey">账号封号？是否存在作弊领取积分行为</text></view></view></view></view></block><adfootbanner vue-id="47ae497e-2" unitId="adunit-e9f553c403a978f6" bind:__l="__l"></adfootbanner></view></block>