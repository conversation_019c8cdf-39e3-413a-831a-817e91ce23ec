.page-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f8f8f8;
}
.content-container {
    flex: 1;
    padding: 20rpx;
    margin-bottom: 120rpx;
}
.course-list {
    padding: 10rpx;
}
.course-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}
.course-card:active {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
    background-color: #f9f9f9;
}
.course-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}
.course-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 10rpx;
    line-height: 1.4;
}
.course-expire {
    font-size: 24rpx;
    color: #8a8a8a;
    line-height: 1.4;
}
.course-arrow {
    color: #c8c8cc;
    font-size: 36rpx;
    padding-left: 20rpx;
}
.course-state {
    font-size: 18rpx;
    color: white;
    padding: 7rpx 9rpx;
    border-radius: 10rpx;
}
