{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/match-media/match-media.vue?0c45", "webpack:///D:/桌面/thinker/app/components/match-media/match-media.vue?b114", "webpack:///D:/桌面/thinker/app/components/match-media/match-media.vue?baa1", "webpack:///D:/桌面/thinker/app/components/match-media/match-media.vue?3622", "uni-app:///components/match-media/match-media.vue", "webpack:///D:/桌面/thinker/app/components/match-media/match-media.vue?0669", "webpack:///D:/桌面/thinker/app/components/match-media/match-media.vue?64b4"], "names": ["name", "props", "query", "type", "required", "breakpoint", "validator", "data", "isMatched", "mediaQueryList", "computed", "mediaQuery", "xs", "sm", "md", "lg", "xl", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "initMediaQuery", "observer", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getMaxWidth", "checkScreenSize", "uni", "success", "handleMediaChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,6pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;eCOnrB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAG;QAAA;MAAA;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACAC;MAUA;MACA;QACA;QACAC;UACAC;UACAC;QACA;MACA;QACA;QACA;MACA;IAOA;IAEAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MAAA;MACAC;QACAC;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEAC;MAMA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrHA;AAAA;AAAA;AAAA;AAAg+B,CAAgB,07BAAG,EAAC,C;;;;;;;;;;;ACAp/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/match-media/match-media.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./match-media.vue?vue&type=template&id=4235383e&scoped=true&\"\nvar renderjs\nimport script from \"./match-media.vue?vue&type=script&lang=js&\"\nexport * from \"./match-media.vue?vue&type=script&lang=js&\"\nimport style0 from \"./match-media.vue?vue&type=style&index=0&id=4235383e&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4235383e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/match-media/match-media.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./match-media.vue?vue&type=template&id=4235383e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./match-media.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./match-media.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-if=\"isMatched\" class=\"match-media\">\n    <slot></slot>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'match-media',\n  props: {\n    // 媒体查询条件\n    query: {\n      type: String,\n      required: true\n    },\n    // 预设的断点\n    breakpoint: {\n      type: String,\n      validator: value => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)\n    }\n  },\n  data() {\n    return {\n      isMatched: false,\n      mediaQueryList: null\n    };\n  },\n  computed: {\n    mediaQuery() {\n      if (this.breakpoint) {\n        const breakpoints = {\n          xs: '(max-width: 576px)',\n          sm: '(min-width: 577px) and (max-width: 768px)', \n          md: '(min-width: 769px) and (max-width: 1024px)',\n          lg: '(min-width: 1025px) and (max-width: 1440px)',\n          xl: '(min-width: 1441px)'\n        };\n        return breakpoints[this.breakpoint];\n      }\n      return this.query;\n    }\n  },\n  mounted() {\n    this.initMediaQuery();\n  },\n  beforeDestroy() {\n    if (this.mediaQueryList && this.mediaQueryList.removeListener) {\n      this.mediaQueryList.removeListener(this.handleMediaChange);\n    }\n  },\n  methods: {\n    initMediaQuery() {\n      // #ifdef H5\n      if (window.matchMedia) {\n        this.mediaQueryList = window.matchMedia(this.mediaQuery);\n        this.isMatched = this.mediaQueryList.matches;\n        this.mediaQueryList.addListener(this.handleMediaChange);\n      }\n      // #endif\n      \n      // #ifdef MP-WEIXIN\n      // 微信小程序使用 uni.createMediaQueryObserver\n      if (uni.createMediaQueryObserver) {\n        const observer = uni.createMediaQueryObserver(this);\n        observer.observe({\n          minWidth: this.getMinWidth(),\n          maxWidth: this.getMaxWidth()\n        }, this.handleMediaChange);\n      } else {\n        // 降级方案：使用系统信息判断\n        this.checkScreenSize();\n      }\n      // #endif\n      \n      // #ifndef H5 || MP-WEIXIN\n      // 其他平台降级方案\n      this.checkScreenSize();\n      // #endif\n    },\n    \n    getMinWidth() {\n      if (this.breakpoint === 'sm') return 577;\n      if (this.breakpoint === 'md') return 769;\n      if (this.breakpoint === 'lg') return 1025;\n      if (this.breakpoint === 'xl') return 1441;\n      return 0;\n    },\n    \n    getMaxWidth() {\n      if (this.breakpoint === 'xs') return 576;\n      if (this.breakpoint === 'sm') return 768;\n      if (this.breakpoint === 'md') return 1024;\n      if (this.breakpoint === 'lg') return 1440;\n      return 9999;\n    },\n    \n    checkScreenSize() {\n      uni.getSystemInfo({\n        success: (res) => {\n          const width = res.windowWidth;\n          const minWidth = this.getMinWidth();\n          const maxWidth = this.getMaxWidth();\n          this.isMatched = width >= minWidth && width <= maxWidth;\n        }\n      });\n    },\n    \n    handleMediaChange(matches) {\n      // #ifdef H5\n      this.isMatched = matches.matches;\n      // #endif\n      \n      // #ifdef MP-WEIXIN\n      this.isMatched = matches;\n      // #endif\n    }\n  }\n}\n</script>\n\n<style scoped>\n.match-media {\n  width: 100%;\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./match-media.vue?vue&type=style&index=0&id=4235383e&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./match-media.vue?vue&type=style&index=0&id=4235383e&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753559962993\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}