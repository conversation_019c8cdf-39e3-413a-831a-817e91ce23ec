{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/user/login/bindmp.vue?b8e9", "webpack:///D:/桌面/thinker/app/pages/user/login/bindmp.vue?acd8", "webpack:///D:/桌面/thinker/app/pages/user/login/bindmp.vue?d4aa", "webpack:///D:/桌面/thinker/app/pages/user/login/bindmp.vue?e6f1", "uni-app:///pages/user/login/bindmp.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "app", "cache", "data", "configType", "pageSetData", "isLoad", "verifyCode", "user", "showRuleModal", "onLoad", "onShow", "that", "methods", "getInfo", "service", "showRuleTap", "inputVerifyCodeTap", "bindVerifyTap", "code", "uni", "delta", "bindOpenMp", "urls"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACgB;;;AAGrE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAyqB,CAAgB,wpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkC7rB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;EACAC;EACAC;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACAC;IACA;IACAA;IACAA;IACAA;IACAA;EACA;EACAC;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAI;MACAJ;IACA;IACAK;MACAL;IACA;IACAM;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAX;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAN;gBAAA;cAAA;gBAAA;gBAAA,OAGA;kBACAkB;gBACA;cAAA;gBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACAF;QACAG;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/user/login/bindmp.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/login/bindmp.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./bindmp.vue?vue&type=template&id=b98770be&\"\nvar renderjs\nimport script from \"./bindmp.vue?vue&type=script&lang=js&\"\nexport * from \"./bindmp.vue?vue&type=script&lang=js&\"\nimport style0 from \"./bindemail.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/login/bindmp.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bindmp.vue?vue&type=template&id=b98770be&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n    adfootbanner: function () {\n      return import(\n        /* webpackChunkName: \"components/adfootbanner/adfootbanner\" */ \"@/components/adfootbanner/adfootbanner.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bindmp.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bindmp.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"isLoad\">\r\n\t\t<back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"绑定公众号\"></back>\r\n\t\t<view>\r\n\t\t\t<view class=\"cu-form-group margin-top\">\r\n\t\t\t\t<view class=\"title\">验证码</view>\r\n\t\t\t\t<input @input=\"inputVerifyCodeTap\" maxlength=\"32\" placeholder=\"请输入公众号验证码\" placeholderStyle=\"color:#999;\"\r\n\t\t\t\t\ttype=\"text\" :value=\"user.mp_openid ? user.mp_openid : ''\"\r\n\t\t\t\t\t:disabled=\"user.mp_openid ? true : false\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"padding\">\r\n\t\t\t\t<button class=\"cu-btn block bg-blue margin-tb-sm lg\" @tap=\"bindVerifyTap\">确定绑定</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cu-bar bg-white solid-bottom\" style=\"\">\r\n\t\t\t\t<view class=\"action\">\r\n\t\t\t\t\t<text class=\"cuIcon-title text-red\"></text>\r\n\t\t\t\t\t绑定指南\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action\">\r\n\t\t\t\t\t<button class=\"cu-btn bg-blue shadow\" @tap=\"bindOpenMp\"><text>显示公众号二维码</text></button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cu-list menu\">\r\n\t\t\t\t<view class=\"cu-item\" v-for=\"(value, index) in pageSetData.rule\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t<text class=\"text-grey\">{{value}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<adfootbanner></adfootbanner>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport {\r\n\t\tget,\r\n\t\tpost,\r\n\t} from \"@/common/js/http.js\";\r\n\tlet that = null,\r\n\t\tapp = getApp(),\r\n\t\tcache = app.globalData.config.storage;\r\n\tlet service = app.globalData.service;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tconfigType: 'bind_mp_page_set',\r\n\t\t\t\tpageSetData: null,\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\tverifyCode: '',\r\n\t\t\t\tuser: [],\r\n\t\t\t\tshowRuleModal: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {},\r\n\t\tonShow() {\r\n\t\t\tthat = this;\r\n\t\t\tlet info = cache.getUserInfoData();\r\n\t\t\tthat.isLoad = true;\r\n\t\t\tthat.user = info;\r\n\t\t\tthat.verifyCode = info.mp_openid;\r\n\t\t\tthat.getInfo();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync getInfo() {\r\n\t\t\t\tthat.pageSetData = await service.getConfig(that.configType);\r\n\t\t\t},\r\n\t\t\tshowRuleTap() {\r\n\t\t\t\tthat.showRuleModal = true;\r\n\t\t\t},\r\n\t\t\tinputVerifyCodeTap(options) {\r\n\t\t\t\tthat.verifyCode = options.detail.value;\r\n\t\t\t},\r\n\t\t\tasync bindVerifyTap(e) {\r\n\t\t\t\tlet verifyCode = that.verifyCode;\r\n\t\t\t\tif (!verifyCode) {\r\n\t\t\t\t\tapp.showToast('验证码不得为空');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tawait post('user/bindMp', {\r\n\t\t\t\t\tcode: verifyCode\r\n\t\t\t\t})\r\n\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\tdelta: 1\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tbindOpenMp(e) {\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\turls: [that.pageSetData.mp_qrcode_url]\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style src=\"./bindemail.css\"></style>"], "sourceRoot": ""}