<template>
	<view class="transfer-container">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="赠送会员"></back>
		<view v-if="isLoad" class="transfer-content">
			<!-- 第一步：填写会员编号 -->
			<view class="transfer-card">
				<view class="card-header">
					<view class="step-badge">1</view>
					<text class="step-title">填写会员编号</text>
				</view>
				<view class="card-content">
					<view class="input-container">
						<input 
							@input="uidInputTap" 
							maxlength="32" 
							placeholder="请填写对方会员编号" 
							placeholderStyle="color:#999;"
							type="text" 
							value="" 
							class="ios-input"
						/>
					</view>
				</view>
			</view>
			
			<!-- 第二步：选择要赠送的会员 -->
			<view class="transfer-card">
				<view class="card-header">
					<view class="step-badge">2</view>
					<text class="step-title">选择要赠送的会员</text>
				</view>
				<view class="card-content">
					<radio-group class="vip-radio-group" @change="vipListChangeTap">
						<view class="vip-list">
							<view 
								class="vip-item" 
								:class="{active: radio=='radio' + index}"
								v-for="(item, index) in info.vip_list" 
								:key="index"
							>
								<label class="vip-label">
									<view class="vip-info">
										<text class="vip-name">{{ item.name }}</text>
										<text class="vip-expire">{{ item.expire_name }}到期</text>
									</view>
									<radio 
										class="ios-radio" 
										:class="radio=='radio' + index?'checked':''"
										:checked="radio=='radio' + index?true:false" 
										:value="item.vid"
									></radio>
								</label>
							</view>
						</view>
					</radio-group>
				</view>
			</view>
			
			<!-- 确认按钮 -->
			<view class="action-container">
				<button @tap="submitTap" class="ios-button">确认赠送</button>
			</view>
			
			<!-- 操作说明 -->
			<view class="transfer-card notice-card">
				<view class="card-header">
					<view class="notice-icon">
						<text class="cuIcon-info"></text>
					</view>
					<text class="step-title">操作说明</text>
				</view>
				<view class="card-content">
					<view class="notice-list">
						<view class="notice-item" v-for="(notice, index) in info.notice_list" :key="index">
							<text class="notice-text">{{ notice }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	let app = getApp();
	let that = null;
	export default {
		data() {
			return {
				info: {},
				isLoad: false,
				selectVipId: 0,
				selectUserId: 0,
				showUserInfo: false
			};
		},
		onLoad(t) {
			that = this;
			app.globalData.checkAppIsAuditAndRedirect();
		},
		onShow() {
			that.getInfo();
		},
		methods: {
			async getInfo() {
				let res = await app.globalData.service.vipTransferInfo();
				that.info = res.data
				that.isLoad = true;
			},
			uidInputTap(options) {
				that.selectUserId = options.detail.value;
			},
			vipListChangeTap(options) {
				that.selectVipId = options.detail.value;
			},
			submitTap() {
				if (!that.selectUserId) {
					app.showToast('请先填写用户编号');
					return;
				}
				if (!that.selectVipId) {
					app.showToast('请先选择要赠送的会员');
					return;
				}
				uni.showModal({
					content: '确认赠送？',
					success: function(res) {
						if (res.confirm) {
							uni.login({
								async success(res) {
									console.log(res)
									let code = res.code
									if (code) {
										let data = {
											uid: that.selectUserId,
											vid: that.selectVipId,
											code: code
										}
										let res = await app.globalData.service.vipTransfer(data);
										app.showToast('操作成功');
										setTimeout(() => {
											that.getInfo();
										}, 1500);
									} else {
										app.showToast('获取用户身份失败');
									}
								}
							})
						}
					}
				});
			}
		}
	};
</script>
<style src="./transfer.css"></style>
