<block wx:if="{{isLoad&&!appIsAudit}}"><view class="list-container"><back vue-id="71b4978e-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" showBack="{{true}}" showTitle="{{true}}" title="最新资讯" bind:__l="__l"></back><view><scroll-view class="bg-white nav margin-bottom-xs" scroll-x="{{true}}"><view class="flex text-center"><block wx:for="{{cateList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['cateTap',[index]]]]]}}" class="{{['cu-item','flex-sub','',cateIndex==index?'text-blue cur text-bold':'']}}" style="font-size:30rpx;" bindtap="__e">{{''+item.name+''}}</view></block></view></scroll-view><scroll-view class="scroll-Y" style="{{('height:'+(screenHeight-200)+'px')}}" scroll-y="true" data-event-opts="{{[['scrolltolower',[['upper',['$event']]]]]}}" bindscrolltolower="__e"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['detailTap',['$0'],[[['list','',index,'id']]]]]]]}}" class="cu-card article no-card solid-bottom margin-xs-top" bindtap="__e"><view class="cu-item shadow"><view class="content"><view><image class="radius" style="height:120rpx;width:120rpx;" src="{{item.thumb}}" mode="aspectFit"></image></view><view class="desc" style="justify-content:space-around;margin-left:10rpx;"><view class="text-black text-df">{{item.title}}</view><view class="flex justify-between"><view class="text-gray text-sm">{{item.cate_name+" · "+item.create_date}}</view><view class="text-gray text-sm padding-right text-shadow">{{item.page_view+' 阅读'}}</view></view></view></view></view></view></block><block wx:if="{{$root.g0}}"><adfootbanner vue-id="71b4978e-2" bind:__l="__l"></adfootbanner></block><block wx:if="{{$root.g1}}"><empty vue-id="71b4978e-3" bind:__l="__l"></empty></block></scroll-view></view></view></block>