<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<view v-if="showAd" style="bottom:0;padding: 0;width: 100%;" :class="className">
			<ad :unit-id="unitId" ad-intervals="30"></ad>
		</view>
		<!-- #endif -->
	</view>
</template>
<script>
	const app = getApp();
	export default {
		name: 'adbanner',
		data() {
			return {
				showAd: false
			};
		},
		props: {
			unitId: {
				type: String,
				default: 'adunit-b8d5a49b6c1c8b07'
			},
			className: {
				type: String,
				default: 'margin-top'
			},
		},
		created() {
			let checkHasVip = app.checkHasVip();
			if (!checkHasVip) {
				this.showAd = true;
			}
		}
	};
</script>
