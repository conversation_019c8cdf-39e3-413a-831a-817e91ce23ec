<template>
	<view v-if="!appIsAudit && load">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" :title="info.wx_group_title"></back>
		<view class="cu-bar bg-white solid-bottom">
			<view class="action">
				<text class="cuIcon-weixin text-blue"></text>{{info.wx_group_name}}
			</view>
		</view>
		<view style="">
			<view class="cu-card case" style="">
				<view class="cu-item shadow">
					<view class="image">
						<image :src="info.wx_group_image" show-menu-by-longpress="true" mode="aspectFit">
						</image>
						<view class="cu-bar bg-shadeBottom" style="justify-content: center;">
							<text class="text-cut">{{info.wx_group_scan_title}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="cu-bar bg-white solid-bottom">
			<view class="action">
				<text class="cuIcon-markfill text-blue"></text>{{info.wx_group_rule_title}}
			</view>
		</view>
		<view class="cu-list menu">
			<view class="cu-item" v-for="(value, index) in info.wx_group_rule_list" :key="index">
				<view class="content">
					<text class="text-df">{{value}}</text>
				</view>
			</view>
		</view>
	</view>
</template>
<style src="./group.css"></style>
<script src="./group.js"></script>