<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="修改密码"></back>
		<view>
			<view class="cu-form-group margin-top">
				<view class="title">输入密码</view>
				<input @input="passwordInputTap1" maxlength="32" placeholder="请输入登录密码" type="password" />
			</view>
			<view class="cu-form-group">
				<view class="title">确认密码</view>
				<input @input="passwordInputTap2" maxlength="32" placeholder="请输入确认密码" type="password" />
			</view>
			<view class="padding flex flex-direction">
				<button @tap="setPasswordTap" class="cu-btn bg-blue lg">提交密码</button>
			</view>
			<view class="cu-form-group bg-white solid-bottom margin-bottom">
				<view class="action">
					<text class="cuIcon-title text-blue"></text>
					提示：设置密码后使用手机号|邮箱+密码登录
				</view>
			</view>
		</view>
		<adfootbanner unitId="adunit-1fb0622d11b3c262"></adfootbanner>
	</view>
</template>
<style src="./password.css"></style>
<script src="./password.js"></script>