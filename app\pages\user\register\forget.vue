<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="找回密码"></back>
		<view v-if="!checkAppIsAudit">
			<safeCheck :sid.sync="username" :scene="40" :status.sync="showConfirm" confirmText="确认输入"
				confirmTextClass="cuIcon-roundcheck" @complete="safeCheckCompleteTap"></safeCheck>
			<view class="cu-form-group margin-top">
				<view class="title">手机号码</view>
				<input @input="usernameInputTap" maxlength="32" placeholder="请输入手机号码" placeholderStyle="color:#999;"
					type="text" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">新的密码</view>
				<input @input="passwordInputTap" maxlength="32" :password="true" placeholder="请输入新的密码"
					placeholderStyle="color:#999;" type="text" />
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">短信验证</view>
				<input @input="verifyCodeInputTap" maxlength="8" placeholder="请输入短信验证码" placeholderStyle="color:#999;"
					type="number" />
				<button @tap="sendVerifyTap" class="cu-btn bg-green shadow">{{ countDown }}</button>
			</view>
			<view class="padding flex flex-direction">
				<button @tap="submitTap" class="cu-btn bg-blue lg">确定</button>
			</view>
		</view>
	</view>
</template>
<style src="./forget.css"></style>
<script src="./forget.js"></script>