<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="选择题库"></back>
		<view class="flex flex-direction" style="min-height: 100vh;" v-if="isLoad">
			<view class="flex-sub" style="margin-bottom: 270rpx;">
				<view
					:style="'padding-top:0.1rpx; padding-bottom:' + (isAllScreen ? 68 : 20) + 'rpx;padding-left:30rpx;padding-right:30rpx;'">
					<block v-for="(item, index) in listData" :key="index">
						<view class="group-head">
							<text 
								:class="['cuIcon-' + (index == 0 ? 'roundcheckfill' : 'roundadd'), 'group-head-icon']">
							</text>
							<text>{{ item.group }}</text>
						</view>
						<view @tap="courseTap" class="course-layout" :data-child="idx" :data-index="index"
							v-for="(course, idx) in item.courses" :key="idx">
							<view style="flex: 1">
								<view class="course-name">{{ course.name }}</view>
								<courselabel :code="course.code" :courseApply="index == 0 ? '已报考' : '未报考'">
								</courselabel>
							</view>
							<view class="">							<text @tap.stop.prevent="editTap" :data-item="course" 
								:class="['cuIcon-' + (course.joined == 1 ? 'pulldown' : 'pullup'), 'course-icon']">
							</text>
							</view>

						</view>
					</block>
					<view class="wbk_hint">点击课程右边向上箭头添加报考</view>
				</view>
			</view>
			<adfootbanner></adfootbanner>
		</view>
	</view>
</template>

<script src="./course.js"></script>
<style src="./course.css"></style>