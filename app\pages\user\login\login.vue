<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="会员登录"></back>
		<view>
			<scroll-view scroll-x class="bg-white nav text-center ">
				<view class="cu-item" :class="index==selectModelIndex?'text-blue cur':''"
					v-for="(item,index) in selectModel" :key="index" @tap="selectModelTap" :data-id="index"
					v-if="item.show">
					<text :class="item.icon"></text> {{item.name}}
				</view>
			</scroll-view>
			<view v-if="selectModelIndex == 1">
				<view class="cu-form-group margin-top">
					<view class="title">账户</view>
					<input @input="usernameInputTap" maxlength="32" placeholder="手机号码|邮箱地址"
						placeholderStyle="color:#999;" type="text" />
				</view>
				<view class="cu-form-group margin-top">
					<view class="title">密码</view>
					<input @input="passwordInputTap" maxlength="20" :password="true" placeholder="账号登录密码"
						placeholderStyle="color:#999;" type="text" />
				</view>
				<view v-if="appPlatform==30" class="cu-form-group margin-top" style="justify-content: space-evenly;">
					<switch @change="agreementTap" class='red text-df' :class="isAgreement ? 'checked':''"
						:checked="isAgreement ? true:false" color="#e54d42"></switch>
					<view style="margin-left: 0;">
						<text>我已阅读并同意</text>
						<text class="text-bold"><a href="https://admin.5b1.cn/index/agreement/index"
								target="_blank">用户协议</a></text>、
						<text class="text-bold"><a href="https://admin.5b1.cn/index/agreement/privacy"
								target="_blank">隐私政策</a></text>
					</view>
				</view>
				<view class="padding flex flex-direction">
					<button @tap="loginTap" class="cu-btn bg-blue lg">登录</button>
				</view>
				<view v-if="!checkAppIsAudit" class="padding flex flex-direction" style="padding-top: 0rpx">
					<view class="flex" style="text-align: center">
						<view class="flex-sub bg-blue padding-sm margin-xs radius" style="margin-left: -3rpx">
							<navigator hoverClass="none" url="../register/register">注册账号</navigator>
						</view>
						<view class="flex-sub bg-blue padding-sm margin-xs radius" style="margin-right: -3rpx">
							<navigator hoverClass="none" url="../register/forget">找回密码</navigator>
						</view>
					</view>
				</view>
			</view>
			<view v-if="selectModelIndex == 0 && login_page_set!=null" class="margin-top">
				<view class="padding-xs flex align-center">
					<view class="flex-sub text-center">
						<view class="text-lg padding">
							<text class="text-black">申请获取以下权限</text>
						</view>
						<view class="padding"><text class="text-grey">获取您的用户身份</text></view>
					</view>
				</view>
				<view class="padding flex flex-direction" v-if="login_page_set.open_mcp_mobile_login!=1">
					<button @tap="wxloginTap" class="cu-btn  bg-blue lg">授权登录</button>
				</view>
				<view class="padding flex flex-direction" v-if="login_page_set.open_mcp_mobile_login==1">
					<button open-type="getPhoneNumber" @getphonenumber="wxPhoneloginTap"
						class="cu-btn  bg-blue lg">手机号快捷登录</button>
				</view>
			</view>
		</view>
	</view>
</template>
<style src="./login.css"></style>
<script src="./login.js"></script>