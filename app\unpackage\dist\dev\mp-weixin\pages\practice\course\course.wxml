<view><back vue-id="35534692-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="选择题库" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view class="flex flex-direction" style="min-height:100vh;"><view class="flex-sub" style="margin-bottom:270rpx;"><view style="{{('padding-top:0.1rpx; padding-bottom:'+(isAllScreen?68:20)+'rpx;padding-left:30rpx;padding-right:30rpx;')}}"><block wx:for="{{listData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="group-head"><text class="{{['cuIcon-'+(index==0?'roundcheckfill':'roundadd'),'group-head-icon']}}"></text><text>{{item.group}}</text></view><block wx:for="{{item.courses}}" wx:for-item="course" wx:for-index="idx" wx:key="idx"><view class="course-layout" data-child="{{idx}}" data-index="{{index}}" data-event-opts="{{[['tap',[['courseTap',['$event']]]]]}}" bindtap="__e"><view style="flex:1;"><view class="course-name">{{course.name}}</view><courselabel vue-id="{{'35534692-2-'+index+'-'+idx}}" code="{{course.code}}" courseApply="{{index==0?'已报考':'未报考'}}" bind:__l="__l"></courselabel></view><view><text class="{{['cuIcon-'+(course.joined==1?'pulldown':'pullup'),'course-icon']}}" data-item="{{course}}" data-event-opts="{{[['tap',[['editTap',['$event']]]]]}}" catchtap="__e"></text></view></view></block></block></block><view class="wbk_hint">点击课程右边向上箭头添加报考</view></view></view><adfootbanner vue-id="35534692-3" bind:__l="__l"></adfootbanner></view></block></view>