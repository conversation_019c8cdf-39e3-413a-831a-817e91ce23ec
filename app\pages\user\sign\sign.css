.label {
    font-size: 31rpx;
    font-weight: bold;
    color: #000;
    padding: 30rpx;
}

.layout-section {
    margin: 0 30rpx 0rpx 30rpx;
    background: white;
    border-radius: 30rpx;
}

.btn-share {
    padding: 0;
    background-color: transparent;
    border-color: transparent;
    border-radius: 0rpx;
    margin: 0rpx;
	display: inline;
}

.headinfo {
    background: #24b2f4;
    border: none;
    position: relative;
    height: auto;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
}

.headinfo .child {
    width: 30%;
    height: auto;
    text-align: center;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    margin-top: 20rpx;
    padding: 0;
}

.headinfo .child .title {
    padding-top: 20rpx;
    font-size: 35rpx;
    color: #fff;
    text-align: center;
    font-weight: bold;
}

.headinfo .child .num {
    font-size: 30rpx;
    box-sizing: border-box;
    margin-top: 30rpx;
    padding: 0;
    color: #fff;
    text-align: center;
    white-space: nowrap;
}

.top-layout {
    margin: 0;
    padding: 15rpx;
    border-radius: 30rpx;
    background: white;
    font-size: 28rpx;
}

.top-layout textarea {
    height: 100rpx;
}

.activity_head {
    padding: 0;
    margin: 0 35rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

.activity_head .tag {
    box-sizing: border-box;
    width: 6rpx;
    height: 30rpx;
    background-color: rgb(255, 44, 60);
    margin-right: 20rpx;
}

.item-layout {
    display: flex;
    align-items: center;
    flex-direction: row;
    padding: 30rpx;
    margin: 30rpx;
    border-radius: 20rpx;
    background: white;
}

.item-name {
    font-size: 32rpx;
    color: #333;
}

.rules {
    margin: 30rpx 30rpx 0 30rpx;
    color: red;
}