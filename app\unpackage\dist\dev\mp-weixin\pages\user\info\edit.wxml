<view><back vue-id="0d41a374-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="编辑资料" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view class="content-wrapper"><view class="avatar-section"><button class="avatar-button" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['avatarInputTap',['$event']]]]]}}" bindchooseavatar="__e"><view class="cu-avatar round xl" style="{{('background-image:url('+avatarInputVal+');')}}"></view><view class="avatar-hint">点击更换头像</view></button></view><view class="form-section"><view class="cu-form-group radius shadow-warp"><view class="title"><text class="cuIcon-my text-blue margin-right-xs"></text>昵称</view><input type="nickname" maxlength="20" placeholder="填入要修改的昵称" data-event-opts="{{[['input',[['nicknameInputTap',['$event']]]]]}}" value="{{nicknameInputVal}}" bindinput="__e"/></view><view class="cu-form-group radius shadow-warp"><view class="title"><text class="cuIcon-mobile text-blue margin-right-xs"></text>手机</view><block wx:if="{{user.mobile}}"><input disabled="{{true}}" value="{{user.mobile}}"/></block><block wx:else><button class="cu-btn bg-blue sm radius" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['wxPhoneBindTap',['$event']]]]]}}" bindgetphonenumber="__e">绑定手机号</button></block></view></view><view class="button-section"><button data-event-opts="{{[['tap',[['submitTap',['$event']]]]]}}" class="cu-btn bg-gradual-blue lg radius shadow-blur" bindtap="__e">保存资料</button><button data-event-opts="{{[['tap',[['cancelTap',['$event']]]]]}}" class="cu-btn bg-grey lg radius shadow-blur" bindtap="__e">取消修改</button></view><adfootbanner vue-id="0d41a374-2" bind:__l="__l"></adfootbanner></view></block><canvas style="position:absolute;left:-9999px;top:-9999px;" canvas-id="compressCanvas" id="compressCanvas"></canvas></view>