
.vip-container {
	background-color: #f5f7fa;
	min-height: 100vh;
}
.content-container {
	padding: 30rpx;
}
.section {
	margin-bottom: 30rpx;
	border-radius: 12rpx;
	background-color: #ffffff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}
.section-title {
	padding: 24rpx;
	border-bottom: 1rpx solid #eee;
	display: flex;
	align-items: center;
}
.section-title .icon {
	color: #0081ff;
	margin-right: 16rpx;
	font-size: 36rpx;
}
.section-title text {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

/* 专业选择区域样式 */
.profession-card {
	padding: 30rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.profession-name {
	font-size: 32rpx;
	color: #0081ff;
	font-weight: bold;
}
.profession-action {
	display: flex;
	align-items: center;
	color: #ff6b6b;
	font-size: 26rpx;
}
.profession-action .cuIcon-right {
	margin-left: 8rpx;
}

/* 会员套餐选择区域样式 */
.package-list {
	display: flex;
	flex-wrap: wrap;
	padding: 20rpx;
	justify-content: space-between;
}
.package-item {
	width: 30%;
	padding: 24rpx 0;
	margin-bottom: 20rpx;
	border-radius: 12rpx;
	background-color: #f9fafc;
	border: 2rpx solid #eaeef5;
	display: flex;
	flex-direction: column;
	align-items: center;
	transition: all 0.3s ease;
}
.package-item.active {
	background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
	border: 2rpx solid #0081ff;
	box-shadow: 0 4rpx 16rpx rgba(0, 129, 255, 0.15);
	-webkit-transform: translateY(-4rpx);
	        transform: translateY(-4rpx);
}
.package-price {
	display: flex;
	align-items: baseline;
	margin-bottom: 12rpx;
}
.price-symbol {
	color: #ff6b6b;
	font-size: 28rpx;
	margin-right: 4rpx;
}
.price-value {
	color: #ff6b6b;
	font-size: 40rpx;
	font-weight: bold;
}
.package-name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	font-weight: 500;
}
.package-bonus {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #0081ff;
}
.bonus-tag {
	background-color: rgba(0, 129, 255, 0.1);
	color: #0081ff;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	margin-right: 8rpx;
	font-size: 22rpx;
}

/* 充值按钮样式 */
.action-section {
	padding: 20rpx 40rpx 40rpx;
}
.charge-btn {
	background: linear-gradient(90deg, #0081ff, #1cbbb4);
	color: #ffffff;
	font-size: 32rpx;
	font-weight: bold;
	height: 90rpx;
	line-height: 90rpx;
	border-radius: 45rpx;
	box-shadow: 0 10rpx 20rpx rgba(0, 129, 255, 0.2);
	transition: all 0.3s ease;
}
.charge-btn:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
	box-shadow: 0 5rpx 10rpx rgba(0, 129, 255, 0.2);
}

/* 充值说明样式 */
.notice-list {
	padding: 20rpx;
}
.notice-item {
	display: flex;
	margin-bottom: 16rpx;
	padding: 10rpx;
	border-radius: 8rpx;
	background-color: #f9fafc;
}
.notice-num {
	width: 40rpx;
	height: 40rpx;
	background-color: #0081ff;
	color: #fff;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 24rpx;
	margin-right: 16rpx;
	flex-shrink: 0;
}
.notice-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

