
.material-detail-ios.data-v-0054fb44 {
  background: #f7f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.loading-container.data-v-0054fb44 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 80vh;
}
.loading-text.data-v-0054fb44 {
  margin-top: 24rpx;
  color: #8799a3;
  font-size: 28rpx;
}
.main-content.data-v-0054fb44 {
  flex: 1;
  padding: 32rpx 0 120rpx 0;
}
.info-card.data-v-0054fb44 {
  background: #fff;
  border-radius: 24rpx;
  margin: 32rpx 32rpx 0 32rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
}
.info-header.data-v-0054fb44 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.info-title.data-v-0054fb44 {
  font-size: 40rpx;
  font-weight: 700;
  color: #222;
  flex: 1;
}
.info-tags.data-v-0054fb44 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}
.price-tag.data-v-0054fb44 {
  font-size: 28rpx;
  padding: 8rpx 24rpx;
}
.version-tag.data-v-0054fb44 {
  font-size: 22rpx;
  padding: 4rpx 16rpx;
}
.info-desc.data-v-0054fb44 {
  margin: 24rpx 0 0 0;
  color: #666;
  font-size: 28rpx;
  line-height: 1.6;
}
.info-meta.data-v-0054fb44 {
  display: flex;
  gap: 48rpx;
  margin-top: 32rpx;
}
.meta-item.data-v-0054fb44 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #888;
}
.meta-label.data-v-0054fb44 {
  margin-left: 4rpx;
}
.meta-value.data-v-0054fb44 {
  color: #39b54a;
  font-weight: 600;
  margin-left: 8rpx;
}
.preview-section.data-v-0054fb44 {
  margin: 32rpx 32rpx 0 32rpx;
}
.preview-btn.data-v-0054fb44 {
  font-size: 30rpx;
  padding: 24rpx 0;
  font-weight: 500;
}
.desc-card.data-v-0054fb44 {
  background: #fff;
  border-radius: 24rpx;
  margin: 32rpx 32rpx 0 32rpx;
  padding: 32rpx;
}
.desc-title.data-v-0054fb44 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.desc-content.data-v-0054fb44 {
  color: #666;
  font-size: 28rpx;
  line-height: 1.7;
}
.ad-section.data-v-0054fb44 {
  margin: 40rpx 32rpx 0 32rpx;
}
.action-bar.data-v-0054fb44 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 24rpx 0 32rpx 0;
  box-shadow: 0 -4rpx 32rpx rgba(0,0,0,0.04);
  z-index: 99;
}
.action-btn.data-v-0054fb44 {
  flex: 1;
  margin: 0 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  padding: 24rpx 0;
  border-radius: 16rpx;
}
.shadow.data-v-0054fb44 {
  box-shadow: 0 4rpx 32rpx rgba(0,0,0,0.06);
}
.safe-area-inset-bottom.data-v-0054fb44 {
  padding-bottom: env(safe-area-inset-bottom);
}

