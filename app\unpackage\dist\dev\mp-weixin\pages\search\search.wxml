<view><back vue-id="50cad900-1" showBackText="{{false}}" showBackLeft="{{false}}" customClass="bg-gradual-blue text-white" title="搜索" bind:__l="__l"></back><view class="flex flex-direction" style="min-height:100vh;"><view class="flex-sub"><view class="cu-form-group" style="{{(cssData.searchBar)}}"><textarea placeholder="输入搜索关键字，搜课程（可以按照编码搜索），搜题目（支持拍照搜，语音搜）" data-event-opts="{{[['input',[['onInputChange',['$event']]]]]}}" value="{{keyword}}" bindinput="__e"></textarea></view><block wx:if="{{!isAudioing&&!appIsAudit}}"><view style="background-color:white;"><view class="grid col-2 text-center" style="margin:0 10rpx;"><view class="solid-bottom text-lg"><view class="cu-bar btn-group"><button data-event-opts="{{[['tap',[['audioTap',['$event']]]]]}}" class="cu-btn action-button" style="{{(cssData.button)}}" bindtap="__e"><text class="cuIcon-voicefill"></text>语音</button><button data-event-opts="{{[['tap',[['chooseImageTap',['$event']]]]]}}" class="cu-btn action-button" style="margin:0 5rpx;padding:0;" bindtap="__e"><text class="cuIcon-camerafill"></text>拍照</button></view></view><view class="solid-bottom text-lg"><view class="cu-bar btn-group"><button data-event-opts="{{[['tap',[['clearAndPasteTap',['$event']]]]]}}" class="cu-btn action-button" style="margin:0 5rpx;padding:0;" bindtap="__e">清除并粘贴</button><button data-event-opts="{{[['tap',[['clearTap',['$event']]]]]}}" class="cu-btn action-button" style="margin:0 5rpx;padding:0;" bindtap="__e">清除</button></view></view></view></view></block><block wx:if="{{isAudioing}}"><view data-event-opts="{{[['tap',[['audioActionTap',[false]]]]]}}" class="padding bg-white margin-top-xs" bindtap="__e"><view class="cu-progress radius striped active" style="height:60rpx;"><view class="bg-green" style="width:100%;font-size:30rpx;">录音中，点击停止</view></view></view></block><view class="padding-sm"><view class="flex flex-direction"><button class="{{['cu-btn lg bg-gradual-blue shadow ',cssData.button]}}" data-type="1" data-event-opts="{{[['tap',[['onSearchClick',['$event']]]]]}}" bindtap="__e"><text class="cuIcon-search"></text>立即搜索</button></view></view><view class="cu-bar bg-white solid-bottom" style="margin-top:10rpx;"><view class="action"><text class="cuIcon-title text-red"></text>搜索动态</view></view><view class="cu-list menu"><block wx:for="{{recordList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onClickQuestion',['$0'],[[['recordList','',index,'question_id']]]]]]]}}" class="cu-item" bindtap="__e"><view class="action">"><view class="cu-avatar sm round" style="{{('background-image:url('+item.avatar+');')}}"></view></view></view></block><view class="content"><text class="{{['text-orange text-df',cssData.textContent]}}">{{item.nickname}}</text><text class="{{['text-gray text-df',cssData.textContent]}}">搜索</text><text class="{{['text-blue text-df',cssData.textContent]}}">{{item.question_name}}</text></view></view></view><adfootbanner vue-id="50cad900-2" bind:__l="__l"></adfootbanner></view></view>