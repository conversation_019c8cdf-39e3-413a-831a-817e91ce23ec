<view class="{{['search-page',responsiveClass]}}"><back vue-id="50cad900-1" showBackText="{{false}}" showBackLeft="{{false}}" customClass="bg-gradual-blue text-white" title="搜索" bind:__l="__l"></back><responsive-container vue-id="50cad900-2" bind:__l="__l" vue-slots="{{['default']}}"><view class="search-container"><view class="search-input-section"><view class="cu-form-group search-form" style="{{(cssData.searchBar)}}"><textarea class="search-textarea" placeholder="输入搜索关键字，搜课程（可以按照编码搜索），搜题目（支持拍照搜，语音搜）" data-event-opts="{{[['input',[['onInputChange',['$event']]]]]}}" value="{{keyword}}" bindinput="__e"></textarea></view></view><block wx:if="{{!isAudioing&&!appIsAudit}}"><view class="action-buttons-section"><view class="{{['action-buttons-container','grid','grid-xs-2','grid-sm-2','grid-md-4','grid-lg-4','grid-xl-4']}}"><view class="action-group"><button data-event-opts="{{[['tap',[['audioTap',['$event']]]]]}}" class="cu-btn action-button responsive-btn" style="{{(cssData.button)}}" bindtap="__e"><text class="cuIcon-voicefill"></text><text class="btn-text">语音</text></button></view><view class="action-group"><button data-event-opts="{{[['tap',[['chooseImageTap',['$event']]]]]}}" class="cu-btn action-button responsive-btn" bindtap="__e"><text class="cuIcon-camerafill"></text><text class="btn-text">拍照</text></button></view><view class="action-group"><button data-event-opts="{{[['tap',[['clearAndPasteTap',['$event']]]]]}}" class="cu-btn action-button responsive-btn" bindtap="__e"><text class="cuIcon-copy"></text><text class="btn-text d-xs-none d-sm-none">清除并粘贴</text><text class="btn-text d-md-none d-lg-none d-xl-none">粘贴</text></button></view><view class="action-group"><button data-event-opts="{{[['tap',[['clearTap',['$event']]]]]}}" class="cu-btn action-button responsive-btn" bindtap="__e"><text class="cuIcon-deletefill"></text><text class="btn-text">清除</text></button></view></view></view></block><block wx:if="{{isAudioing}}"><view data-event-opts="{{[['tap',[['audioActionTap',[false]]]]]}}" class="voice-recording-section" bindtap="__e"><view class="voice-container"><view class="cu-progress radius striped active voice-progress"><view class="bg-green voice-progress-bar">录音中，点击停止</view></view></view></view></block><view class="search-button-section"><view class="search-button-container"><button class="{{['cu-btn lg bg-gradual-blue shadow search-btn',cssData.button]}}" data-type="1" data-event-opts="{{[['tap',[['onSearchClick',['$event']]]]]}}" bindtap="__e"><text class="cuIcon-search"></text><text class="search-btn-text">立即搜索</text></button></view></view><view class="search-dynamics-section"><view class="dynamics-header"><view class="dynamics-title"><text class="cuIcon-title text-red"></text><text class="title-text">搜索动态</text></view></view><view class="dynamics-grid grid grid-xs-1 grid-sm-1 grid-md-2 grid-lg-2 grid-xl-3"><block wx:for="{{recordList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onClickQuestion',['$0'],[[['recordList','',index,'question_id']]]]]]]}}" class="dynamics-item" bindtap="__e"><view class="dynamics-card"><view class="dynamics-header-info"><view class="dynamics-avatar"><view class="cu-avatar sm round" style="{{('background-image:url('+item.avatar+');')}}"></view></view><view class="dynamics-meta"><text class="{{['dynamics-time text-grey text-df',cssData.textContent]}}">{{index+3+"秒前"}}</text></view></view><view class="dynamics-content"><text class="{{['dynamics-nickname text-orange text-df',cssData.textContent]}}">{{item.nickname}}</text><text class="{{['dynamics-action text-gray text-df',cssData.textContent]}}">搜索</text><text class="{{['dynamics-question text-blue text-df',cssData.textContent]}}">{{item.question_name}}</text></view></view></view></block></view><adfootbanner vue-id="{{('50cad900-3')+','+('50cad900-2')}}" bind:__l="__l"></adfootbanner></view></view></responsive-container></view>