<view class="transfer-container"><back vue-id="7a67024e-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="赠送会员" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view class="transfer-content"><view class="transfer-card"><view class="card-header"><view class="step-badge">1</view><text class="step-title">填写会员编号</text></view><view class="card-content"><view class="input-container"><input class="ios-input" maxlength="32" placeholder="请填写对方会员编号" placeholderStyle="color:#999;" type="text" value="" data-event-opts="{{[['input',[['uidInputTap',['$event']]]]]}}" bindinput="__e"/></view></view></view><view class="transfer-card"><view class="card-header"><view class="step-badge">2</view><text class="step-title">选择要赠送的会员</text></view><view class="card-content"><radio-group data-event-opts="{{[['change',[['vipListChangeTap',['$event']]]]]}}" class="vip-radio-group" bindchange="__e"><view class="vip-list"><block wx:for="{{info.vip_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['vip-item',(radio=='radio'+index)?'active':'']}}"><label class="vip-label"><view class="vip-info"><text class="vip-name">{{item.name}}</text><text class="vip-expire">{{item.expire_name+"到期"}}</text></view><radio class="{{['ios-radio',radio=='radio'+index?'checked':'']}}" checked="{{radio=='radio'+index?true:false}}" value="{{item.vid}}"></radio></label></view></block></view></radio-group></view></view><view class="action-container"><button data-event-opts="{{[['tap',[['submitTap',['$event']]]]]}}" class="ios-button" bindtap="__e">确认赠送</button></view><view class="transfer-card notice-card"><view class="card-header"><view class="notice-icon"><text class="cuIcon-info"></text></view><text class="step-title">操作说明</text></view><view class="card-content"><view class="notice-list"><block wx:for="{{info.notice_list}}" wx:for-item="notice" wx:for-index="index" wx:key="index"><view class="notice-item"><text class="notice-text">{{notice}}</text></view></block></view></view></view></view></block></view>