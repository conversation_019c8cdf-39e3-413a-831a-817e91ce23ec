<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="我的收藏"></back>
		<view class="flex flex-direction" style="min-height: 100vh;">
			<view class="flex-sub" style="margin-bottom: 270rpx;">
				<scroll-view scroll-x class="bg-white nav text-center top">
					<view class="cu-item" :class="index==selectModelIndex?'text-blue cur':''"
						v-for="(item,index) in selectModel" :key="index" @tap="selectModelTap" :data-id="index">
						{{item}}
					</view>
				</scroll-view>
				<view class="margin-top-xs" v-if="isLoad == true">
					<view @tap="courseTap" class="course-layout margin-bottom-xs" :data-item="course"
						v-for="(course, idx) in listData" :key="idx">
						<view class="name-layout">
							<text>{{ course.name }}</text>
							<courselabel :code="course.code" :topicCount="course.count ? '共'+course.count+'题' :''">							</courselabel>
						</view>
						<text class="cuIcon-right"></text>
					</view>
				</view>
				<empty v-if="isLoad == true && listData.length == 0"></empty>
			</view>
			<adfootbanner></adfootbanner>
		</view>
	</view>
</template>

<script src="./coll.js"></script>
<style src="./coll.css"></style>