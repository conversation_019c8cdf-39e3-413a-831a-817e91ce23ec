let app = getApp();
let that = null;
export default {
	data() {
		return {
			id: 0,
			item: [],
			title: '',
			CustomBar: this.CustomBar,
			isLoad: false,
			options: ['A', 'B', 'C', 'D', 'E', 'F'],
			appIsAudit: false,
			currentTopicTypeLabel: '',
		};
	},
	onLoad: function(options) {
		that = this;
		that.id = options.id;
		that.appIsAudit = app.globalData.checkAppIsAudit();
		that.getQuestion();
	},
	onShow: function(options) {
		app.globalData.showShareMenu();
	},
	onShareAppMessage: function() {
		let item = that.item;
		let config = app.globalData.getShareConfig();
		config.title = item.questionAsk;
		config.path = '/pages/practice/question/detail?id=' + item.id;
		console.log(config);
		return config;
	},
	methods: {
		getQuestion: function() {
			app.globalData.server
				.getRequest('question/getInfo', {
					id: that.id
				})
				.then(function(res) {
					console.log(res);
					let item = res.data;
					if (item.question_type <= 3) {
						for (let key in that.options) {
							let opKey = that.options[key];
							let opCorrectKey = 'correct_' + opKey;
							if (item.hasOwnProperty(opKey) && item.correctOption.indexOf(opKey) != -1) {
								item[opCorrectKey] = 1;
							} else {
								item[opCorrectKey] = 0;
							}
						}
					}
					that.item = item;
					that.title = item.questionAsk;
					that.isLoad = true;
					that.currentTopicTypeLabel = app.globalData.practice.getTopicTypeLabel(res.data.question_type);
				})
				.catch(function(res) {
					console.log(res);
					app.showToast('获取答题记录失败');
				});
		},

		onCollectTap: function() {
			let item = that.item;
			if (item.isCollection == 1) {
				item.isCollection = 2;
			} else {
				item.isCollection = 1;
			}
			app.globalData.server
				.postRequest('question/collect', {
					id: item.id,
					type: item.isCollection
				})
				.then(function(e) {
					app.showToast('收藏成功');
				})
				.catch(function(a) {
					app.showToast('收藏出错');
				});
		},
		onCommentTap: function() {
			app.showToast('敬请期待');
		},
		onCourseTap: function() {
			let item = that.item;
			uni.navigateTo({
				url: '/pages/practice/course/detail?id=' + item.course_id
			});
		},
		onFeedbackTap: function() {
			let item = that.item;
			uni.navigateTo({
				url: '/pages/practice/feedback/feedback?id=' + item.id
			});
		}
	}
}; 