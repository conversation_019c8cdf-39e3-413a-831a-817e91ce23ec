{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/user/login/bindopenid.vue?8f2a", "webpack:///D:/桌面/thinker/app/pages/user/login/bindopenid.vue?32c4", "webpack:///D:/桌面/thinker/app/pages/user/login/bindopenid.vue?c245", "webpack:///D:/桌面/thinker/app/pages/user/login/bindopenid.vue?5bcd", "uni-app:///pages/user/login/bindopenid.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "from", "onLoad", "that", "methods", "bindopenidTap", "uni", "success", "app", "code", "response", "rebackTap", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA6qB,CAAgB,4pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACajsB;AACA;AAAA,eACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;IACAA;EACA;EACAC;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;8BAAA,OACAC;gCACAC;8BACA;4BAAA;8BAFAC;8BAGA;gCACAP;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAQ;MACA;MACAL;QACAM;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/user/login/bindopenid.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/login/bindopenid.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./bindopenid.vue?vue&type=template&id=14edaee3&\"\nvar renderjs\nimport script from \"./bindopenid.vue?vue&type=script&lang=js&\"\nexport * from \"./bindopenid.vue?vue&type=script&lang=js&\"\nimport style0 from \"./bindopenid.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/login/bindopenid.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bindopenid.vue?vue&type=template&id=14edaee3&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n    adfootbanner: function () {\n      return import(\n        /* webpackChunkName: \"components/adfootbanner/adfootbanner\" */ \"@/components/adfootbanner/adfootbanner.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bindopenid.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bindopenid.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"绑定微信\"></back>\r\n\t\t<view class=\"padding flex flex-direction margin-top\">\r\n\t\t\t<button @tap=\"bindopenidTap\" class=\"cu-btn bg-gradual-green lg \">绑定微信，一键登录</button>\r\n\t\t\t<button @tap=\"rebackTap\" class=\"cu-btn bg-grey margin-tb-sm lg \"\r\n\t\t\t\tstyle=\"margin-top: 80rpx\">暂时没空，下次再说</button>\r\n\t\t</view>\r\n\t\t<adfootbanner unitId=\"adunit-9f7b1de89ce8659f\"></adfootbanner>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar that = null;\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfrom: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(options) {\r\n\t\t\tthat = this;\r\n\t\t\tthat.from = options.from;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync bindopenidTap() {\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tsuccess: async function(res) {\r\n\t\t\t\t\t\tlet response = await app.globalData.service.bindOpenId({\r\n\t\t\t\t\t\t\tcode: res.code\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (response.code == 1) {\r\n\t\t\t\t\t\t\tthat.rebackTap();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\trebackTap: function() {\r\n\t\t\t\tlet url = that.from == 1 ? '../../index/city/city' : '../../user/user';\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t};\r\n</script>\r\n<style src=\"./bindopenid.css\"></style>\r\n"], "sourceRoot": ""}