@import 'common/colorui/adaptation.css';
@import 'common/colorui/main.css';
@import 'common/colorui/icon.css';
@import 'common/font-awesome/style.css';
@import 'common/css/responsive.css';

page {
	background-color: #f5f5f5;
}

uni-toast {
	z-index: 10000;
}

/* 响应式基础样式 */
* {
	box-sizing: border-box;
}

/* 大屏适配基础样式 */
@media screen and (min-width: 1024rpx) {
	page {
		background-color: #f8f9fa;
	}

	/* 大屏下的卡片样式 */
	.cu-card {
		border-radius: 12rpx;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
	}

	/* 大屏下的按钮样式 */
	.cu-btn {
		border-radius: 8rpx;
		transition: all 0.3s ease;
	}

	.cu-btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.15);
	}
}