/* 支付容器样式 */
.payment-container {
	padding: 30rpx;
	background-color: #f8f8f8;
}
/* 订单卡片样式 */
.order-card {
	background-color: #ffffff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	margin-bottom: 30rpx;
	overflow: hidden;
}
.order-header {
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.order-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}
.order-content {
	padding: 20rpx 30rpx;
}
.order-item {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f8f8f8;
}
.order-item:last-child {
	border-bottom: none;
}
.item-label {
	color: #666666;
	margin: 0 16rpx;
}
.item-value {
	color: #333333;
	flex: 1;
}
.price-item {
	padding-top: 24rpx;
}
.price-value {
	color: #ff6600;
	font-size: 36rpx;
	font-weight: bold;
	flex: 1;
}
/* 支付方式区域 */
.payment-methods {
	background-color: #ffffff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	padding: 20rpx 30rpx;
	margin-bottom: 30rpx;
}
.section-title {
	display: flex;
	align-items: center;
	padding: 16rpx 0 24rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	margin-bottom: 24rpx;
}
.section-title text {
	margin-right: 10rpx;
	font-size: 30rpx;
	color: #333333;
}
.payment-btn {
	background: linear-gradient(to right, #07c160, #10ad6e);
	color: #ffffff;
	border-radius: 50rpx;
	font-size: 32rpx;
	padding: 20rpx 0;
	margin: 10rpx 0;
	display: flex;
	justify-content: center;
	align-items: center;
}
/* 扫码支付区域 */
.scan-pay-container {
	background-color: #ffffff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	padding: 20rpx 30rpx;
	margin-bottom: 30rpx;
}
.qrcode-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 0;
}
.qrcode-image {
	width: 400rpx;
	height: 400rpx;
	margin-bottom: 20rpx;
	border: 1rpx solid #f0f0f0;
	padding: 10rpx;
}
.qrcode-tip {
	display: flex;
	align-items: center;
	color: #666666;
	font-size: 28rpx;
	margin-top: 20rpx;
}
.confirm-btn {
	background: linear-gradient(to right, #1989fa, #0570db);
	color: #ffffff;
	border-radius: 50rpx;
	font-size: 32rpx;
	padding: 20rpx 0;
	margin: 20rpx 0 10rpx 0;
	display: flex;
	justify-content: center;
	align-items: center;
}
/* 支付说明 */
.payment-tips {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f8f8;
	border-radius: 8rpx;
	margin-top: 20rpx;
}
.tips-text {
	color: #999999;
	font-size: 26rpx;
	margin-left: 10rpx;
}

