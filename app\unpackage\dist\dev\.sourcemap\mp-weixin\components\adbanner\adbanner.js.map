{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/adbanner/adbanner.vue?d3be", "webpack:///D:/桌面/thinker/app/components/adbanner/adbanner.vue?2456", "webpack:///D:/桌面/thinker/app/components/adbanner/adbanner.vue?4fbe", "webpack:///D:/桌面/thinker/app/components/adbanner/adbanner.vue?7f78", "uni-app:///components/adbanner/adbanner.vue"], "names": ["name", "data", "showAd", "props", "unitId", "type", "default", "className", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;;;AAGvD;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4pB,CAAgB,0pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUhrB;AAAA,eACA;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;IACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "components/adbanner/adbanner.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./adbanner.vue?vue&type=template&id=4c6a9ae4&\"\nvar renderjs\nimport script from \"./adbanner.vue?vue&type=script&lang=js&\"\nexport * from \"./adbanner.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/adbanner/adbanner.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./adbanner.vue?vue&type=template&id=4c6a9ae4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./adbanner.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./adbanner.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<view v-if=\"showAd\" style=\"bottom:0;padding: 0;width: 100%;\" :class=\"className\">\r\n\t\t\t<ad :unit-id=\"unitId\" ad-intervals=\"30\"></ad>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n<script>\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tname: 'adbanner',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowAd: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tunitId: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'adunit-b8d5a49b6c1c8b07'\r\n\t\t\t},\r\n\t\t\tclassName: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'margin-top'\r\n\t\t\t},\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tlet checkHasVip = app.checkHasVip();\r\n\t\t\tif (!checkHasVip) {\r\n\t\t\t\tthis.showAd = true;\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n"], "sourceRoot": ""}