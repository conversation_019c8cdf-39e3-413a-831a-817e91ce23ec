/* iOS风格的转赠会员页面样�?*/
.transfer-container {
  background-color: #f5f7fa;
  min-height: 100vh;
}
.transfer-content {
  padding: 20rpx;
}
/* 卡片样式 */
.transfer-card {
  margin: 20rpx 0;
  border-radius: 16rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.card-header {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.step-badge {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background-color: #007aff;
  color: #ffffff;
  font-size: 24rpx;
  margin-right: 16rpx;
}
.notice-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  color: #007aff;
  font-size: 24rpx;
  margin-right: 16rpx;
}
.step-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}
.card-content {
  padding: 24rpx;
}
/* 输入框样�?*/
.input-container {
  position: relative;
}
.ios-input {
  height: 88rpx;
  border-radius: 8rpx;
  background-color: #f5f7fa;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333333;
}
/* 会员列表样式 */
.vip-list {
  display: flex;
  flex-direction: column;
}
.vip-item {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5ea;
  overflow: hidden;
  transition: all 0.3s ease;
}
.vip-item.active {
  border-color: #007aff;
  background-color: rgba(0, 122, 255, 0.05);
}
.vip-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  width: 100%;
}
.vip-info {
  display: flex;
  flex-direction: column;
}
.vip-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}
.vip-expire {
  font-size: 24rpx;
  color: #8e8e93;
}
/* iOS风格单选按�?*/
.ios-radio {
  border-radius: 50%;
  width: 44rpx;
  height: 44rpx;
  border: 2rpx solid #c7c7cc;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.ios-radio.checked {
  border-color: #007aff;
  background-color: #007aff;
}
.ios-radio.checked::before {
  content: '';
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #ffffff;
  position: absolute;
}
/* 确认按钮样式 */
.action-container {
  padding: 30rpx 20rpx;
}
.ios-button {
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  background-color: #007aff;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.2);
  transition: all 0.3s ease;
}
.ios-button:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  background-color: #0062cc;
}
/* 操作说明样式 */
.notice-card {
  margin-bottom: 40rpx;
}
.notice-list {
  display: flex;
  flex-direction: column;
}
.notice-item {
  display: flex;
  margin-bottom: 16rpx;
}
.notice-number {
  color: #007aff;
  font-weight: 500;
  margin-right: 8rpx;
  min-width: 32rpx;
}
.notice-text {
  color: #666666;
  font-size: 26rpx;
  line-height: 1.5;
}
