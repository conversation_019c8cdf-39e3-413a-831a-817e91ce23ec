<block wx:if="{{isLoad}}"><view class="article-container"><back vue-id="15089e26-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" showBack="{{true}}" showTitle="{{false}}" bind:__l="__l"></back><view class="article-wrapper"><view class="article-content"><view class="article-header"><text class="article-title">{{info.title}}</text><view class="article-meta"><view class="meta-item"><text class="cuIcon-time"></text><text>{{$root.f0}}</text></view><view class="meta-item"><text class="cuIcon-people"></text><text>{{info.author||'admin'}}</text></view><view class="meta-item"><text class="cuIcon-attention"></text><text>{{(info.page_view||0)+"阅读"}}</text></view></view></view><block wx:if="{{info.summary}}"><view class="article-summary"><text>{{info.summary}}</text></view></block><view class="article-body"><rich-text nodes="{{info.content}}"></rich-text></view><block wx:if="{{$root.g0}}"><view class="article-tags"><text class="tag-title">标签：</text><block wx:for="{{info.tags}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><view class="tag-item">{{''+tag+''}}</view></block></view></block><view class="article-actions"><button class="action-btn share-btn" open-type="share"><text class="cuIcon-share"></text><text>分享好文</text></button></view></view></view><adfootbanner vue-id="15089e26-2" bind:__l="__l"></adfootbanner></view></block>