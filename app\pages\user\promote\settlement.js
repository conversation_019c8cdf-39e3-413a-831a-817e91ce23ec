let app = getApp(),
that = null,
helper = app.globalData.helper;
export default {
	data() {
		return {
			page: 1,
			completed_order_id:0,
			isFinish: false,
			load: false,
			listData: []
		};
	},
	onLoad(options) {
		that = this;
		that.completed_order_id = helper.variableDefalut(options.completed_order_id, 0),
		that.getList();
	},
	onShow() {

	},
	onReachBottom() {
		that.page++;
		if (!that.isFinish) {
			that.getList();
		}
	},
	onShareAppMessage() {},
	methods: {
		getList() {
			app.globalData.server
				.getRequest('promote/settlementOrder', {
					page: that.page,
					completed_order_id:that.completed_order_id
				})
				.then(function(res) {
					uni.stopPullDownRefresh();
					that.load = true;
					if (res.data.length > 0) {
						that.listData = that.listData.concat(res.data);
					} else {
						that.isFinish = true;
					}
				})
				.catch(function(a) {
					app.showToast('加载数据失败');
				});
		},
		itemTap(options){
			let id = options.currentTarget.dataset.id;
			let url = '/pages/user/promote/completedorder?settlement_order_id='+id;
			console.log(url)
			uni.navigateTo({
				url: url
			});
		}
	}
};