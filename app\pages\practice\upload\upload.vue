<template>
	<view v-if="load">
		<back :showBackText="false"  customClass="bg-gradual-blue text-white" :showBack="true" :showTitle="true" title="创建题库"></back>
		<view class="flex flex-direction" style="min-height: 100vh;">
			<view class="flex-sub">
				<view class="guide-container">
					<view class="step-item" v-for="(step, index) in steps" :key="index">
						<view class="step-header">
							<text class="step-number">{{index + 1}}</text>
						</view>
						<view class="step-other">
							<view class="step-title">
								<text class="step-title">{{step.title}}</text>
							</view>
							<view class="step-content">{{step.content}}</view>
						</view>
					</view>
					<view class="tips">
						<view class="tips-title">温馨提示</view>
						<view class="tips-content margin-top-sm">创建题库如有任何疑问或需要人工协助(付费)上传题库，请联系在线客服。</view>
					</view>
				</view>
			</view>
			<advideo></advideo>
		</view>
	</view>
</template>
<style src="./upload.css"></style>
<script src="./upload.js"></script>