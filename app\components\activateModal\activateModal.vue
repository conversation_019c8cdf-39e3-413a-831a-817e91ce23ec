<template>
	<view class="cu-modal" :class="showModal?'show':''">
		<view class="cu-dialog" style="width: 650rpx; border-radius: 24rpx; overflow: hidden;">
			<view class="cu-bar bg-white justify-end" style="height: 100rpx; border-bottom: 1rpx solid rgba(0,0,0,0.05);">
				<view class="content text-bold" style="font-size: 34rpx;">{{title}}</view>
				<view class="action" @tap="closeModal" style="padding-right: 30rpx;">
					<text class="cuIcon-close text-gray"></text>
				</view>
			</view>
			<view class="padding-lg flex flex-direction align-center">
				<view class="diamond-icon margin-bottom-xl">
					<text class="cuIcon-diamond text-yellow" style="font-size: 120rpx;"></text>
				</view>
				<view class="cu-form-group margin-bottom-lg" style="width: 90%;">
					<input placeholder="请输入激活码" v-model="activateCode" class="radius" style="height: 90rpx; font-size: 32rpx; padding: 0 30rpx;"></input>
				</view>
				<view class="text-sm text-gray margin-bottom-lg">激活失败可咨询在线客服</view>
				<view class="flex margin-top-sm">
					<button class="cu-btn bg-blue margin-right-sm" style="height: 80rpx; font-size: 30rpx; padding: 0 40rpx;" @tap="showOnlineServiceTap" confirmButtonOpenType="contact">在线客服</button>
					<button class="cu-btn bg-blue" style="height: 80rpx; font-size: 30rpx; padding: 0 40rpx;" @tap="activateVIP">确认激活</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
let app = getApp();
export default {
	props: {
		// 是否显示弹窗
		show: {
			type: Boolean,
			default: false
		},
		// 弹窗标题
		title: {
			type: String,
			default: '激活题库'
		},
		// 业务ID
		businessId: {
			type: [Number, String],
			default: ''
		},
		// 业务类型
		businessType: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			showModal: false,
			activateCode: ''
		};
	},
	watch: {
		show(newVal) {
			this.showModal = newVal;
		}
	},
	created() {
		this.showModal = this.show;
	},
	methods: {
		// 关闭弹窗
		closeModal() {
			this.showModal = false;
			this.$emit('update:show', false);
		},
		// 显示在线客服
		showOnlineServiceTap() {
			this.$emit('showService');
		},
		// 激活码验证
		activateVIP() {
			if (!this.activateCode) {
				app.showToast('请输入激活码');
				return;
			}
			
			// 调用激活接口
			app.globalData.server
				.postRequest('activationCode/confirm', {
					code: this.activateCode,
					business_id: this.businessId,
					business_type: this.businessType
				})
				.then(res => {
					app.showToast('激活成功');
					this.closeModal();
					this.activateCode = '';
					// 触发激活成功事件
					this.$emit('success', res.data);
				})
				.catch(err => {
					console.log(err);

					app.showToast(err.data.message || '激活失败');
					// 触发激活失败事件
					this.$emit('fail', err);
				});
		}
	}
};
</script>

<style>
/* 可以添加自定义样式 */
</style>