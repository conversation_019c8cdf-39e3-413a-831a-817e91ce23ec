<template>
	<view v-if="isLoad && !appIsAudit" class="list-container">
		<back :showBackText="false"  customClass="bg-gradual-blue text-white" :showBack="true" :showTitle="true" title="最新资讯"></back>
		<view>
			<scroll-view scroll-x class="bg-white nav margin-bottom-xs">
				<view class="flex text-center">
					<view class="cu-item flex-sub " style="font-size: 30upx;"
						:class="cateIndex== index ?'text-blue cur text-bold':''" v-for="(item, index) in cateList"
						:key="index" @tap="cateTap(index)">
						{{item.name}}
					</view>
				</view>
			</scroll-view>
			<scroll-view class="scroll-Y" :style="'height:'+ (screenHeight-200) +'px'" scroll-y="true"
				@scrolltolower="upper">
				<view class="cu-card article no-card solid-bottom margin-xs-top" @tap="detailTap(item.id)"
					v-for="(item, index) in list" :key="index">
					<view class="cu-item shadow">
						<view class="content">
							<view>
								<image :src="item.thumb" mode="aspectFit" class="radius"
									style="height: 120rpx;width: 120rpx;">
								</image>
							</view>
							<view class="desc" style="justify-content: space-around;margin-left: 10rpx;">
								<view class="text-black text-df">{{item.title}}</view>
								<view class="flex justify-between">
									<view class="text-gray text-sm">{{item.cate_name}} · {{item.create_date}}</view>
									<view class="text-gray text-sm padding-right text-shadow">{{item.page_view}} 阅读
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<adfootbanner v-if="isLoad == true && list.length > 0"></adfootbanner>
				<empty v-if="isLoad == true && list.length == 0"></empty>
			</scroll-view>
		</view>
	</view>
</template>
<style src="./list.css"></style>
<script src="./list.js"></script>