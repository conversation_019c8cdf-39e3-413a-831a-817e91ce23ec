{"setting": {}, "condition": {"plugin": {"list": []}, "game": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"name": "pages/search/search", "pathName": "pages/search/search", "query": "", "scene": null}, {"name": "", "pathName": "pages/search/detail", "query": "", "scene": null}, {"name": "", "pathName": "pages/search/detail", "query": "id=1670023", "scene": null}, {"name": "", "pathName": "pages/search/detail", "query": "id=2", "scene": null}, {"name": "", "pathName": "pages/search/detail", "query": "id=1167519", "scene": null}, {"name": "", "pathName": "pages/user/promote/promote", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/register/register", "query": "", "scene": null}, {"name": "", "pathName": "pages/practice/practice", "query": "title=%E5%88%9D%E7%BA%A7%E4%BC%9A%E8%AE%A1%E5%AE%9E%E5%8A%A1&id=7810&extend_id=0&mainType=1&max_page=12&question_type=1&chargeType=1", "scene": null}, {"name": "", "pathName": "pages/user/user", "query": "", "scene": null}, {"name": "", "pathName": "pages/practice/practice", "query": "title=%E4%B8%AD%E5%9B%BD%E8%BF%91%E7%8E%B0%E4%BB%A3%E5%8F%B2%E7%BA%B2%E8%A6%81&id=1&extend_id=0&mainType=1&max_page=18&question_type=1&chargeType=1", "scene": null}, {"name": "", "pathName": "pages/user/vip/vip", "query": "", "scene": null}, {"name": "", "pathName": "pages/index/city/city", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/vip/payRes", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/info/info", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/info/info", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/user", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/sign/sign", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/login/login", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/vip/vip", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/promote/promote", "query": "", "scene": null}, {"name": "", "pathName": "pages/user/promote/group", "query": "", "scene": null}, {"name": "", "pathName": "pages/practice/practice", "query": "title=%E5%8F%8D%E5%AF%B9%E5%A4%96%E5%9B%BD%E4%BE%B5%E7%95%A5%E7%9A%84%E6%96%97%E4%BA%89&id=1&extend_id=123&mainType=5&max_page=12&question_type=0&chargeType=2", "scene": null}, {"name": "", "pathName": "pages/practice/practice", "query": "title=%E7%BB%BC%E5%90%88%E7%BB%83%E4%B9%A03&id=8029&extend_id=9765&mainType=5&max_page=1&question_type=0&chargeType=2", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pages/practice/practice", "query": "title=%E7%BB%BC%E5%90%88%E7%BB%83%E4%B9%A03&id=7971&extend_id=9279&mainType=5&max_page=1&question_type=0&chargeType=2", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pages/practice/practice", "query": "title=%E7%BB%BC%E5%90%88%E7%BB%83%E4%B9%A03&id=7971&extend_id=9279&mainType=5&max_page=1&question_type=0&chargeType=2", "scene": null, "launchMode": "default"}]}}}