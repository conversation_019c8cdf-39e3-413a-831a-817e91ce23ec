<template>
  <view>
    <back :showBackText="false" customClass="bg-gradual-blue text-white" title="授权登录"></back>
    <view class="sso-auth-container">
      <view class="sso-auth-header">
        <view class="sso-auth-icon">
          <text class="cuIcon-time text-blue" style="font-size:80rpx;"></text>
        </view>
        <view class="sso-auth-title">授权登录网页端</view>
        <view class="sso-auth-desc">若非本人操作，请忽略申请</view>
      </view>
      <view class="sso-auth-btns">
        <button class="cu-btn bg-blue shadow radius lg sso-auth-confirm" @tap="confirmTap">确认登录</button>
        <button class="cu-btn line-blue radius lg sso-auth-cancel" @tap="cancelTap">取消</button>
      </view>
    </view>
  </view>
</template>

<script src="./sso.js"></script>
<style src="./sso.css"></style>