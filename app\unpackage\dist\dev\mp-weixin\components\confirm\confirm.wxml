<view><view data-event-opts="{{[['tap',[['setStatusTap',[false]]]]]}}" class="{{['cu-modal','radius',status==true?'show':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="cu-dialog" catchtap="__e"><view class="cu-bar bg-white justify-end"><view class="content">{{title}}</view><view data-event-opts="{{[['tap',[['setStatusTap',[false]]]]]}}" class="action" bindtap="__e"><text class="cuIcon-close text-red"></text></view></view><view class="padding-xl" style="word-break:break-all;"><block wx:if="{{content}}"><rich-text class="explain-text" nodes="{{content}}"></rich-text></block></view><block wx:if="{{confirmText}}"><view class="cu-bar bg-white"><button class="action margin flex-sub text-green" openType="{{confirmButtonOpenType}}" data-event-opts="{{[['tap',[['setStatusTap',[false]]]]]}}" bindtap="__e"><text class="{{[confirmTextClass?confirmTextClass:'']}}"></text>{{confirmText+''}}</button></view></block></view></view></view>