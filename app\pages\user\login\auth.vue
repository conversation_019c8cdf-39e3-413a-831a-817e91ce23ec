<template>
	<view v-if="load">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="正在自动登录"></back>
	</view>
</template>
<script>
	let that = null,
		app = getApp(),
		cache = app.globalData.config.storage;
	export default {
		data() {
			return {
				load: false,
			};
		},
		onLoad: function(options) {
			let authKey = options.authKey;
			that = this;
			that.getAuthData(authKey);
		},
		onShow: function() {},
		methods: {
			getAuthData: function(authKey) {
				app.globalData.server
					.getRequest('user/auth', {
						authKey: authKey
					})
					.then(function(e) {
						that.load = true;
						cache.setFristData(2);
						cache.setUserTokenData(e.data.tokenInfo);
						cache.setUserInfoData(e.data.info);
						cache.setCurrentCityData(e.data.regionInfo);
						cache.setCurrentExamData(e.data.examInfo);
						cache.setCurrentProfessionData(e.data.professionInfo);
						app.showToast('登录成功');
						app.globalData.checkLogin();
						let url = '../../index/index';
						if (e.data.redirectUrl != '') {
							url = e.data.redirectUrl
						}
						uni.reLaunch({
							url: url
						});
					})
					.catch(function(e) {
						app.showToast('自动登录失败');
					});
			}
		}
	};
</script>
<style src="./auth.css"></style>