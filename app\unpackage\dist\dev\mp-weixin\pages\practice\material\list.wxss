.bottom-layout {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
}
page.data-v-487aa56b {
  height: 100%;
  background-color: #f5f7fa;
}
.material-list-ios.data-v-487aa56b {
  background: #f7f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.list-content.data-v-487aa56b {
  flex: 1;
  padding: 32rpx 0 120rpx 0;
}
.scroll-area.data-v-487aa56b {
  min-height: 60vh;
}
.material-card.data-v-487aa56b {
  background: #fff;
  border-radius: 24rpx;
  margin: 32rpx 32rpx 0 32rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  box-shadow: 0 4rpx 32rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.card-main.data-v-487aa56b {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.card-title-row.data-v-487aa56b {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.card-title.data-v-487aa56b {
  font-size: 36rpx;
  font-weight: 700;
  color: #222;
}
.card-tags.data-v-487aa56b {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}
.price-tag.data-v-487aa56b {
  font-size: 28rpx;
  padding: 8rpx 24rpx;
}
.version-tag.data-v-487aa56b {
  font-size: 22rpx;
  padding: 4rpx 16rpx;
}
.card-meta-row.data-v-487aa56b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
}
.meta-item.data-v-487aa56b {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  font-size: 26rpx;
  color: #888;
}
.center-value.data-v-487aa56b {
  display: flex;
  align-items: center;
  height: 100%;
}
.meta-label.data-v-487aa56b {
  margin-left: 4rpx;
}
.meta-value.data-v-487aa56b {
  color: #39b54a;
  font-weight: 600;
  margin-left: 8rpx;
}
.meta-action.data-v-487aa56b {
  display: flex;
  align-items: center;
}
.detail-btn.data-v-487aa56b {
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  font-weight: 500;
  border-radius: 16rpx;
}
.float-action-container.data-v-487aa56b {
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  z-index: 99;
}
.share-button.data-v-487aa56b {
  background: linear-gradient(135deg, #0081ff, #1cbbb4);
  border-radius: 50rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 129, 255, 0.3);
  padding: 16rpx 32rpx;
  display: flex;
  align-items: center;
}
.share-button text.data-v-487aa56b {
  color: #ffffff;
}
.share-button .cuIcon-add.data-v-487aa56b {
  font-size: 32rpx;
  margin-right: 8rpx;
}

