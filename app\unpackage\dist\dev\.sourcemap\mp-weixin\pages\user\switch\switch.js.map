{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/user/switch/switch.vue?2d77", "webpack:///D:/桌面/thinker/app/pages/user/switch/switch.vue?91d0", "webpack:///D:/桌面/thinker/app/pages/user/switch/switch.vue?07d1", "webpack:///D:/桌面/thinker/app/pages/user/switch/switch.vue?ef0e", "uni-app:///pages/user/switch/switch.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "load", "showNotice", "showNoticeText", "appIsAudit", "onLoad", "that", "app", "onShow", "methods", "getInfo", "getRequest", "then", "catch", "console", "copyTap", "uni", "fail", "success", "hideNoticeTap"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAyqB,CAAgB,wpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiD7rB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAH;MACAI;IACA;EACA;EACAC;IACAC;IACAC;IACAD;EACA;EACAE;EACAC;IACAC;MACAH,sBACAI,8BACAC;QACAN;QACAA;MACA,GACAO;QACAN;QACAO;MACA;IACA;IACAC;MACA;MACA;MACAC;QACAhB;QACAiB;UACAV;QACA;QACAW;UACAZ;UACAA;QACA;MACA;IACA;IACAa;MACAb;IACA;EACA;AACA;AAAA,2B", "file": "pages/user/switch/switch.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/switch/switch.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./switch.vue?vue&type=template&id=62f589e4&\"\nvar renderjs\nimport script from \"./switch.vue?vue&type=script&lang=js&\"\nexport * from \"./switch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./switch.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/switch/switch.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./switch.vue?vue&type=template&id=62f589e4&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./switch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./switch.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"load\">\r\n\t\t<back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"切换版本\"></back>\r\n\t\t<view class=\"cu-bar bg-white solid-bottom\">\r\n\t\t\t<view class=\"action\">\r\n\t\t\t\t<text class=\"cuIcon-titles text-orange \"></text> 版本列表\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"cu-list menu\">\r\n\t\t\t<view class=\"cu-item arrow\">\r\n\t\t\t\t<view class=\"content\" @tap=\"copyTap(1)\" >\r\n\t\t\t\t\t<text class=\"cuIcon-discoverfill text-blue\"></text>\r\n\t\t\t\t\t<text class=\"text-blue\">网页版刷题（复制网址）</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cu-item arrow\">\r\n\t\t\t\t<view class=\"content\" @tap=\"copyTap(2)\">\r\n\t\t\t\t\t<text class=\"cuIcon-discoverfill text-blue\"></text>\r\n\t\t\t\t\t<text class=\"text-blue\">安卓APP版（下载地址）</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cu-item arrow\">\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<text class=\"cuIcon-discoverfill text-gray\"></text>\r\n\t\t\t\t\t<text class=\"text-gray\">苹果APP版（即将上线）</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"cu-modal\" :class=\"showNotice ? 'show':''\">\r\n\t\t\t<view class=\"cu-dialog\">\r\n\t\t\t\t<view class=\"cu-bar bg-white justify-end\">\r\n\t\t\t\t\t<view class=\"content\">提示</view>\r\n\t\t\t\t\t<view class=\"action\" @tap=\"hideNoticeTap\">\r\n\t\t\t\t\t\t<text class=\"cuIcon-close text-red\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"padding-xl\">\r\n\t\t\t\t\t{{showNoticeText}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cu-bar bg-white\">\r\n\t\t\t\t\t<button class=\"action  margin flex-sub text-green\" @tap=\"hideNoticeTap\">\r\n\t\t\t\t\t\t<text class=\"cuIcon-check\"></text>知道啦\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tlet that = null;\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tload: false,\r\n\t\t\t\tshowNotice: false,\r\n\t\t\t\tshowNoticeText:'',\r\n\t\t\t\tdata: [],\r\n\t\t\t\tappIsAudit: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function() {\r\n\t\t\tthat = this;\r\n\t\t\tapp.globalData.checkAppIsAuditAndRedirect();\r\n\t\t\tthat.getInfo();\r\n\t\t},\r\n\t\tonShow: function() {},\r\n\t\tmethods: {\r\n\t\t\tgetInfo: function() {\r\n\t\t\t\tapp.globalData.server\r\n\t\t\t\t\t.getRequest('user/switch', {})\r\n\t\t\t\t\t.then(function(e) {\r\n\t\t\t\t\t\tthat.data = e.data;\r\n\t\t\t\t\t\tthat.load = true;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(function(e) {\r\n\t\t\t\t\t\tapp.showToast('获取信息失败');\r\n\t\t\t\t\t\tconsole.log(e);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcopyTap: function(type) {\r\n\t\t\t\tlet text = type==1 ? that.data.h5:that.data.android;\r\n\t\t\t\tlet showNoticeText = type==1 ? '网址复制成功，您可以粘贴到手机浏览器进行访问，再通过浏览器添加到桌面，即可享受APP版本纯净体验':'安卓版下载地址已经复制,您可以粘贴到手机浏览器进行下载'\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: text.toString(),\r\n\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\tapp.showToast('网址复制失败');\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\tthat.showNoticeText = showNoticeText; \r\n\t\t\t\t\t\tthat.showNotice = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thideNoticeTap(){\r\n\t\t\t\tthat.showNotice = false;\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style src=\"./switch.css\"></style>\r\n"], "sourceRoot": ""}