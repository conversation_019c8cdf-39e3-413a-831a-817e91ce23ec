{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?c8fa", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?97b2", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?97db", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?4118", "uni-app:///pages/responsive-demo/responsive-demo.vue", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?99bd", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?dbfb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "mixins", "data", "onLoad", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACqC;;;AAGnG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACuGvrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;EACAC;IACA,QAEA;EACA;EACAC;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpHA;AAAA;AAAA;AAAA;AAAo+B,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACAx/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/responsive-demo/responsive-demo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/responsive-demo/responsive-demo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./responsive-demo.vue?vue&type=template&id=4f69b280&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-demo.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-demo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-demo.vue?vue&type=style&index=0&id=4f69b280&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f69b280\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/responsive-demo/responsive-demo.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=template&id=4f69b280&scoped=true&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"responsive-demo\" :class=\"responsiveClass\">\n    <back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"响应式演示\"></back>\n    \n    <!-- 响应式容器演示 -->\n    <responsive-container>\n      <view class=\"demo-section\">\n        <view class=\"section-title\">屏幕信息</view>\n        <view class=\"info-card\">\n          <view class=\"info-item\">\n            <text class=\"label\">断点:</text>\n            <text class=\"value\">{{ screenInfo.breakpoint }}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"label\">宽度:</text>\n            <text class=\"value\">{{ screenInfo.width }}px</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"label\">高度:</text>\n            <text class=\"value\">{{ screenInfo.height }}px</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"label\">大屏设备:</text>\n            <text class=\"value\">{{ isLargeDevice ? '是' : '否' }}</text>\n          </view>\n        </view>\n      </view>\n    </responsive-container>\n\n    <!-- 响应式网格演示 -->\n    <responsive-container>\n      <view class=\"demo-section\">\n        <view class=\"section-title\">响应式网格</view>\n        <responsive-grid class=\"demo-grid\">\n          <view class=\"grid-item\" v-for=\"n in 12\" :key=\"n\">\n            <view class=\"item-content\">{{ n }}</view>\n          </view>\n        </responsive-grid>\n      </view>\n    </responsive-container>\n\n    <!-- 媒体查询演示 -->\n    <responsive-container>\n      <view class=\"demo-section\">\n        <view class=\"section-title\">媒体查询演示</view>\n        \n        <match-media breakpoint=\"xs\">\n          <view class=\"media-demo xs\">超小屏幕 (xs) 显示</view>\n        </match-media>\n        \n        <match-media breakpoint=\"sm\">\n          <view class=\"media-demo sm\">小屏幕 (sm) 显示</view>\n        </match-media>\n        \n        <match-media breakpoint=\"md\">\n          <view class=\"media-demo md\">中等屏幕 (md) 显示</view>\n        </match-media>\n        \n        <match-media breakpoint=\"lg\">\n          <view class=\"media-demo lg\">大屏幕 (lg) 显示</view>\n        </match-media>\n        \n        <match-media breakpoint=\"xl\">\n          <view class=\"media-demo xl\">超大屏幕 (xl) 显示</view>\n        </match-media>\n      </view>\n    </responsive-container>\n\n    <!-- 响应式文本演示 -->\n    <responsive-container>\n      <view class=\"demo-section\">\n        <view class=\"section-title\">响应式文本</view>\n        <view class=\"text-demo\">\n          <text class=\"text-xs-sm text-sm-md text-md-lg text-lg-lg text-xl-lg\">\n            这段文字在不同屏幕尺寸下显示不同大小\n          </text>\n        </view>\n      </view>\n    </responsive-container>\n\n    <!-- 响应式布局演示 -->\n    <responsive-container>\n      <view class=\"demo-section\">\n        <view class=\"section-title\">响应式布局</view>\n        <view class=\"layout-demo\">\n          <view class=\"sidebar d-xs-none d-sm-none d-md-block\">\n            <view class=\"sidebar-content\">侧边栏 (中屏及以上显示)</view>\n          </view>\n          <view class=\"main-content\">\n            <view class=\"content-card\">\n              <view class=\"card-title\">主要内容</view>\n              <view class=\"card-body\">\n                这是主要内容区域，在小屏幕上占满宽度，在大屏幕上与侧边栏并排显示。\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </responsive-container>\n  </view>\n</template>\n\n<script>\nimport { responsiveMixin } from '@/common/js/responsive.js'\n\nexport default {\n  name: 'responsive-demo',\n  mixins: [responsiveMixin],\n  data() {\n    return {\n      \n    }\n  },\n  onLoad() {\n    console.log('响应式演示页面加载');\n  }\n}\n</script>\n\n<style scoped>\n.responsive-demo {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.demo-section {\n  margin-bottom: 40rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  padding: 20rpx 0;\n  border-bottom: 2rpx solid #e0e0e0;\n}\n\n.info-card {\n  background: white;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.info-item:last-child {\n  border-bottom: none;\n}\n\n.label {\n  font-weight: bold;\n  color: #666;\n}\n\n.value {\n  color: #007aff;\n  font-weight: bold;\n}\n\n.demo-grid {\n  gap: 20rpx;\n}\n\n.grid-item {\n  background: white;\n  border-radius: 8rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.item-content {\n  padding: 40rpx;\n  text-align: center;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #007aff;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.media-demo {\n  padding: 30rpx;\n  margin: 20rpx 0;\n  border-radius: 8rpx;\n  text-align: center;\n  font-weight: bold;\n  color: white;\n}\n\n.media-demo.xs {\n  background: #ff6b6b;\n}\n\n.media-demo.sm {\n  background: #4ecdc4;\n}\n\n.media-demo.md {\n  background: #45b7d1;\n}\n\n.media-demo.lg {\n  background: #96ceb4;\n}\n\n.media-demo.xl {\n  background: #feca57;\n}\n\n.text-demo {\n  background: white;\n  padding: 40rpx;\n  border-radius: 12rpx;\n  text-align: center;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.layout-demo {\n  display: flex;\n  gap: 20rpx;\n  min-height: 400rpx;\n}\n\n.sidebar {\n  flex: 0 0 300rpx;\n  background: white;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.sidebar-content {\n  padding: 30rpx;\n  text-align: center;\n  font-weight: bold;\n  color: #666;\n}\n\n.main-content {\n  flex: 1;\n}\n\n.content-card {\n  background: white;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n  height: 100%;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.card-body {\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 大屏优化 */\n@media screen and (min-width: 1024rpx) {\n  .section-title {\n    font-size: 36rpx;\n  }\n  \n  .info-card,\n  .content-card {\n    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);\n  }\n  \n  .grid-item:hover .item-content {\n    transform: scale(1.05);\n    transition: transform 0.3s ease;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=style&index=0&id=4f69b280&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=style&index=0&id=4f69b280&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753559149994\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}