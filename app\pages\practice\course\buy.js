import {
	post
} from "@/common/js/http.js";
let app = getApp();
let that = null;
export default {
	data() {
		return {
			info: [],
			isLoad: false,
			id: 0,
			appIsAudit: false,
		};
	},
	onLoad(options) {
		that = this;
		that.id = options.id;
		app.globalData.showShareMenu();
	},
	onShow(options) {
		that.appIsAudit = app.globalData.checkAppIsAudit();
		that.getInfo();
	},
	methods: {
		async getInfo() {
			let res = await post('course/saleInfo', {
				id: that.id
			})
			that.info = res.data;
			that.isLoad = true;
		},

		/**
		 * 确认开通
		 * @param {Object} options
		 */
		async onBuyCommit(options) {
			uni.showModal({
				title: '确认开通',
				cancelText: '我不同意',
				confirmText: '我同意',
				content: '我同意服务协议所有条款',
				success: async (res)=> {
					if (res.confirm) {
						let res = await post('order/create', {
							type: 2,
							course_id: that.id
						});
						let id = res.data.order_id;
						let url = '/pages/pay/pay?id=' + id;
						uni.navigateTo({
							url: url
						});
					}
				}
			});
		},
	}
};