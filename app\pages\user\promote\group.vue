<template>
	<view v-if="load">
		<back :showBackText="false" customClass="bg-gradual-red text-white" title="推广记录"></back>
		<view class="cu-list menu-avatar" v-if="listData.length > 0">
			<view class="cu-item arrow" v-for="(item, index) in listData" :key="index">
				<view class="cu-avatar radius lg" :style="'background-image:url(' + item.avatar + ');'">

				</view>
				<view class="content">
					<view class="text-cut">{{ item.nickname }}</view>
					<view class="flex text-gray text-sm">
						<text class="text-cut">
							<text class="cuIcon-cuIcon text-red margin-right-xs"></text>
							会员编号{{ item.id }} ({{item.relation_level_name}})
						</text>
					</view>
				</view>

				<view class="action">
					<view class="text-red text-xs">{{ item.add_date }}</view>
					<view class="cu-tag round bg-red sm">{{ item.add_time }}</view>
				</view>
			</view>
		</view>
		<empty v-if="listData.length == 0" info="赶紧去推广赚钱吧"></empty>
	</view>
</template>
<style src="./group.css"></style>
<script src="./group.js"></script>