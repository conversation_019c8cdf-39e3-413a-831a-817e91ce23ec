<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<view v-if="showAd" style="bottom:0;padding: 0;width: 100%;" class="margin-top">
			<ad :unit-id="unitId" ad-type="video" :ad-theme="adTheme"></ad>
		</view>
		<!-- #endif -->
	</view>
</template>
<script>
	const app = getApp();
	export default {
		name: 'advideo',
		data() {
			return {
				showAd: false
			};
		},
		props: {
			unitId: {
				type: String,
				default: 'adunit-9099eb3969908c24'
			},
			adTheme: {
				type: String,
				default: 'white'
			},
		},
		created() {
			let checkHasVip = app.checkHasVip();
			if (!checkHasVip) {
				this.showAd = true;
			}
		}
	};
</script>
