.price_select {
    box-shadow: 0 4rpx 16rpx rgba(3,169,244,0.12), 0 0 0 2rpx #03a9f4;
    border-radius: 18rpx;
    background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
    border: 2rpx solid #03a9f4;
    transition: box-shadow 0.2s, border 0.2s, background 0.2s;
    margin: 10rpx 0;
    padding: 32rpx 0 24rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}
.price_select.selected {
    box-shadow: 0 8rpx 32rpx rgba(3,169,244,0.18), 0 0 0 3rpx #0288d1;
    border-color: #0288d1;
    background: linear-gradient(135deg, #b3e5fc 0%, #e1f5fe 100%);
}
.price_select .price {
    font-size: 40rpx;
    font-weight: bold;
    color: #0288d1;
    margin-bottom: 10rpx;
}
.price_select .desc {
    font-size: 26rpx;
    color: #666;
}

/* Styles for Charge Notice section */
/* Add some margin below the title bar */
.cu-bar.bg-white + .cu-list.menu {
    margin-top: 20rpx; /* Add space between title and list */
}

/* Style the notice list container */
.cu-list.menu {
    background-color: #f9f9f9; /* Light background for the section */
    border-radius: 10rpx; /* Rounded corners */
    margin: 20rpx; /* Add margin around the section */
    padding: 10rpx 0; /* Add vertical padding */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

/* Style individual notice items */
.cu-list.menu > .cu-item {
    padding: 20rpx 30rpx; /* Increase padding */
    background-color: #ffffff; /* White background for items */
    margin: 0 10rpx 10rpx 10rpx; /* Space between items and container */
    border-radius: 8rpx; /* Rounded corners for items */
    border-left: 4px solid #0081ff; /* Add a colored left border */
    min-height: auto; /* Override default min-height if needed */
}

/* Remove bottom margin for the last item */
.cu-list.menu > .cu-item:last-child {
    margin-bottom: 0;
}

/* Style the text inside notice items */
.cu-list.menu > .cu-item .content text {
    color: #555; /* Darker gray text */
    line-height: 1.6; /* Improve readability */
}

/* Style the title bar for consistency */
.cu-bar.bg-white {
    padding: 20rpx 30rpx; /* Add padding to title bar */
    border-bottom: none; /* Remove the default bottom border */
    margin-bottom: 0; /* Remove bottom margin if any */
}
.cu-bar.bg-white .action text.cuIcon-titles {
    margin-right: 10rpx; /* Space after icon */
}
.cu-bar.bg-white .action text:last-child {
    font-weight: bold; /* Make title bold */
    font-size: 30rpx; /* Slightly larger font size */
}