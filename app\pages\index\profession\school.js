import adbanner from '@/components/adbanner/adbanner';
let app = getApp();
let that = null;
export default {
	components: {
		adbanner
	},
	data() {
		return {
			currentCity: {},
			currentExam: {},
			CustomBar: this.CustomBar,
			hidden: true,
			name: '',
			load: false,
			firstVisit: true,
			listData: {},
			listInitial: {},
			listCur: '',
			listCurID: ''
		};
	},
	onLoad(options) {
		that = this;
		that.initCityAndExam(options);
		that.getList();
	},
	methods: {
		initCityAndExam: function(options) {
			that.currentCity = app.globalData.config.storage.getCurrentCityData();
			that.currentExam = app.globalData.config.storage.getCurrentExamData();
			if (options.hasOwnProperty('exam_id')) {
				that.currentExam = {
					id: options.exam_id,
					name: options.exam_name
				};
			}
		},
		getList: function() {
			let name = that.name;
			app.globalData.server
				.getRequest('school/get', {
					name: name,
					exam_id: that.currentExam.id,
					province_id: that.currentCity.id
				})
				.then(function(res) {
					that.setData({
						load: true,
						listData: res.data
					});
				});
		},
		inputTap: function(options) {
			let name = options.detail.value;
			that.setData({
				name: name
			});
		},
		searchTap(options) {
			that.getList();
		},
		schoolTap(t) {
			let currentSchool = t.currentTarget.dataset.data;
			app.globalData.config.storage.setCurrentSchoolData(currentSchool);
			let examData = app.globalData.config.storage.getCurrentExamData();
			uni.navigateTo({
				url: `../profession/profession?exam_id=${examData.id}&exam_name=${examData.name}&school_id=${currentSchool.id}&school_name=${currentSchool.name}`
			});
		},
		cityTap: function() {
			uni.navigateTo({
				url: '../city/city'
			});
		},
	}
};