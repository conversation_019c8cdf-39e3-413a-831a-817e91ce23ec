/* 响应式搜索页面样式 */
.search-page {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.search-container {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

/* 搜索输入区域 */
.search-input-section {
	background: white;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.search-form {
	border: none;
	margin: 0;
}

.search-textarea {
	min-height: 120rpx;
	padding: 30rpx;
	font-size: 28rpx;
	line-height: 1.5;
}

/* 操作按钮区域 */
.action-buttons-section {
	background: white;
	border-radius: 12rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.action-buttons-container {
	display: grid;
	gap: 15rpx;
}

.action-group {
	display: flex;
	justify-content: center;
}

.responsive-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: #007aff;
	color: white;
	border-radius: 12rpx;
	padding: 25rpx 20rpx;
	min-height: 120rpx;
	width: 100%;
	border: none;
	transition: all 0.3s ease;
}

.responsive-btn text {
	margin: 5rpx 0;
}

.responsive-btn .cuIcon-voicefill,
.responsive-btn .cuIcon-camerafill,
.responsive-btn .cuIcon-copy,
.responsive-btn .cuIcon-deletefill {
	font-size: 40rpx;
	margin-bottom: 8rpx;
}

.btn-text {
	font-size: 24rpx;
	font-weight: normal;
}

/* 语音录制区域 */
.voice-recording-section {
	background: white;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.voice-container {
	text-align: center;
}

.voice-progress {
	height: 60rpx;
	border-radius: 30rpx;
	overflow: hidden;
}

.voice-progress-bar {
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
}

/* 搜索按钮区域 */
.search-button-section {
	background: white;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.search-button-container {
	display: flex;
	justify-content: center;
}

.search-btn {
	width: 100%;
	max-width: 400rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.search-btn-text {
	margin-left: 10rpx;
}

/* 搜索动态区域 */
.search-dynamics-section {
	background: white;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.dynamics-header {
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.dynamics-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.dynamics-grid {
	padding: 20rpx;
}

.dynamics-item {
	margin-bottom: 20rpx;
}

.dynamics-card {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;
}

.dynamics-header-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.dynamics-avatar {
	flex-shrink: 0;
}

.dynamics-meta {
	flex: 1;
	text-align: right;
}

.dynamics-time {
	font-size: 24rpx;
}

.dynamics-content {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	align-items: center;
}

.dynamics-nickname {
	font-weight: bold;
}

.dynamics-action {
	font-size: 26rpx;
}

.dynamics-question {
	font-weight: bold;
	flex: 1;
}

/* 响应式适配 */
/* 小屏幕 */
@media screen and (max-width: 576rpx) {
	.search-textarea {
		min-height: 100rpx;
		padding: 20rpx;
		font-size: 26rpx;
	}

	.responsive-btn {
		min-height: 100rpx;
		padding: 20rpx 15rpx;
	}

	.responsive-btn .cuIcon-voicefill,
	.responsive-btn .cuIcon-camerafill,
	.responsive-btn .cuIcon-copy,
	.responsive-btn .cuIcon-deletefill {
		font-size: 36rpx;
	}

	.btn-text {
		font-size: 22rpx;
	}
}

/* 中等屏幕 */
@media screen and (min-width: 769rpx) and (max-width: 1024rpx) {
	.search-container {
		gap: 30rpx;
	}

	.search-textarea {
		min-height: 140rpx;
		padding: 40rpx;
		font-size: 30rpx;
	}

	.responsive-btn {
		min-height: 140rpx;
		padding: 30rpx 25rpx;
	}

	.responsive-btn .cuIcon-voicefill,
	.responsive-btn .cuIcon-camerafill,
	.responsive-btn .cuIcon-copy,
	.responsive-btn .cuIcon-deletefill {
		font-size: 44rpx;
	}

	.btn-text {
		font-size: 26rpx;
	}

	.dynamics-card:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 25rpx rgba(0,0,0,0.15);
	}
}

/* 大屏幕 */
@media screen and (min-width: 1025rpx) {
	.search-container {
		gap: 40rpx;
	}

	.search-textarea {
		min-height: 160rpx;
		padding: 50rpx;
		font-size: 32rpx;
	}

	.responsive-btn {
		min-height: 160rpx;
		padding: 40rpx 30rpx;
		border-radius: 16rpx;
	}

	.responsive-btn:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 25rpx rgba(0,123,255,0.3);
	}

	.responsive-btn .cuIcon-voicefill,
	.responsive-btn .cuIcon-camerafill,
	.responsive-btn .cuIcon-copy,
	.responsive-btn .cuIcon-deletefill {
		font-size: 48rpx;
	}

	.btn-text {
		font-size: 28rpx;
	}

	.search-btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 6rpx 20rpx rgba(0,123,255,0.3);
	}

	.dynamics-card:hover {
		transform: translateY(-6rpx);
		box-shadow: 0 12rpx 30rpx rgba(0,0,0,0.15);
		border-color: #007aff;
	}

	.title-text {
		font-size: 36rpx;
	}
}

/* 旧样式兼容 */
.action-button {
	background-color: #f4f5f7;
	color: #222;
	margin: 0 5rpx;
	padding: 0;
}