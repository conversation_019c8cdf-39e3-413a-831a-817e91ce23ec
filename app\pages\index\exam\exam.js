let app = getApp();
let that = null;
export default {
	data() {
		return {
			list: [],
			load: false
		};
	},
	onLoad() {
		that = this;
		that.getExamList();
	},
	methods: {
		getExamList() {
			app.globalData.server
				.getRequest('exam/get', {})
				.then(function(e) {
					that.list = e.data;
					that.load = true;
				})
				.catch(function(e) {
					app.showToast('获取考试信息失败');
				});
		},
		examTap(options) {
			let index = options.currentTarget.dataset.index;
			let indexItem = that.list[index];
			uni.navigateTo({
				url: `../profession/profession?exam_id=${indexItem.id}&exam_name=${indexItem.name}`
			});
			return;
			if (indexItem.id == 1) {
				uni.navigateTo({
					url: `../profession/school?exam_id=${indexItem.id}&exam_name=${indexItem.name}`
				});
			} else {
				uni.navigateTo({
					url: `../profession/profession?exam_id=${indexItem.id}&exam_name=${indexItem.name}`
				});
			}

		}
	}
};