<view><back vue-id="ee6ea2fa-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="绑定微信" bind:__l="__l"></back><view class="padding flex flex-direction margin-top"><button data-event-opts="{{[['tap',[['bindopenidTap',['$event']]]]]}}" class="cu-btn bg-gradual-green lg" bindtap="__e">绑定微信，一键登录</button><button data-event-opts="{{[['tap',[['rebackTap',['$event']]]]]}}" class="cu-btn bg-grey margin-tb-sm lg" style="margin-top:80rpx;" bindtap="__e">暂时没空，下次再说</button></view><adfootbanner vue-id="ee6ea2fa-2" unitId="adunit-9f7b1de89ce8659f" bind:__l="__l"></adfootbanner></view>