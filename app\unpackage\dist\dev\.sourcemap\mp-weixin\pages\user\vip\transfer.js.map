{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/user/vip/transfer.vue?c55d", "webpack:///D:/桌面/thinker/app/pages/user/vip/transfer.vue?ecc3", "webpack:///D:/桌面/thinker/app/pages/user/vip/transfer.vue?d8b1", "webpack:///D:/桌面/thinker/app/pages/user/vip/transfer.vue?323b", "uni-app:///pages/user/vip/transfer.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "info", "isLoad", "selectVipId", "selectUserId", "showUserInfo", "onLoad", "that", "app", "onShow", "methods", "getInfo", "res", "uidInputTap", "vipListChangeTap", "submitTap", "uni", "content", "success", "console", "code", "uid", "vid", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA2qB,CAAgB,0pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmF/rB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAC;EACA;EACAC;IACAF;EACA;EACAG;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAH;cAAA;gBAAAI;gBACAL;gBACAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAM;MACAN;IACA;IACAO;MACAP;IACA;IACAQ;MACA;QACAP;QACA;MACA;MACA;QACAA;QACA;MACA;MACAQ;QACAC;QACAC;UACA;YACAF;cACAE;gBAAA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACAC;0BACAC;0BAAA,KACAA;4BAAA;4BAAA;0BAAA;0BACApB;4BACAqB;4BACAC;4BACAF;0BACA;0BAAA;0BAAA,OACAZ;wBAAA;0BAAAI;0BACAJ;0BACAe;4BACAhB;0BACA;0BAAA;0BAAA;wBAAA;0BAEAC;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;cAEA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/user/vip/transfer.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/vip/transfer.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./transfer.vue?vue&type=template&id=8cbf9490&\"\nvar renderjs\nimport script from \"./transfer.vue?vue&type=script&lang=js&\"\nexport * from \"./transfer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./transfer.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/vip/transfer.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./transfer.vue?vue&type=template&id=8cbf9490&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./transfer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./transfer.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"transfer-container\">\r\n\t\t<back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"赠送会员\"></back>\r\n\t\t<view v-if=\"isLoad\" class=\"transfer-content\">\r\n\t\t\t<!-- 第一步：填写会员编号 -->\r\n\t\t\t<view class=\"transfer-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<view class=\"step-badge\">1</view>\r\n\t\t\t\t\t<text class=\"step-title\">填写会员编号</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"input-container\">\r\n\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\t@input=\"uidInputTap\" \r\n\t\t\t\t\t\t\tmaxlength=\"32\" \r\n\t\t\t\t\t\t\tplaceholder=\"请填写对方会员编号\" \r\n\t\t\t\t\t\t\tplaceholderStyle=\"color:#999;\"\r\n\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\tvalue=\"\" \r\n\t\t\t\t\t\t\tclass=\"ios-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 第二步：选择要赠送的会员 -->\r\n\t\t\t<view class=\"transfer-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<view class=\"step-badge\">2</view>\r\n\t\t\t\t\t<text class=\"step-title\">选择要赠送的会员</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<radio-group class=\"vip-radio-group\" @change=\"vipListChangeTap\">\r\n\t\t\t\t\t\t<view class=\"vip-list\">\r\n\t\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\t\tclass=\"vip-item\" \r\n\t\t\t\t\t\t\t\t:class=\"{active: radio=='radio' + index}\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in info.vip_list\" \r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<label class=\"vip-label\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"vip-info\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"vip-name\">{{ item.name }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"vip-expire\">{{ item.expire_name }}到期</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<radio \r\n\t\t\t\t\t\t\t\t\t\tclass=\"ios-radio\" \r\n\t\t\t\t\t\t\t\t\t\t:class=\"radio=='radio' + index?'checked':''\"\r\n\t\t\t\t\t\t\t\t\t\t:checked=\"radio=='radio' + index?true:false\" \r\n\t\t\t\t\t\t\t\t\t\t:value=\"item.vid\"\r\n\t\t\t\t\t\t\t\t\t></radio>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</radio-group>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 确认按钮 -->\r\n\t\t\t<view class=\"action-container\">\r\n\t\t\t\t<button @tap=\"submitTap\" class=\"ios-button\">确认赠送</button>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 操作说明 -->\r\n\t\t\t<view class=\"transfer-card notice-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<view class=\"notice-icon\">\r\n\t\t\t\t\t\t<text class=\"cuIcon-info\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"step-title\">操作说明</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"notice-list\">\r\n\t\t\t\t\t\t<view class=\"notice-item\" v-for=\"(notice, index) in info.notice_list\" :key=\"index\">\r\n\t\t\t\t\t\t\t<text class=\"notice-text\">{{ notice }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tlet app = getApp();\r\n\tlet that = null;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tinfo: {},\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\tselectVipId: 0,\r\n\t\t\t\tselectUserId: 0,\r\n\t\t\t\tshowUserInfo: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(t) {\r\n\t\t\tthat = this;\r\n\t\t\tapp.globalData.checkAppIsAuditAndRedirect();\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthat.getInfo();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync getInfo() {\r\n\t\t\t\tlet res = await app.globalData.service.vipTransferInfo();\r\n\t\t\t\tthat.info = res.data\r\n\t\t\t\tthat.isLoad = true;\r\n\t\t\t},\r\n\t\t\tuidInputTap(options) {\r\n\t\t\t\tthat.selectUserId = options.detail.value;\r\n\t\t\t},\r\n\t\t\tvipListChangeTap(options) {\r\n\t\t\t\tthat.selectVipId = options.detail.value;\r\n\t\t\t},\r\n\t\t\tsubmitTap() {\r\n\t\t\t\tif (!that.selectUserId) {\r\n\t\t\t\t\tapp.showToast('请先填写用户编号');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!that.selectVipId) {\r\n\t\t\t\t\tapp.showToast('请先选择要赠送的会员');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\tcontent: '确认赠送？',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\t\t\tasync success(res) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\t\t\tlet code = res.code\r\n\t\t\t\t\t\t\t\t\tif (code) {\r\n\t\t\t\t\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\t\t\t\t\tuid: that.selectUserId,\r\n\t\t\t\t\t\t\t\t\t\t\tvid: that.selectVipId,\r\n\t\t\t\t\t\t\t\t\t\t\tcode: code\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tlet res = await app.globalData.service.vipTransfer(data);\r\n\t\t\t\t\t\t\t\t\t\tapp.showToast('操作成功');\r\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.getInfo();\r\n\t\t\t\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tapp.showToast('获取用户身份失败');\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style src=\"./transfer.css\"></style>\r\n"], "sourceRoot": ""}