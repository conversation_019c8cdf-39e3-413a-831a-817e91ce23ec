<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="答题结果"></back>
		<view class="result-container" v-if="isLoad">
			<!-- 成绩卡片 -->
			<view class="result-card bg-white shadow">
				<!-- 成绩圆环 -->
				<view class="score-circle">
					<view class="circle-bg">
						<view class="circle-progress" :style="'transform: rotate(' + (responseData.right_rate * 3.6) + 'deg)'"></view>
					</view>
					<view class="circle-content">
						<text class="circle-percent">{{ responseData.right_rate }}%</text>
						<text class="circle-label">正确率</text>
					</view>
				</view>
				
				<!-- 详细数据 -->
				<view class="result-stats">
					<view class="stat-item">
						<text class="stat-value text-blue">{{ responseData.right_count }}</text>
						<text class="stat-label">做对题数</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-value">{{ responseData.answer_count }}/{{ responseData.all_count }}</text>
						<text class="stat-label">已完成</text>
					</view>
				</view>
				
				<!-- 评价信息 -->
				<view class="result-comment">
					<text class="comment-text" v-if="responseData.right_rate >= 80">太棒了！继续保持！</text>
					<text class="comment-text" v-else-if="responseData.right_rate >= 60">不错的表现，还可以更好！</text>
					<text class="comment-text" v-else>再接再厉，继续努力！</text>
				</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="action-buttons">
				<button @tap="redirectTap" class="cu-btn round bg-gradual-blue shadow lg" data-action="1">
					<text class="cuIcon-home"></text> 题库首页
				</button>
				<button @tap="redirectTap" class="cu-btn round bg-gradual-purple shadow lg margin-top" data-action="2">
					<text class="cuIcon-my"></text> 个人中心
				</button>
			</view>
		</view>
		<adfootbanner unitId="adunit-9f7b1de89ce8659f"></adfootbanner>
	</view>
</template>

<script src="./result.js"></script>
<style src="./result.css"></style>