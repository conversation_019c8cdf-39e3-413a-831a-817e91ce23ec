import {
	get,
	post,
} from "../../common/js/http.js";

/**
 * 服务端Get请求
 */

exports.getRequest = function(t, o) {
	return get(t, o)
};

/**
 * 服务端Post请求
 */
exports.postRequest = function(t, o) {
	return post(t, o);
};


exports.uploadRequest = function(name, file, formData) {
	let that = this;
	return new Promise(function(s, e) {
		uni.showLoading({
				title: '上传中',
				mask: !0
			}),
			uni.uploadFile({
				url: `${that.config.ossHost}/api/fileManage/upload`,
				filePath: file,
				name: 'file',
				formData: formData,
				success: (uploadFileRes) => {
					uni.hideLoading();
					let data = JSON.parse(uploadFileRes.data);
					let statusCode = uploadFileRes.statusCode;
					if (statusCode != 200) {
						getApp().showToast('服务端异常:'.res);
					}
					if (data.code == 0) {
						getApp().showToast(data.message);
					}
					s(data);
				},
				fail: (err) => {
					uni.hideLoading(), getApp().showToast('上传失败'), e(err);
				}
			});
	});

};