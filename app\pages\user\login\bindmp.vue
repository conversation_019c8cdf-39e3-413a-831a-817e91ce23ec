<template>
	<view v-if="isLoad">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="绑定公众号"></back>
		<view>
			<view class="cu-form-group margin-top">
				<view class="title">验证码</view>
				<input @input="inputVerifyCodeTap" maxlength="32" placeholder="请输入公众号验证码" placeholderStyle="color:#999;"
					type="text" :value="user.mp_openid ? user.mp_openid : ''"
					:disabled="user.mp_openid ? true : false" />
			</view>
			<view class="padding">
				<button class="cu-btn block bg-blue margin-tb-sm lg" @tap="bindVerifyTap">确定绑定</button>
			</view>
			<view class="cu-bar bg-white solid-bottom" style="">
				<view class="action">
					<text class="cuIcon-title text-red"></text>
					绑定指南
				</view>
				<view class="action">
					<button class="cu-btn bg-blue shadow" @tap="bindOpenMp"><text>显示公众号二维码</text></button>
				</view>
			</view>
			<view class="cu-list menu">
				<view class="cu-item" v-for="(value, index) in pageSetData.rule" :key="index">
					<view class="content">
						<text class="text-grey">{{value}}</text>
					</view>
				</view>
			</view>
		</view>
		<adfootbanner></adfootbanner>
	</view>
</template>
<script>
	import {
		get,
		post,
	} from "@/common/js/http.js";
	let that = null,
		app = getApp(),
		cache = app.globalData.config.storage;
	let service = app.globalData.service;
	export default {
		data() {
			return {
				configType: 'bind_mp_page_set',
				pageSetData: null,
				isLoad: false,
				verifyCode: '',
				user: [],
				showRuleModal: false
			};
		},
		onLoad() {},
		onShow() {
			that = this;
			let info = cache.getUserInfoData();
			that.isLoad = true;
			that.user = info;
			that.verifyCode = info.mp_openid;
			that.getInfo();
		},
		methods: {
			async getInfo() {
				that.pageSetData = await service.getConfig(that.configType);
			},
			showRuleTap() {
				that.showRuleModal = true;
			},
			inputVerifyCodeTap(options) {
				that.verifyCode = options.detail.value;
			},
			async bindVerifyTap(e) {
				let verifyCode = that.verifyCode;
				if (!verifyCode) {
					app.showToast('验证码不得为空');
					return;
				}
				await post('user/bindMp', {
					code: verifyCode
				})
				uni.navigateBack({
					delta: 1
				});
			},
			bindOpenMp(e) {
				uni.previewImage({
					urls: [that.pageSetData.mp_qrcode_url]
				});
			}
		}
	};
</script>
<style src="./bindemail.css"></style>