
<template>
	<view v-if="!appIsAudit && isLoad" class="vip-container">
		<!-- 使用back组件作为返回按钮 -->
		<back :showBackText="false"  customClass="bg-gradual-blue text-white" :showBack="true" :showTitle="true" title="会员充值"></back>
		
		<!-- 主内容区域 -->
		<view class="content-container">
			<!-- 专业选择区域 -->
			<view class="section profession-section">
				<view class="section-title">
					<text class="icon cuIcon-profile"></text>
					<text>您选择的专业</text>
				</view>
				<view class="profession-card" @tap="toCourseTap">
					<view class="profession-name">{{chargeInfo.professionName}}</view>
					<view class="profession-action">
						<text>查看可用课程</text>
						<text class="cuIcon-right"></text>
					</view>
				</view>
			</view>
			
			<!-- 会员套餐选择区域 -->
			<view class="section package-section">
				<view class="section-title">
					<text class="icon cuIcon-vip"></text>
					<text>选择会员套餐</text>
				</view>
				<view class="package-list">
					<view 
						class="package-item" 
						:class="{active: vipComboId==vip.id}"
						v-for="(vip, index) in chargeInfo.vipComboList" 
						:key="index"
						@tap="onPriceClick"
						:data-index="index"
					>
						<view class="package-price">
							<text class="price-symbol">¥</text>
							<text class="price-value">{{vip.sale_price}}</text>
						</view>
						<view class="package-name">{{vip.name}}</view>
						<view class="package-bonus">
						<text class="bonus-tag">赠送</text>
						<text>{{formatScore(vip.score)}}积分</text>
					</view>
					</view>
				</view>
			</view>
			
			<!-- 充值按钮 -->
			<view class="action-section">
				<button @tap="onChargeCommit" class="charge-btn">立即充值</button>
			</view>
			
			<!-- 充值说明 -->
			<view class="section notice-section">
				<view class="section-title">
					<text class="icon cuIcon-info"></text>
					<text>充值说明</text>
				</view>
				<view class="notice-list">
					<view class="notice-item" v-for="(notice, index) in chargeInfo.vipChargeNoticeList" :key="index">
						<text class="notice-num">{{index+1}}</text>
						<text class="notice-text">{{ notice }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<style>
.vip-container {
	background-color: #f5f7fa;
	min-height: 100vh;
}

.content-container {
	padding: 30rpx;
}

.section {
	margin-bottom: 30rpx;
	border-radius: 12rpx;
	background-color: #ffffff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.section-title {
	padding: 24rpx;
	border-bottom: 1rpx solid #eee;
	display: flex;
	align-items: center;
}

.section-title .icon {
	color: #0081ff;
	margin-right: 16rpx;
	font-size: 36rpx;
}

.section-title text {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

/* 专业选择区域样式 */
.profession-card {
	padding: 30rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.profession-name {
	font-size: 32rpx;
	color: #0081ff;
	font-weight: bold;
}

.profession-action {
	display: flex;
	align-items: center;
	color: #ff6b6b;
	font-size: 26rpx;
}

.profession-action .cuIcon-right {
	margin-left: 8rpx;
}

/* 会员套餐选择区域样式 */
.package-list {
	display: flex;
	flex-wrap: wrap;
	padding: 20rpx;
	justify-content: space-between;
}

.package-item {
	width: 30%;
	padding: 24rpx 0;
	margin-bottom: 20rpx;
	border-radius: 12rpx;
	background-color: #f9fafc;
	border: 2rpx solid #eaeef5;
	display: flex;
	flex-direction: column;
	align-items: center;
	transition: all 0.3s ease;
}

.package-item.active {
	background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
	border: 2rpx solid #0081ff;
	box-shadow: 0 4rpx 16rpx rgba(0, 129, 255, 0.15);
	transform: translateY(-4rpx);
}

.package-price {
	display: flex;
	align-items: baseline;
	margin-bottom: 12rpx;
}

.price-symbol {
	color: #ff6b6b;
	font-size: 28rpx;
	margin-right: 4rpx;
}

.price-value {
	color: #ff6b6b;
	font-size: 40rpx;
	font-weight: bold;
}

.package-name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	font-weight: 500;
}

.package-bonus {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #0081ff;
}

.bonus-tag {
	background-color: rgba(0, 129, 255, 0.1);
	color: #0081ff;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	margin-right: 8rpx;
	font-size: 22rpx;
}

/* 充值按钮样式 */
.action-section {
	padding: 20rpx 40rpx 40rpx;
}

.charge-btn {
	background: linear-gradient(90deg, #0081ff, #1cbbb4);
	color: #ffffff;
	font-size: 32rpx;
	font-weight: bold;
	height: 90rpx;
	line-height: 90rpx;
	border-radius: 45rpx;
	box-shadow: 0 10rpx 20rpx rgba(0, 129, 255, 0.2);
	transition: all 0.3s ease;
}

.charge-btn:active {
	transform: scale(0.98);
	box-shadow: 0 5rpx 10rpx rgba(0, 129, 255, 0.2);
}

/* 充值说明样式 */
.notice-list {
	padding: 20rpx;
}

.notice-item {
	display: flex;
	margin-bottom: 16rpx;
	padding: 10rpx;
	border-radius: 8rpx;
	background-color: #f9fafc;
}

.notice-num {
	width: 40rpx;
	height: 40rpx;
	background-color: #0081ff;
	color: #fff;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 24rpx;
	margin-right: 16rpx;
	flex-shrink: 0;
}

.notice-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
</style>

<script>
let app = getApp();
let that = null;
export default {
	data() {
		return {
			isLoad: false,
			chargeInfo: {},
			vipComboId: 0,
			appIsAudit: false,
			showConfirmNotice: false
		};
	},
	onLoad(options) {
		that = this;
	},
	onShow() {
		that.appIsAudit = app.globalData.checkAppIsAudit();
		if (that.appIsAudit) {
			uni.reLaunch({
				url: '/pages/index/index'
			});
		}
		that.getInfo();
	},
	methods: {
		/**
		 * 格式化积分数值，超过10000的显示为x.x万
		 * @param {Number} score 积分数值
		 * @return {String} 格式化后的积分字符串
		 */
		formatScore(score) {
			if (score >= 10000) {
				return Math.floor(score / 1000) / 10 + '万';
			}
			return score.toString();
		},
		getInfo() {
			app.globalData.server
				.postRequest('user/vip/chargeInfo', {})
				.then(function(res) {
					console.log(res);
					that.isLoad = true;
					that.chargeInfo = res.data;
				})
				.catch(function(err) {
					console.log(err);
				});
		},
		toCourseTap() {
			let url = '/pages/practice/course/course';
			uni.navigateTo({
				url: url
			});
		},
		onPriceClick(options) {
			let key = options.currentTarget.dataset.index;
			let item = that.chargeInfo.vipComboList[key];
			that.vipComboId = item.id;
		},

		/**
		 * 确认充值
		 * @param {Object} options
		 */
		onChargeCommit(options) {
			if (that.vipComboId === 0) {
				app.showToast('请选择充值套餐');
				return;
			}
			uni.showModal({
				title: '充值确认',
				cancelText: '我不同意',
				confirmText: '我同意',
				content: '我已阅读并同意充值协议',
				success: (res) => {
					if (res.confirm) {
						app.globalData.server
							.getRequest('order/create', {
								type: 1,
								vip_comb_id: that.vipComboId
							})
							.then(function(res) {
								let id = res.data.order_id;
								let url = '/pages/pay/pay?id=' + id;
								uni.navigateTo({
									url: url
								});

							})
							.catch(function(res) {
								app.showToast('创建订单失败');
							});
					}
				}
			});
		}
	}
};
</script>