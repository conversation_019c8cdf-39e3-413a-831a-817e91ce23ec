<view><back vue-id="5a332402-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="会员注册" bind:__l="__l"></back><block wx:if="{{!checkAppIsAudit}}"><view><safe-check vue-id="5a332402-2" sid="{{username}}" scene="{{10}}" status="{{showConfirm}}" confirmText="确认输入" confirmTextClass="cuIcon-roundcheck" data-event-opts="{{[['^updateSid',[['__set_sync',['$0','username','$event'],['']]]],['^updateStatus',[['__set_sync',['$0','showConfirm','$event'],['']]]],['^complete',[['safeCheckCompleteTap']]]]}}" bind:updateSid="__e" bind:updateStatus="__e" bind:complete="__e" bind:__l="__l"></safe-check><view class="cu-form-group margin-top"><view class="title">手机号码</view><input maxlength="11" placeholder="请输入手机号" placeholderStyle="color:#999;" type="number" data-event-opts="{{[['input',[['usernameInputTap',['$event']]]]]}}" bindinput="__e"/></view><view class="cu-form-group margin-top"><view class="title">登录密码</view><input maxlength="20" password="{{true}}" placeholder="请输入登录密码" placeholderStyle="color:#999;" type="text" data-event-opts="{{[['input',[['passwordInputTap',['$event']]]]]}}" bindinput="__e"/></view><view class="cu-form-group margin-top"><view class="title">确认密码</view><input maxlength="20" password="{{true}}" placeholder="请输入确认密码" placeholderStyle="color:#999;" type="text" data-event-opts="{{[['input',[['confirmPasswordInputTap',['$event']]]]]}}" bindinput="__e"/></view><view class="cu-form-group margin-top"><view class="title">短信验证</view><input maxlength="6" placeholder="请输入短信验证码" placeholderStyle="color:#999;" type="number" data-event-opts="{{[['input',[['verifyCodeInputTap',['$event']]]]]}}" bindinput="__e"/><button data-event-opts="{{[['tap',[['verifyTap',['$event']]]]]}}" class="cu-btn bg-green shadow" bindtap="__e">{{countDown}}</button></view><block wx:if="{{appPlatform==30}}"><view class="cu-form-group margin-top" style="justify-content:space-evenly;"><switch class="{{['red','text-df',isAgreement?'checked':'']}}" checked="{{isAgreement?true:false}}" color="#e54d42" data-event-opts="{{[['change',[['agreementTap',['$event']]]]]}}" bindchange="__e"></switch><view style="margin-left:0;"><text>我已阅读并同意</text><text class="text-bold"><navigator href="https://admin.5b1.cn/index/agreement/index" target="_blank" class="_a">用户协议</navigator></text>、<text class="text-bold"><navigator href="https://admin.5b1.cn/index/agreement/privacy" target="_blank" class="_a">隐私政策</navigator></text></view></view></block><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['registerTap',['$event']]]]]}}" class="cu-btn bg-blue lg" bindtap="__e">注册</button></view></view></block></view>