<view><back vue-id="68244d58-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="课程详情" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view class="flex flex-direction"><block wx:if="{{!showVideoPlayer}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="video-cover" bindtap="__e"><image src="{{info.cover}}" mode="aspectFill" lazy-load="{{true}}"></image><block wx:if="{{$root.g0>0}}"><view class="video-duration"><text>{{"共"+$root.g1+"课时"}}</text></view></block><block wx:if="{{info.is_hot}}"><view class="video-tag"><text>HOT</text></view></block><block wx:if="{{info.can_play_online==1}}"><view class="play-icon"><text class="cuIcon-playfill"></text></view></block></view></block><block wx:if="{{showVideoPlayer}}"><view class="video-container"><video class="video-player" src="{{currentVideoUrl}}" controls="{{true}}" autoplay="{{true}}"></video></view></block><view class="course-info bg-white padding"><view class="course-title text-bold">{{info.name}}</view><view class="course-desc text-gray">{{info.description}}</view><view class="flex justify-between align-center margin-top-sm"><view><text class="text-red text-xxl text-bold">{{"¥"+info.price}}</text><text class="text-gray text-del margin-left-xs">{{"¥"+info.market_price}}</text></view><block wx:if="{{info.sale_num>0}}"><view class="text-blue"><text class="cuIcon-peoplefill margin-right-xs"></text>{{info.sale_num+'人购买'}}</view></block></view></view><view class="divider-line"></view><view class="cu-bar bg-white"><view class="action" style="width:100%;"><view class="tabs"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{!item.g2}}"><view class="{{['tab-item',currentTab==index?'text-blue cur':'']}}" data-id="{{index}}" data-event-opts="{{[['tap',[['tabSelect',['$event']]]]]}}" bindtap="__e">{{''+item.$orig+''}}</view></block></block></view></view></view><view class="tab-content"><block wx:if="{{currentTab==0}}"><view class="padding bg-white"><rich-text nodes="{{info.content}}"></rich-text></view></block><block wx:if="{{$root.g3}}"><view class="bg-white video-list-container"><scroll-view style="max-height:60vh;" scroll-y="{{true}}"><block wx:for="{{videoList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['cu-item','video-item','solid-bottom',currentVideoIndex===index?'active-video':'']}}" data-id="{{index}}" data-event-opts="{{[['tap',[['selectVideo',['$event']]]]]}}" bindtap="__e"><view class="flex align-center justify-between"><view class="flex align-center" style="flex:1;"><view class="video-index">{{index+1}}</view><view class="flex-sub margin-left-sm" style="max-width:80%;"><view class="text-black text-df" style="display:flex;flex-direction:column;"><text class="text-cut" style="max-width:390rpx;display:inline-block;">{{item.name}}</text><view class="margin-top-xs" style="padding-left:0;"><block wx:if="{{item.is_free==1}}"><view class="free-tag"><text class="cu-tag bg-blue light radius sm" style="vertical-align:middle;">免费体验</text><text class="cuIcon-hotfill text-orange" style="font-size:32rpx;margin-right:4rpx;vertical-align:middle;"></text></view></block><block wx:if="{{item.is_free==0}}"><view class="free-tag"><text class="cu-tag bg-blue light radius sm" style="vertical-align:middle;">付费解锁</text><text class="{{['','text-orange',!info.isPay?'cuIcon-lock':'cuIcon-unlock']}}" style="font-size:32rpx;margin-right:4rpx;vertical-align:middle;"></text></view></block></view></view></view></view><block wx:if="{{currentVideoIndex===index}}"><view class="cuIcon-playfill text-blue" style="font-size:40rpx;margin-right:10rpx;"></view></block><block wx:else><view class="cuIcon-play text-gray" style="font-size:40rpx;margin-right:10rpx;"></view></block></view></view></block><block wx:if="{{$root.g4==0}}"><view class="empty-state-container"><view class="empty-icon"><text class="cuIcon-video text-gray"></text></view><view class="empty-text"><text class="title">暂无视频内容</text></view></view></block></scroll-view></view></block><block wx:if="{{$root.g5}}"><view class="bg-white"><block wx:for="{{materialList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cu-item material-item solid-bottom padding" data-id="{{index}}" data-event-opts="{{[['tap',[['downloadMaterial',['$event']]]]]}}" bindtap="__e"><view class="flex align-center justify-between"><view class="flex align-center"><view class="cuIcon-file text-blue margin-right"></view><view class="text-black">{{item.name}}</view></view><view class="cuIcon-down text-blue"></view></view><view class="text-gray text-sm margin-top-xs">{{item.size+" mb| "+item.type}}</view></view></block><block wx:if="{{$root.g6==0}}"><view class="empty-state-container"><view class="empty-icon"><text class="cuIcon-file text-gray"></text></view><view class="empty-text"><text class="title">暂无课件内容</text></view></view></block></view></block></view><view class="bottom-layout cu-bar bg-white tabbar border shop"><button class="action text-blue" open-type="share"><view class="cuIcon-forwardfill"></view><text class="text-sm text-blue">分享</text></button><button class="action text-blue" open-type="contact"><view class="cuIcon-servicefill"></view><text class="text-sm text-blue">客服</text></button><block wx:if="{{!info.isPay}}"><view data-event-opts="{{[['tap',[['buyCourse',['$event']]]]]}}" class="bg-red flex-twice radius" bindtap="__e"><view class="padding-sm text-center"><text class="text-white">立即购买</text></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['startLearning',['$event']]]]]}}" class="bg-blue flex-twice radius" bindtap="__e"><view class="padding-sm text-center"><text class="text-white">{{info.can_play_online==0?'立即学习':'立即学习'}}</text></view></view></block></view></view></block><block wx:if="{{showServiceQrcode}}"><view class="qrcode-modal"><view class="qrcode-container"><view class="qrcode-header"><text class="qrcode-title">课程观看指引</text><text data-event-opts="{{[['tap',[['closeQrcodeModal',['$event']]]]]}}" class="cuIcon-close" bindtap="__e"></text></view><view class="qrcode-content"><view class="qrcode-tips"><text class="cuIcon-info text-blue"></text><block wx:if="{{page_config.customerServiceGuideTip}}"><text class="tips-text">{{page_config.customerServiceGuideTip}}</text></block></view><image class="qrcode-image" src="{{serviceQrcode}}" mode="aspectFit" show-menu-by-longpress="{{true}}"></image><view class="qrcode-instruction">长按二维码添加客服</view></view><view class="qrcode-footer"><button data-event-opts="{{[['tap',[['closeQrcodeModal',['$event']]]]]}}" class="cu-btn bg-blue" bindtap="__e">我知道了</button></view></view></view></block></view>