let app = getApp(),
	that = null,
	rewardedVideoAd = null;
export default {
	data() {
		return {
			taskDescModal: false,
			taskTitle: '',
			taskContent: '',
			info: [],
			isLoad: false,
			borderWidth: 1,
			appIsAudit: false
		};
	},
	onLoad(options) {
		that = this;
		that.borderWidth = app.globalData.isIOS ? 2 : 1;
		if (app.globalData.appPlatform == 20) {
			rewardedVideoAd = that.setRewardVideoAd(that);
			rewardedVideoAd.onClose((res) => {
				that.rewardConfirmSign(res);
			});
		}
	},
	onShow() {
		that.appIsAudit = app.globalData.checkAppIsAudit();
		that.getSignInfo();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		confirmSign() {
			if (app.globalData.appPlatform != 20) {
				app.showToast('请在小程序中进行签到');
				return;
			}
			uni.login({
				success: function(e) {
					let code = e.code
					app.globalData.server
						.postRequest('user/sign/confirm', {
							code: code
						})
						.then((ret) => {
							if (ret.code == 1) {
								that.getSignInfo();
								setTimeout(function() {
									app.showToast(ret.message);
								}, 1500);
							}
						})
						.catch((err) => {
							console.log(err);
						});
				}
			});

		},
		rewardConfirmSign(res) {
			if (app.globalData.appPlatform != 20) {
				app.showToast('请在小程序手机版中使用');
				return;
			}
			if (res && res.isEnded) {
				let api = 'user/sign/rewardConfirm';
				uni.login({
					success(res) {
						if (res.code) {
							app.globalData.server
								.postRequest(api, {
									code: res.code
								})
								.then(function(ret) {
									if (ret.code == 1) {
										that.getSignInfo();
										app.showToast('已赠送积分');
									} else {
										app.showToast(ret.message);
									}
								})
								.catch(function(res) {
									console.log(err);
								});
						} else {
							app.showToast('获取用户信息失败');
						}
					}
				});
			}
		},
		getSignInfo() {
			app.globalData.server
				.getRequest('user/sign/info', {})
				.then(function(t) {
					that.setData({
						info: t.data,
						isLoad: true
					});
				})
				.catch(function(err) {
					console.log(err);
				});
		},
		bindMpTap() {
			uni.navigateTo({
				url: '../../user/login/bindmp?from=2'
			});
		},
		bindWxGroupTap() {
			uni.navigateTo({
				url: '../../user/group/group'
			});
		},
		bindWeiXinTap() {
			uni.navigateTo({
				url: '../../user/login/bindopenid?from=2'
			});
		},
		bindMobileTap() {
			uni.navigateTo({
				url: '../../user/login/bindmobile'
			});
		},
		bindEmailTap() {
			uni.navigateTo({
				url: '../../user/login/bindemail'
			});
		},

		bindShareTap() {
			that.setData({
				taskTitle: '分享小程序',
				taskContent: '转发小程序到微信群，有人点击即可获得对应积分，拉新才能获得更多积分。',
				taskDescModal: true,
			});
		},
		bindShareFileTap() {
			that.setData({
				taskTitle: '分享学习资料',
				taskContent: '在课程导航/学习资料栏目中上传分享高质量学习资料，通过管理人员的审核后可获得对应奖励。',
				taskDescModal: true,
			});
		},
		bindAddDeskTap() {
			that.setData({
				taskTitle: '添加小程序到手机桌面',
				taskContent: '点击小程序右上角三个点的图标，点击添加到桌面，然后从手机桌面点击进入小程序，积分自动到账。（通过桌面启动小程序自动关闭启动广告）',
				taskDescModal: true,
			});
		},

		bindAddMyAppTap() {
			that.setData({
				taskTitle: '添加到我的小程序',
				taskContent: '点击小程序右上角三个点的图标，点击添加到我的小程序，然后从微信->小程序->我的小程序中进入小程序，积分自动到账',
				taskDescModal: true,
			});
		},
		bindVideoTap() {
			if (app.globalData.appPlatform != 20) {
				app.showToast('请在小程序中使用');
				return;
			}
			rewardedVideoAd.show().catch(() => {
				rewardedVideoAd
					.load()
					.then(() => rewardedVideoAd.show())
					.catch((err) => {
						app.showToast('广告显示失败');
					});
			});
		},
		setRewardVideoAd(page) {
			let rewardedVideoAd = null;
			if (uni.createRewardedVideoAd) {
				rewardedVideoAd = uni.createRewardedVideoAd({
					adUnitId: 'adunit-ce5097dd8107e1a9'
				});
				rewardedVideoAd.onError((err) => {
					app.showToast('广告加载失败');
				});
			}
			return rewardedVideoAd;
		},
	}
};