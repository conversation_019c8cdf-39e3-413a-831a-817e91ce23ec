let that = null;
let app = getApp();
import {
	post
} from "@/common/js/http.js";

export default {
	data() {
		return {
			load: false,
			info: [],
			steps: [{
					title: "登录官网",
					content: "使用浏览器访问官方网站 %s ，扫码登录 ，进入创作中心。"
				},
				{
					title: "创建题库",
					content: "点击【我的题库】，创建一个题库。"
				},
				{
					title: "创建分类",
					content: "点击题库右侧的分类按钮，为题库创建分类，例如【历年真题】"
				},
				{
					title: "导入试卷",
					content: "点击【我的试卷】，创建一个试卷，例如'2025年考试真题'。选中该试卷后，点击导入题目，按照导入模板导入Excel即可。"
				},
				{
					title: "绑定分类",
					content: "在【我的试卷】中，选中刚才创建的试卷，例如'2025年考试真题'，将其绑定到对应分类中，例如绑定到【历年真题】分类中"
				},
				{
					title: "查看题目",
					content: "在微信小程序的【我的题库】中查看导入的题目，即可免费练习。"
				}
			]
		};
	},
	onLoad() {
		that = this;
		that.getInfo();
	},
	onShow() {},
	methods: {
		async getInfo() {
			let res = await post('appConfig/get', {
				config_type: 'upload_question'
			});
			if (res.data && res.data.host) {
				var content = that.steps[0].content;
				that.steps[0].content = content.replace("%s", res.data.host);
			}
			console.log(that.steps);
			that.steps = that.steps;
			that.info = res.data;
			that.load = true;
		}
	}
};