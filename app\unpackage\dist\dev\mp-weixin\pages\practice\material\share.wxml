<view><back vue-id="5f2fa638-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="分享资料" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view><view class="cu-bar bg-white solid-bottom"><view class="action"><text class="cuIcon-infofill text-blue"></text>分享指引</view><view class="action"><button data-event-opts="{{[['tap',[['showRuleTap',['$event']]]]]}}" class="cu-btn bg-green shadow" bindtap="__e">查看</button></view></view><view class="cu-form-group"><view class="title">课程名称</view><input name="input" disabled="true" value="{{info.name}}"/></view><block wx:if="{{info.code}}"><view class="cu-form-group"><view class="title">课程代码</view><input name="input" disabled="true" value="{{info.code}}"/></view></block><view class="cu-form-group"><textarea maxlength="128" placeholder="{{ruleInfo.shareContentTip}}" data-event-opts="{{[['input',[['shareContentTap',['$event']]]]]}}" bindinput="__e"></textarea></view><view class="box"><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['submitTap',['$event']]]]]}}" class="cu-btn bg-blue lg shadow-blur" bindtap="__e">立即提交</button></view><view class="padding-left padding-right flex flex-direction"><button data-event-opts="{{[['tap',[['recordTap',['$event']]]]]}}" class="cu-btn bg-grey shadow-blur" bindtap="__e">查看已提交记录</button></view></view><confirm vue-id="5f2fa638-2" title="分享指引" content="{{ruleInfo.content}}" status="{{showRuleModal}}" confirmText="我知道啦" confirmTextClass="cuIcon-roundcheck" data-event-opts="{{[['^updateStatus',[['__set_sync',['$0','showRuleModal','$event'],['']]]]]}}" bind:updateStatus="__e" bind:__l="__l"></confirm></view></block></view>