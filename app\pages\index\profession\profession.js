let app = getApp();
let that = null;

export default {
  data() {
    return {
      customBar: this.CustomBar,
      currentCity: {},
      currentExam: {},
      currentSchool: {},
      load: false,
      cateData: [],
      listData: [],
      allListData: [],
      cateId: 0,
      cate: {
        id: '',
        name: ''
      },

      professionItem: {
        name: '',
        old_name: ''
      }
    };
  },
  onReady() {

  },
  onLoad(options) {
    that = this;
    that.initCityAndExam(options);
    that.getProfessionList();
  },
  methods: {
    initCityAndExam(options) {
      that.currentCity = app.globalData.config.storage.getCurrentCityData();
      that.currentExam = app.globalData.config.storage.getCurrentExamData();
      if (options.hasOwnProperty('exam_id')) {
        that.currentExam = {
          id: options.exam_id,
          name: options.exam_name
        };
      }
      if (options.hasOwnProperty('school_id')) {
        that.currentSchool = {
          id: options.school_id,
          name: options.school_name
        };
        that.title = options.school_name;
      } else {
        that.currentSchool = {
          id: 0,
          name: ''
        };
      }
    },
    getProfessionList() {
      app.globalData.server
          .getRequest('profession/get', {
            exam_id: that.currentExam.id,
            province_id: that.currentCity.id,
            school_id: that.currentSchool.id
          })
          .then(function(res) {
            that.load = true;
            that.cateData = res.data.new_cate;
            that.allListData = res.data.list;

            if (that.allListData) {
              that.setListData();
            }
          });
    },

    setListData() {
      let cateId = that.cateId;
      let allListData = that.allListData;

      if (cateId == 0 && that.cateData.length > 0) {
        cateId = that.cateData['0'].id;
        that.cateId = cateId;
      }

      if (cateId == 0) {
        that.listData = allListData;
      } else {
        let newListData = [];
        for (var i = 0; i < allListData.length; i++) {
          if (allListData[i].category_id == cateId) {
            newListData.push(allListData[i]);
          }
        }
        that.listData = newListData;
      }
    },

    /**
     * 选中专业事件
     */
    async professionTap(options) {
      let professionData = options.currentTarget.dataset.data;
      app.globalData.config.storage.setFristData(2);
      app.globalData.config.storage.setCurrentExamData(that.currentExam);
      app.globalData.config.storage.setCurrentProfessionData(professionData);
      uni.reLaunch({
        url: '../index'
      });
    },

    /**
     * 专业类别切换事件
     */
    changeTap(options) {
      let id = options.currentTarget.dataset.id;
      that.cateId = id;
      that.setListData();
    }
  }
}; 