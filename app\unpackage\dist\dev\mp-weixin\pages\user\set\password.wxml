<view><back vue-id="0bf675ba-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="修改密码" bind:__l="__l"></back><view><view class="cu-form-group margin-top"><view class="title">输入密码</view><input maxlength="32" placeholder="请输入登录密码" type="password" data-event-opts="{{[['input',[['passwordInputTap1',['$event']]]]]}}" bindinput="__e"/></view><view class="cu-form-group"><view class="title">确认密码</view><input maxlength="32" placeholder="请输入确认密码" type="password" data-event-opts="{{[['input',[['passwordInputTap2',['$event']]]]]}}" bindinput="__e"/></view><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['setPasswordTap',['$event']]]]]}}" class="cu-btn bg-blue lg" bindtap="__e">提交密码</button></view><view class="cu-form-group bg-white solid-bottom margin-bottom"><view class="action"><text class="cuIcon-title text-blue"></text>提示：设置密码后使用手机号|邮箱+密码登录</view></view></view><adfootbanner vue-id="0bf675ba-2" unitId="adunit-1fb0622d11b3c262" bind:__l="__l"></adfootbanner></view>