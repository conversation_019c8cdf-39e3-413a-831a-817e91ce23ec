let app = getApp();
let that = null;
export default {
	data() {
		return {
			id: 0,
			feedbackContent: '',
			feedbackType: '',
			feedbackTypes: ['题目有错别字', '选项不合理', '答案解析不清晰或不合理', '题目分类不正确', '其他']
		};
	},
	onLoad(options) {
		that = this;
		that.id = options.id;
	},
	methods: {
		bindContentInput: (e) => {
			that.feedbackContent = e.detail.value;
		},

		feedbackTypeTap: (e) => {
			let t = e.currentTarget.dataset.feedbacktype;
			that.feedbackType = t;
		},

		commitTap() {
			if ('' != this.feedbackType) {
				app.globalData.server
					.postRequest('question/feedback', {
						question_id: that.id,
						content: that.feedbackType + '->' + that.feedbackContent
					})
					.then((e) => {
						uni.showToast({
							title: '反馈成功!',
							mask: true,
							complete: () => {
								setTimeout(() => {
									uni.navigateBack();
								}, 1500);
							}
						});
					})
					.catch((t) => {
						app.showToast('反馈失败');
						console.log(t);
					});
			} else {
				uni.showToast({
					icon: 'none',
					title: '请选择反馈类型'
				});
			}
		}
	}
}; 