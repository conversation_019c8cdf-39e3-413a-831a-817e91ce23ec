<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="个人资料"></back>
		<view v-if="isLoad" class="flex flex-direction" style="min-height: 100vh;">
			<view class="flex-sub">
				<view class="cu-bar bg-white">
					<view class="action sub-title">
						<text class="text-xl text-bold text-blue text-shadow">账号信息</text>
						<text class="text-ABC text-blue">BasicInfo</text>
					</view>
					<view class="action" v-if="appPlatform==30">
						<button class="cu-btn sm bg-grey shadow" @tap="logoutTap(1)"
							data-target="menuModal">注销账号</button>
					</view>
				</view>
				<view class="cu-list menu solid-top ">
					<view class="cu-item arrow" @tap="editInfoTap">
						<view class="content">
							<text class="cuIcon-my text-green"></text>
							<text class="text-black">头像</text>
						</view>
						<view class="action">
							<view class="cu-avatar round margin" :style="'background-image:url('+user.avatar+');'">
							</view>
						</view>
					</view>
					<view class="cu-item arrow" @tap="editInfoTap">
						<view class="content">
							<text class="cuIcon-wenzi text-green"></text>
							<text class="text-black">昵称</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{ user.nickname }}</view>
						</view>
					</view>
					<view class="cu-item solid-top">
						<view class="content">
							<text class="cuIcon-cuIcon text-green"></text>
							<text class="text-black">编号</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{ user.id }}</view>
						</view>
					</view>
					<view class="cu-item arrow" v-if="user.mobile" @tap="editInfoTap">
						<view class="content">
							<text class="cuIcon-mobile text-green"></text>
							<text class="text-black">手机</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{ user.mobile }}</view>
						</view>
					</view>
					<view class="cu-item arrow" @tap="bindMailTap">
						<view class="content">
							<text class="cuIcon-mail text-green"></text>
							<text class="text-black">邮箱</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{ user.email }}</view>
						</view>
					</view>
				</view>
				<view class="cu-bar bg-white solid-top solid-bottom" v-if="user.vip_list.length>0">
					<view class="action sub-title">
						<text class="text-xl text-bold text-blue text-shadow">会员信息</text>
						<text class="text-ABC text-blue">VipInfo</text>
					</view>
					<view class="action">
						<button class="cu-btn bg-green shadow" @tap="transferTap" data-target="menuModal">赠送会员</button>
					</view>
				</view>
				<view style="" class="cu-list menu" v-for="(item, index) in user.vip_list" :key="index">
					<view class="cu-item">
						<view class="content">
							<text class="cuIcon-vip text-green"></text>
							<text class="text-black">专业名称</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{ item.name }}</view>
						</view>
					</view>
					<view class="cu-item" v-if="item.layer_name!=''">
						<view class="content">
							<text class="cuIcon-vip text-green"></text>
							<text class="text-black">专业标识</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{ item.layer_name }}</view>
						</view>
					</view>
					<view class="cu-item">
						<view class="content">
							<text class="cuIcon-time text-green"></text>
							<text class="text-black">到期时间</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{ item.expire_name }}</view>
						</view>
					</view>
				</view>
				<view v-if="appPlatform==30">
					<view class="cu-bar bg-white solid-top solid-bottom">
						<view class="action sub-title">
							<text class="text-xl text-bold text-blue text-shadow">授权信息</text>
							<text class="text-ABC text-blue">Other</text>
						</view>
					</view>
					<view class="cu-list menu solid-top">
						<view class="cu-item">
							<view class="content">
								<text class="cuIcon-write text-gray"></text>
								<text class="text-black">用户协议</text>
							</view>
							<view class="action">
								<view class="cu-tag round bg-green light"><a style="text-decoration: none"
										href="https://admin.5b1.cn/index/agreement/index" target="_blank">查看协议</a>
								</view>
							</view>
						</view>
						<view class="cu-item">
							<view class="content">
								<text class="cuIcon-safe text-gray"></text>
								<text class="text-black">隐私政策</text>
							</view>
							<view class="action">
								<view class="cu-tag round bg-green light"><a style="text-decoration: none"
										href="https://admin.5b1.cn/index/agreement/privacy" target="_blank">查看协议</a>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="padding flex flex-direction">
					<button @tap="logoutTap(2)" class="cu-btn bg-blue button-hover lg">退出登录</button>
				</view>
			</view>
		</view>
		<adfootbanner></adfootbanner>
	</view>
	</view>
</template>

<style src="./info.css"></style>
<script src="./info.js"></script>