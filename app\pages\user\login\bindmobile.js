let that = null;
let app = getApp();
let cache = app.globalData.config.storage;
import { post } from "@/common/js/http.js";
import { isSet } from "@/common/js/util.js";

export default {
	data() {
		return {
			user: {},
			mobile: '',
			verifyCode: '',
			checkAppIsAudit: true,
			showConfirm: false,
			countDown: '',
			isBind: false,
		};
	},
	onLoad(options) {
		that = this;
		that.checkAppIsAudit = app.globalData.checkAppIsAudit();
		app.globalData.checkAppIsAuditAndRedirect();
	},
	onShow() {
		let user = cache.getUserInfoData();
		that.user = user;
		that.isBind = user.mobile != ''
		that.countDown = !that.isBind ? '发送验证码' : '已绑定'
		if (that.isBind) {
			app.showToast('您已绑定手机号码');
		}
	},
	methods: {
		mobileInputTap(options) {
			that.mobile = options.detail.value;
		},
		verifyCodeInputTap(options) {
			that.verifyCode = options.detail.value;
		},
		verifyTap() {
			try {
				if (that.isCountDowning === true) {
					app.showToast('请不要频繁操作');
					return;
				}
				that.verifyForm();
				that.showConfirm = true;
			} catch (exception) {
				app.showToast(exception.message);
			}
		},
		async safeCheckCompleteTap(options) {
			if (options.action != 1) {
				return;
			}
			if (that.isCountDowning === true) {
				that.showToast('请不要频繁操作');
				return;
			}

			// 倒计时
			let time = 60;
			let func = function() {
				that.isCountDowning = true;
				app.startInterval(function() {
					time--;
					that.countDown = `${time}s`;
					if (time <= 0) {
						app.stopInterval();
						that.countDown = '重新发送';
						that.isCountDowning = false;
					}
				}, 1000);
			};
			func();
		},
		verifyForm() {
			if (that.user.mobile != '') {
				throw new Error('您已绑定过手机号');
			}
			if (that.mobile == '') {
				throw new Error('请输入手机号码');
			}
		},
		async bindTap(confirm) {
			try {
				that.verifyForm();
				await app.globalData.service.bindMobile({
					code: that.verifyCode,
					mobile: that.mobile
				});
				app.showToast('绑定成功');
				that.countDown = '';
				setTimeout(() => {
					uni.navigateTo({
						url: '../../user/user'
					});
				}, 1500);
			} catch (exception) {
				app.showToast(exception.message);
			}
		},
		// 微信手机号快捷绑定
		wxPhoneBindTap(options) {
			if (!isSet(options.detail.code)) {
				return;
			}
			uni.login({
				success(res) {
					let data = {
						code: res.code,
						wx_phone_code: options.detail.code
					};
					post('user/bindWxMobile', data)
						.then((t) => {
							if (t.code == 1) {
								app.showToast('绑定成功');
								setTimeout(() => {
									uni.navigateTo({
										url: '../../user/user'
									});
								}, 1500);
							} else {
								app.showToast(t.message);
							}
						})
						.catch((t) => {
							app.showToast('微信手机号绑定异常');
						});
				}
			});
		},
		setPassTap() {
			uni.navigateTo({
				url: '../../user/set/password'
			});
		}
	},
};