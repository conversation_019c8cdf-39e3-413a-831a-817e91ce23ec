<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<view v-if="showAd" style="position: fixed; bottom: 0; width: 100%;padding:0;">
			<ad :unit-id="unitId"></ad>
		</view>
		<!-- #endif -->
	</view>
</template>
<script>
	const app = getApp();
	export default {
		name: 'adfootbanner',
		data() {
			return {
				showAd: false
			};
		},
		props: {
			unitId: {
				type: String,
				default: 'adunit-1fb0622d11b3c262'
			}
		},
		methods: {},
		created() {
			let checkHasVip = app.checkHasVip();
			if (!checkHasVip) {
				this.showAd = true;
			}
		}
	};
</script>