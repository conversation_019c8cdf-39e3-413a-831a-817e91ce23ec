page {
	background: #f2f2f2;
}

.UCenter-bg {
	height: 550rpx;
	display: flex;
	justify-content: center;
	padding-top: 40rpx;
	overflow: hidden;
	position: relative;
	flex-direction: column;
	align-items: center;
	color: #fff;
	font-weight: 300;
	text-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.UCenter-bg image {
	width: 200rpx;
	height: 200rpx;
}

.UCenter-bg .gif-wave {
	position: absolute;
	width: 100%;
	bottom: 0;
	left: 0;
	z-index: 99;
	mix-blend-mode: screen;
	height: 100rpx;
}

.menu-image {
	position: relative;
	display: block;
	margin-top: 12rpx;
	width: 52rpx;
	height: 52rpx;
}

button::after {
	border: none;
}

.cu-list.grid {
	padding: 20rpx 10rpx;
}

.cu-list.grid>.cu-item {
	padding: 20rpx 0;
}

.cu-list.grid>.cu-item text {
	font-size: 28rpx;
	margin-top: 10rpx;
}

.text-xxl {
	font-size: 46rpx !important;
}

.margin-top-sm {
	font-size: 26rpx;
}