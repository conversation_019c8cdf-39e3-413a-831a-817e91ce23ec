<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="绑定微信"></back>
		<view class="padding flex flex-direction margin-top">
			<button @tap="bindopenidTap" class="cu-btn bg-gradual-green lg ">绑定微信，一键登录</button>
			<button @tap="rebackTap" class="cu-btn bg-grey margin-tb-sm lg "
				style="margin-top: 80rpx">暂时没空，下次再说</button>
		</view>
		<adfootbanner unitId="adunit-9f7b1de89ce8659f"></adfootbanner>
	</view>
</template>

<script>
	var that = null;
	var app = getApp();
	export default {
		data() {
			return {
				from: 0
			};
		},
		onLoad: function(options) {
			that = this;
			that.from = options.from;
		},
		methods: {
			async bindopenidTap() {
				uni.login({
					success: async function(res) {
						let response = await app.globalData.service.bindOpenId({
							code: res.code
						});
						if (response.code == 1) {
							that.rebackTap();
						}
					}
				});
			},
			rebackTap: function() {
				let url = that.from == 1 ? '../../index/city/city' : '../../user/user';
				uni.reLaunch({
					url: url
				});
			},
		}
	};
</script>
<style src="./bindopenid.css"></style>
