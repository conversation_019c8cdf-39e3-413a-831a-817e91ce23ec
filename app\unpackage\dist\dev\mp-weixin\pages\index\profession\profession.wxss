/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: #fff;
}
/* 只显示返回栏时高度更�?*/
.fixed-header.no-nav {
  height: 100rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
}
/* 可滚动内容区域样�?*/
.scrollable-content {
  padding-top: var(--content-padding-top, 280rpx); /* 使用CSS变量，便于动态调�?*/
  box-sizing: border-box;
}
/* 无导航时，内容区padding-top更小 */
.scrollable-content.no-nav-content {
  padding-top: 100rpx;
}
/* 导航栏样式优�?*/
.nav {
  white-space: nowrap;
  border-bottom: 1rpx solid #eee;
}
.nav .cu-item {
  height: 90rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
/* 固定导航栏样�?*/
.nav-fixed {
  position: fixed;
  width: 100%;
  z-index: 1000;
  border-bottom: 1rpx solid #eee;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
/* Tab容器样式 */
.tab-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 90rpx;
}
/* Tab项目样式 */
.tab-item {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  position: relative;
  transition: all 0.3s ease;
  max-width: 250rpx;
}
/* 激活状态的Tab */
.tab-item.active {
  color: #f37b1d;
  font-weight: bold;
}
/* 激活状态的下划�?*/
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #f37b1d;
  border-radius: 3rpx;
}
