let app = getApp();
let that = null;
export default {
	data() {
		return {
			page: 1,
			load: false,
			isFinish: false,
			listData: []
		};
	},
	onLoad: function(options) {
		(that = this).getList();
	},
	onShow: function() {

	},
	onReachBottom: function() {
		that.page++;
		if (!that.isFinish) {
			that.getList();
		}
	},
	onShareAppMessage: function() {},
	methods: {
		navTap(options) {
			let index = options.currentTarget.dataset.index;
			that.selectNav.index = index;
		},
		getList() {
			app.globalData.server
				.getRequest('promote/mySubordinate', {
					page: that.page
				})
				.then(function(res) {
					uni.stopPullDownRefresh();
					that.load = true;
					if (res.data.length > 0) {
						that.listData = that.listData.concat(res.data);
					} else {
						that.isFinish = true;
					}
				})
				.catch(function(a) {
					app.showToast('加载数据失败');
				});
		}
	}
};