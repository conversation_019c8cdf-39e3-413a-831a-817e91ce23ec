<template>
	<view>
		<view class="cu-custom" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar fixed" :style="style" :class="[bgImage!=''?'none-bg text-white bg-img':'',bgColor]">
				<view class="action" v-if="isBack">
					<!--  #ifndef  MP-BAIDU -->
					<view class="action" @tap="BackPage" style="margin-left: 0;">
						<text class="cuIcon-back"></text>
						<slot name="backText"></slot>
					</view>
					<!--  #endif -->
					
					<!--  #ifndef  MP-BAIDU -->
					<view class="action" @tap="HomePage" v-if="isHome" style="margin-left: 35rpx;">
						<text class="cuIcon-home"></text>
					</view>
					<!--  #endif -->
					
				</view>
				<view class="new_action" v-if="isNewHome">
					<!--  #ifndef  MP-BAIDU -->
					<view class="action border-custom" v-if="isNewHome" style="width: 180rpx;margin-left: 10rpx;">
						<text @tap="BackPage" class="cuIcon-back"></text>
						<text @tap="HomePage" class="cuIcon-homefill"></text>
					</view>
					<!--  #endif -->
				</view>
				<view class="content" :style="[{top:StatusBar + 'px'}]">
					<slot name="content"></slot>
				</view>
				<slot name="right"></slot>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar
			};
		},
		created: () => {
			// #ifdef MP-BAIDU
			uni.setNavigationBarColor({
				frontColor: '#ffffff',
				backgroundColor: '#ff0000',
			})
			// #endif
		},
		computed: {
			style() {
				var StatusBar = this.StatusBar;
				var CustomBar = this.CustomBar;
				var bgImage = this.bgImage;
				var style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
				if (this.bgImage) {
					style = `${style}background-image:url(${bgImage});`;
				}
				return style
			}
		},
		props: {
			bgColor: {
				type: String,
				default: ''
			},
			isBack: {
				type: Boolean,
				default: false
			},
			isHome: {
				type: Boolean,
				default: false
			},
			isNewHome: {
				type: Boolean,
				default: false
			},
			bgImage: {
				type: String,
				default: ''
			},
		},
		methods: {
			BackPage() {
				uni.navigateBack({
					delta: 1
				});
			},
			HomePage() {
				uni.reLaunch({
					url: '/pages/index/index'
				});
			},
		}
	}
</script>