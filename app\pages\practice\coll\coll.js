let app = getApp();
let that = null;
export default {
	data() {
		return {
			isLoad: false,
			course_id: 0,
			isAllScreen: app.globalData.isAllScreen,
			listData: [],
			selectModel: ['收藏题目', '收藏课程'],
			selectModelIndex: 0,
		};
	},
	onLoad(options) {
		that = this;
		const {
			course_id = 0
		} = options || {};
		that.course_id = course_id;
	},
	onShow() {
		that.getColl();
	},
	methods: {
		getColl() {
			app.globalData.server
				.getRequest('user/collect', {
					type: that.selectModelIndex,
					course_id: that.course_id
				})
				.then(t => {
					that.isLoad = true;
					that.listData = t.data;
				})
				.catch(a => {
					app.showToast('获取收藏列表失败');
				});
		},
		selectModelTap(options) {
			that.isLoad = false;
			that.selectModelIndex = options.currentTarget.dataset.id;
			that.getColl();
		},
		courseTap(options) {
			let url = '';
			let item = options.currentTarget.dataset.item;
			console.log(item)
			if (that.selectModelIndex == 0) {
				//收藏题
				url = `../topic/topic?mainType=2&title=【收藏】${item.name}&id=${item.course_id}`;
			} else {
				url = `/pages/practice/course/detail?id=${item.course_id}`;
			}
			uni.navigateTo({
				url: url
			});
		}
	}
}; 