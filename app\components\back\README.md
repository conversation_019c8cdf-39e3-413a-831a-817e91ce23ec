# Back Component

一个通用的返回和主页导航组件，支持自定义返回事件。

## 基本用法

```vue
<back title="页面标题"></back>
```

## 自定义返回事件

可以通过监听 `beforeBack` 事件来在返回前执行自定义逻辑：

```vue
<template>
  <back 
    title="成交订单" 
    customClass="bg-gradual-red text-white" 
    :showBackText="false"
    @beforeBack="handleBeforeBack"
  ></back>
</template>

<script>
export default {
  methods: {
    handleBeforeBack() {
      // 在返回前执行自定义逻辑
      console.log('返回前执行的自定义逻辑');
      
      // 如果需要阻止默认返回行为，可以设置 useDefaultBack 为 false
      // <back @beforeBack="handleBeforeBack" :useDefaultBack="false"></back>
      // 然后在自定义方法中手动控制返回行为
    }
  }
}
</script>
```

## 完全自定义返回行为

如果需要完全自定义返回行为，可以禁用默认返回并自行处理：

```vue
<template>
  <back 
    title="成交订单" 
    customClass="bg-gradual-red text-white" 
    :showBackText="false"
    :useDefaultBack="false"
    @beforeBack="customBackAction"
  ></back>
</template>

<script>
export default {
  methods: {
    customBackAction() {
      // 执行自定义逻辑
      console.log('执行自定义返回逻辑');
      
      // 例如，显示确认对话框
      uni.showModal({
        title: '提示',
        content: '确定要离开当前页面吗？',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确定，执行返回
            uni.navigateBack({
              delta: 1
            });
          }
        }
      });
    }
  }
}
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| showBack | Boolean | true | 是否显示返回按钮 |
| showHome | Boolean | true | 是否显示主页按钮 |
| showBackLeft | Boolean | true | 是否显示左侧返回区域 |
| showBackIcon | Boolean | true | 是否显示返回图标 |
| showHomeIcon | Boolean | true | 是否显示主页图标 |
| showBackText | Boolean | true | 是否显示返回文本 |
| backText | String | '返回' | 返回按钮文本 |
| customClass | String | '' | 自定义样式类 |
| homeUrl | String | '/pages/index/index' | 主页路径 |
| showTitle | Boolean | true | 是否显示标题 |
| title | String | '标题' | 标题文本 |
| useDefaultBack | Boolean | true | 是否使用默认返回行为 |

## 事件

| 事件名 | 说明 |
|-------|------|
| beforeBack | 返回前触发的事件，可用于执行自定义逻辑 | 