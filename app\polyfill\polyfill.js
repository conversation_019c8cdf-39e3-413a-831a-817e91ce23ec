/**
 * 获取guid
 */
function guid () {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
    })
}

/**
 * 检查api是否未实现，没实现返回true
 * @param {Object} api
 */
function isApiNotImplemented (api) {
    return uni[api] === undefined || [api] && uni[api].toString().indexOf("is not yet implemented") > -1
}

/**
 * 条件编译
 */
function platformPolyfill () {
    // #ifdef APP-PLUS
    uni.showNavigationBarLoading = function () {
        console.warn("api: uni.showNavigationBarLoading 在App平台会在屏幕中间悬浮loading，不如直接去掉")
    }
    // #endif
}


/**
 * 登录相关api polyfill
 */
function loginPolyfill () {
    if (isApiNotImplemented("login")) {
        uni.login = function (options) {
            console.warn("api: uni.login 登录 在当前平台不支持，【关键流程函数】 回调成功")
            options.success && options.success({
                code: guid(),
                errMsg: "login:ok"
            })
        }
    }

    if (isApiNotImplemented("checkSession")) {
        uni.checkSession = function (options) {
            console.warn("api: uni.checkSession 检查登录状态是否过期 在当前平台不支持，【关键流程函数】 回调成功")
            options.success && options.success()
        }
    }

    if (isApiNotImplemented("getUserInfo")) {
        uni.getUserInfo = function (options) {
            console.warn("api: uni.getUserInfo 获取用户信息 在当前平台不支持，【关键流程函数】回调成功")
            options.success && options.success({
                userInfo: ""
            })
        }
    }
    if (isApiNotImplemented("getUserProfile")) {
        uni.getUserProfile = function (options) {
            console.warn("api: uni.getUserProfile 获取用户授权信息 在当前平台不支持，【关键流程函数】回调成功")
            options.success && options.success({
                userInfo: ""
            })
        }
    }
}

/**
 * 媒体相关
 */
function mediaPolyfill () {
    if (isApiNotImplemented("saveImageToPhotosAlbum")) {
        uni.saveImageToPhotosAlbum = function (options) {
            console.warn("api: uni.saveImageToPhotosAlbum 保存图片到系统相册 在当前平台不支持，回调失败")
            options.fail && options.fail()
        }
    }

    if (isApiNotImplemented("compressImage")) {
        uni.compressImage = function (object) {
            console.warn("api: uni.compressImage 压缩图片接口 在当前平台不支持，回调失败")
            options.fail && options.fail()
        }
    }

    if (isApiNotImplemented("chooseMessageFile")) {
        //从微信聊天会话中选择文件。
        uni.chooseMessageFile = function (object) {
            console.warn("api: uni.chooseMessageFile 从微信聊天会话中选择文件。 在当前平台不支持，回调失败")
            options.fail && options.fail()
        }
    }

    if (isApiNotImplemented("getRecorderManager")) {
        //获取全局唯一的录音管理器 recorderManager
        uni.getRecorderManager = function (object) {
            console.warn("api: uni.getRecorderManager 获取全局唯一的录音管理器 在当前平台不支持")
        }
    }

    if (isApiNotImplemented("getBackgroundAudioManager")) {
        //获取全局唯一的背景音频管理器 backgroundAudioManager
        uni.getBackgroundAudioManager = function (object) {
            console.warn("api: uni.getBackgroundAudioManager 获取全局唯一的背景音频管理器 在当前平台不支持")
        }
    }

    if (isApiNotImplemented("chooseMedia")) {
        // 拍摄或从手机相册中选择图片或视频
        uni.chooseMedia = function (object) {
            console.warn("api: uni.chooseMedia 拍摄或从手机相册中选择图片或视频 在当前平台不支持，回调失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("saveVideoToPhotosAlbum")) {
        // 保存视频到系统相册
        uni.saveVideoToPhotosAlbum = function (object) {
            console.warn("api: uni.saveVideoToPhotosAlbum 保存视频到系统相册 在当前平台不支持，回调失败")
            options.fail && options.fail()
        }
    }

}

/**
 * 设备
 */
function devicePolyfill () {
    if (isApiNotImplemented("canIUse")) {
        // 判断应用的 API，回调，参数，组件等是否在当前版本可用。
        // h5时，恒返回true
        uni.canIUse = function (object) {
            console.warn("api: uni.canIUse 判断API在当前平台是否可用 返回true")
            return true
        }
    }

    //微信小程序
    if (isApiNotImplemented("startDeviceMotionListening")) {
        // 开始监听设备方向的变化
        uni.startDeviceMotionListening = function (options) {
            console.warn("api: uni.startDeviceMotionListening 开始监听设备方向的变化 在当前平台不支持")
            options.success && options.success()
        }
    }

    if (isApiNotImplemented("onMemoryWarning")) {
        // 监听内存不足告警事件。
        uni.onMemoryWarning = function (callback) {
            console.warn("监听内存不足告警事件，仅支持微信小程序、支付宝小程序、百度小程序、QQ小程序，当前平台不支持，已注释")
        }
    }

    if (isApiNotImplemented("offNetworkStatusChange")) {
        // 取消监听网络状态变化
        uni.offNetworkStatusChange = function (callback) { }
    }
    if (isApiNotImplemented("offAccelerometerChange")) {
        // 取消监听加速度数据。
        uni.offAccelerometerChange = function (callback) { }
    }
    if (isApiNotImplemented("startAccelerometer")) {
        // 开始监听加速度数据。
        uni.startAccelerometer = function (callback) {
            console.warn("api: uni.startAccelerometer 开始监听加速度数据 在当前平台不支持")
        }
    }

    if (isApiNotImplemented("offCompassChange")) {
        // 取消监听罗盘数据
        uni.offCompassChange = function (callback) {
            console.warn("api: uni.offCompassChange 取消监听罗盘数据 在当前平台不支持")
        }
    }

    if (isApiNotImplemented("startCompass")) {
        // 开始监听罗盘数据
        uni.startCompass = function (callback) {
            console.warn("api: uni.startCompass 开始监听罗盘数据 在当前平台不支持")
        }
    }


    if (isApiNotImplemented("onGyroscopeChange")) {
        // 监听陀螺仪数据变化事件
        uni.onGyroscopeChange = function (callback) {
            console.warn("api: uni.onGyroscopeChange 监听陀螺仪数据变化事件 在当前平台不支持")
        }
    }

    if (isApiNotImplemented("startGyroscope")) {
        // 开始监听陀螺仪数据
        uni.startGyroscope = function (callback) {
            console.warn("api: uni.startGyroscope 监听陀螺仪数据变化事件 在当前平台不支持")
        }
    }
    if (isApiNotImplemented("stopGyroscope")) {
        // 停止监听陀螺仪数据
        uni.stopGyroscope = function (callback) {
            console.warn("api: uni.stopGyroscope 停止监听陀螺仪数据 在当前平台不支持")
        }
    }
    if (isApiNotImplemented("scanCode")) {
        // 调起客户端扫码界面，扫码成功后返回对应的结果
        uni.scanCode = function (callback) {
            console.warn("api: uni.scanCode 扫描二维码 在当前平台不支持")
        }
    }

    if (isApiNotImplemented("setClipboardData")) {
        // 设置系统剪贴板的内容
        uni.setClipboardData = function (callback) {
            console.warn("api: uni.setClipboardData 设置系统剪贴板的内容 在当前平台不支持")
        }
    }
    if (isApiNotImplemented("getClipboardData")) {
        // 获取系统剪贴板内容
        uni.getClipboardData = function (callback) {
            console.warn("api: uni.getClipboardData 获取系统剪贴板内容 在当前平台不支持")
        }
    }
    if (isApiNotImplemented("setScreenBrightness")) {
        // 设置屏幕亮度
        uni.setScreenBrightness = function (callback) {
            console.warn("api: uni.setScreenBrightness 设置屏幕亮度 在当前平台不支持")
        }
    }
    if (isApiNotImplemented("getScreenBrightness")) {
        // 获取屏幕亮度
        uni.getScreenBrightness = function (callback) {
            console.warn("api: uni.getScreenBrightness 获取屏幕亮度 在当前平台不支持")
        }
    }

    if (isApiNotImplemented("setKeepScreenOn")) {
        // 设置是否保持常亮状态
        uni.setKeepScreenOn = function (callback) {
            console.warn("api: uni.setKeepScreenOn 设置是否保持常亮状态 在当前平台不支持")
        }
    }
    if (isApiNotImplemented("onUserCaptureScreen")) {
        // 监听用户主动截屏事件
        uni.onUserCaptureScreen = function (callback) {
            console.warn("api: uni.onUserCaptureScreen 监听用户主动截屏事件 在当前平台不支持")
        }
    }
    if (isApiNotImplemented("addPhoneContact")) {
        // 添加联系人
        uni.addPhoneContact = function (callback) {
            console.warn("api: uni.addPhoneContact 添加联系人 在当前平台不支持")
        }
    }
}

/**
 * 界面相关
 */
function uiPolyfill () {
    if (isApiNotImplemented("hideNavigationBarLoading")) {
        // 在当前页面隐藏导航条加载动画
        uni.hideNavigationBarLoading = function (options) {
            console.warn("api: uni.hideNavigationBarLoading 在当前页面隐藏导航条加载动画 在当前平台不支持，回调成功")
            options.success && options.success()
        }
    }

    if (isApiNotImplemented("setTabBarBadge")) {
        // 为 tabBar 某一项的右上角添加文本
        uni.setTabBarBadge = function (options) {
            console.warn("api: uni.setTabBarBadge 为 tabBar 某一项的右上角添加文本 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("removeTabBarBadge")) {
        // 移除 tabBar 某一项右上角的文本
        uni.removeTabBarBadge = function (options) {
            console.warn("api: uni.removeTabBarBadge 移除 tabBar 某一项右上角的文本 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("showTabBarRedDot")) {
        // 显示 tabBar 某一项的右上角的红点
        uni.showTabBarRedDot = function (options) {
            console.warn("api: uni.showTabBarRedDot 显示 tabBar 某一项的右上角的红点 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("hideTabBarRedDot")) {
        // 隐藏 tabBar 某一项的右上角的红点
        uni.hideTabBarRedDot = function (options) {
            console.warn("api: uni.hideTabBarRedDot 隐藏 tabBar 某一项的右上角的红点 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    ///////////////////////////////
    if (isApiNotImplemented("setBackgroundColor")) {
        // 动态设置窗口的背景色
        uni.setBackgroundColor = function (options) {
            console.warn("api: uni.setBackgroundColor 动态设置窗口的背景色 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("setBackgroundTextStyle")) {
        // 动态设置下拉背景字体、loading 图的样式
        uni.setBackgroundTextStyle = function (options) {
            console.warn("api: uni.setBackgroundTextStyle 动态设置下拉背景字体、loading 图的样式 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("onWindowResize")) {
        // 监听窗口尺寸变化事件
        uni.onWindowResize = function (callback) {
            console.warn("api: uni.onWindowResize 监听窗口尺寸变化事件 在当前平台不支持，执行失败")
            callback && callback()
        }
    }
    if (isApiNotImplemented("offWindowResize")) {
        // 取消监听窗口尺寸变化事件
        uni.offWindowResize = function (callback) {
            console.warn("api: uni.offWindowResize 取消监听窗口尺寸变化事件 在当前平台不支持，执行失败")
            callback && callback()
        }
    }
    if (isApiNotImplemented("loadFontFace")) {
        // 动态加载网络字体
        uni.loadFontFace = function (options) {
            console.warn("api: uni.loadFontFace 动态加载网络字体 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("getMenuButtonBoundingClientRect")) {
        // 微信胶囊按钮布局信息
        uni.getMenuButtonBoundingClientRect = function () {
            console.warn("api: uni.getMenuButtonBoundingClientRect 微信胶囊按钮布局信息 在当前平台不支持，执行失败")
        }
    }
}
/**
 * file
 */
function filePolyfill () {
    if (isApiNotImplemented("saveFile")) {
        // 保存文件到本地
        uni.saveFile = function (options) {
            console.warn("api: uni.saveFile 保存文件到本地 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("getSavedFileList")) {
        // 获取本地已保存的文件列表
        uni.getSavedFileList = function (options) {
            console.warn("api: uni.getSavedFileList 获取本地已保存的文件列表 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("getSavedFileInfo")) {
        // 获取本地文件的文件信息
        uni.getSavedFileInfo = function (options) {
            console.warn("api: uni.getSavedFileInfo 获取本地文件的文件信息 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("removeSavedFile")) {
        // 删除本地存储的文件
        uni.removeSavedFile = function (options) {
            console.warn("api: uni.removeSavedFile 删除本地存储的文件 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("getFileInfo")) {
        // 获取文件信息
        uni.getFileInfo = function (options) {
            console.warn("api: uni.getFileInfo 获取文件信息 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("openDocument")) {
        // 新开页面打开文档
        uni.openDocument = function (options) {
            console.warn("api: uni.openDocument 新开页面打开文档 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("getFileSystemManager")) {
        // 获取全局唯一的文件管理器
        uni.getFileSystemManager = function () {
            console.warn("api: uni.getFileSystemManager 获取全局唯一的文件管理器 在当前平台不支持，执行失败")
        }
    }
}

/**
 * canvas
 */
function canvasPolyfill () {
    if (isApiNotImplemented("createOffscreenCanvas")) {
        // 创建离屏 canvas 实例
        uni.createOffscreenCanvas = function () {
            console.warn("api: uni.createOffscreenCanvas 创建离屏 canvas 实例 在当前平台不支持，执行失败")
        }
    }

    if (isApiNotImplemented("canvasToTempFilePath")) {
        // 把当前画布指定区域的内容导出生成指定大小的图片
        uni.canvasToTempFilePath = function () {
            console.warn("api: uni.canvasToTempFilePath 把当前画布指定区域的内容导出生成指定大小的图片 在当前平台不支持，执行失败")
        }
    }
}

/**
 * Ad广告
 */
function adPolyfill () {
    if (isApiNotImplemented("createRewardedVideoAd")) {
        // 激励视频广告
        uni.createRewardedVideoAd = function () {
            console.warn("api: uni.createRewardedVideoAd 激励视频广告 在当前平台不支持，执行失败")
            return {
                show () { },
                onLoad () { },
                offLoad () { },
                load () { },
                onError () { },
                offError () { },
                onClose () { },
                offClose () { },
            }
        }
    }
    if (isApiNotImplemented("createInterstitialAd")) {
        // 插屏广告组件
        uni.createInterstitialAd = function () {
            console.warn("api: uni.createInterstitialAd 插屏广告组件 在当前平台不支持，执行失败")
        }
    }
}

/**
 * 第三方
 */
function pluginsPolyfill () {
    if (isApiNotImplemented("getProvider")) {
        // 获取服务供应商
        uni.getProvider = function (options) {
            console.warn("api: uni.getProvider 获取服务供应商 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }

    if (isApiNotImplemented("showShareMenu")) {
        // 小程序的原生菜单中显示分享按钮
        uni.showShareMenu = function (options) {
            console.warn("api: uni.showShareMenu 小程序的原生菜单中显示分享按钮 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("hideShareMenu")) {
        // 小程序的原生菜单中显示分享按钮
        uni.hideShareMenu = function (options) {
            console.warn("api: uni.hideShareMenu 小程序的原生菜单中隐藏分享按钮 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("requestPayment")) {
        // 支付
        uni.requestPayment = function (options) {
            console.error("api: uni.requestPayment 支付 在当前平台不支持(需自行参考文档封装)，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("createWorker")) {
        // 创建一个 Worker 线程
        uni.createWorker = function () {
            console.error("api: uni.createWorker 创建一个 Worker 线程 在当前平台不支持，执行失败")
        }
    }
}

/**
 * 其他
 */
function otherPolyfill () {
    if (isApiNotImplemented("authorize")) {
        // 提前向用户发起授权请求
        uni.authorize = function (options) {
            console.warn("api: uni.authorize 提前向用户发起授权请求 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }

    if (isApiNotImplemented("openSetting")) {
        // 调起客户端小程序设置界面
        uni.openSetting = function (options) {
            console.warn("api: uni.openSetting 调起客户端小程序设置界面 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }

    if (isApiNotImplemented("getSetting")) {
        // 获取用户的当前设置
        uni.getSetting = function (options) {
            console.warn("api: uni.getSetting 获取用户的当前设置 在当前平台不支持，【关键流程函数】回调成功")
            options.success && options.success({
                authSetting: {
                    scope: {
                        userInfo: false
                    }
                }
            })
        }
    }

    if (isApiNotImplemented("navigateToMiniProgram")) {
        // 打开另一个小程序
        uni.navigateToMiniProgram = function (options) {
            console.warn("api: uni.navigateToMiniProgram 打开另一个小程序 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("navigateBackMiniProgram")) {
        // 跳转回上一个小程序
        uni.navigateBackMiniProgram = function (options) {
            console.warn("api: uni.navigateBackMiniProgram 跳转回上一个小程序 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("getAccountInfoSync")) {
        // 获取当前帐号信息
        uni.getAccountInfoSync = function (options) {
            console.warn("api: uni.getAccountInfoSync 获取当前帐号信息 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }

    if (isApiNotImplemented("requestSubscribeMessage")) {
        // 订阅消息
        uni.requestSubscribeMessage = function (options) {
            console.warn("api: uni.requestSubscribeMessage 订阅消息 在当前平台不支持，执行失败")
            options.fail && options.fail()
        }
    }
    if (isApiNotImplemented("getUpdateManager")) {
        // 管理小程序更新
        uni.getUpdateManager = function (options) {
            console.error("api: uni.getUpdateManager 管理小程序更新 在当前平台不支持，执行失败")
        }
    }
    if (isApiNotImplemented("setEnableDebug")) {
        // 设置是否打开调试开关
        uni.setEnableDebug = function (options) {
            console.error("api: uni.setEnableDebug 设置是否打开调试开关 在当前平台不支持，执行失败")
        }
    }
    if (isApiNotImplemented("getExtConfig")) {
        // 获取第三方平台自定义的数据字段
        uni.getExtConfig = function (options) {
            console.error("api: uni.getExtConfig 获取第三方平台自定义的数据字段 在当前平台不支持，执行失败")
        }
    }
    if (isApiNotImplemented("getExtConfigSync")) {
        // uni.getExtConfig 的同步版本
        uni.getExtConfigSync = function (options) {
            console.error("api: uni.getExtConfigSync uni.getExtConfig 的同步版本 在当前平台不支持，执行失败")
        }
    }
}

/**
* uni.navigateTo 和 uni.redirectTo 不能直接跳转tabbar里面的页面，拦截fail，并当它为tabbar页面时，直接调用uni.switchTab()
*/
function routerPolyfill () {
    var routerApiFailEventHandle = function (res, options) {
        if (res.errMsg.indexOf('tabbar page') > -1) {
            console.error('res.errMsg: ' + res.errMsg)
            var apiName = res.errMsg.match(/not\s(\w+)\sa/)[1]
            console.log(apiName)
            var url = options.url
            if (url) {
                var queryString = url.split('?')[1]
                if (queryString) {
                    console.error(apiName + " 的参数将被忽略：" + queryString)
                }
                uni.switchTab({
                    url: url
                })
            }
        }
    }

    var routerApiHandle = function (oriLogFunc) {
        return function (options) {
            try {
                if (options.fail) {
                    options.fail = (function fail (failFun) {
                        return function (res) {
                            routerApiFailEventHandle(res, options)
                            failFun(res)
                        }
                    })(options.fail)
                } else {
                    options.fail = function (res) {
                        routerApiFailEventHandle(res, options)
                    }
                }
                oriLogFunc.call(oriLogFunc, options)
            } catch (e) {
                console.error('uni.navigateTo or uni.redirectTo error', e)
            }
        }
    }

    uni.navigateTo = routerApiHandle(uni.navigateTo)
    uni.redirectTo = routerApiHandle(uni.redirectTo)
}

var isInit = false
/**
 * polyfill 入口
 */
function init () {
    if (isInit) return
    isInit = true

    //条件编译
    platformPolyfill()
    //登录
    loginPolyfill()

    //设备
    devicePolyfill()

    //媒体相关
    mediaPolyfill()

    //ui
    uiPolyfill()
    //file
    filePolyfill()
    //canvas
    canvasPolyfill()
    //ad
    adPolyfill()
    //plugins
    pluginsPolyfill()
    //other
    otherPolyfill()

    //router
    routerPolyfill()
}


module.exports = {
    init,
    guid
}
