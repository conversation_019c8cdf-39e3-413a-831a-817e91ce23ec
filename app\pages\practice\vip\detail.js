import {
	get,
	post
} from "@/common/js/http.js";

let app = getApp(),
	that = null,
	cache = app.globalData.config.storage;

export default {
	data() {
		return {
			isLoad: false,
			info: {},
			tabs: ['网课详情', '视频目录', '课件下载'],
			currentTab: 0,
			currentVideoUrl: '',
			currentVideoIndex: 0,
			videoList: [],
			currentVideo: {},
			materialList: [],
			showVideoPlayer: false,
			showServiceQrcode: false,
			serviceQrcode: '/static/img/customer-support.png', // 使用项目中的客服图片
			page_config: {}, // 存储页面配置信息
		};
	},
	onLoad(options) {
		app.globalData.showShareMenu();
		that = this;
		that.getPageConfig();
		that.getCourseDetail(options.id);
	},
	methods: {
		// 获取页面配置信息
		async getPageConfig() {
			try {
				let res = await post('appConfig/get', { config_type: 'video_collection_detail_page_set' });
				that.page_config = res.data;
				if (res.data.customerServiceQrcode) {
					that.serviceQrcode = res.data.customerServiceQrcode;
				}
				console.log('页面配置信息:', that.page_config);
			} catch (e) {
				console.error('获取页面配置失败:', e);
			}
		},
		getCourseDetail(id) {
			get('videoCollection/getInfo', {
				id: id
			})
				.then((res) => {
					that.info = res.data.info;
					that.videoList = res.data.videoList;
					that.materialList = res.data.coursewareList;

					// 如果没有视频，调整tabs数组
					if (that.videoList.length <= 0) {
						that.tabs = ['网课详情', '课件下载'];
					}

					that.isLoad = true;
				});
		},
		tabSelect(e) {
			this.currentTab = e.currentTarget.dataset.id;
		},
		selectVideo(e) {
			const index = e.currentTarget.dataset.id;
			that.playVideo(index);
		},
		playVideo(index) {
			get('video/getInfo', {
				id: that.videoList[index].id
			})
				.then((res) => {
					that.currentVideoIndex = index;
					that.currentVideoUrl = res.data.url;
					that.showVideoPlayer = true;
				})
				.catch((res) => {
					console.error(res);
				});
		},
		closeQrcodeModal() {
			this.showServiceQrcode = false;
		},
		playFirstVideo() {
			if (this.videoList.length > 0) {
				that.playVideo(0);
			}
		},
		downloadMaterial(e) {
			const index = e.currentTarget.dataset.id;
			const material = this.materialList[index];
		},
		async buyCourse() {
			// 购买课程的逻辑
			try {
				let res = await post('order/create', {
					type: 4,
					collection_id: that.info.id
				});
				let id = res.data.order_id;
				let url = '/pages/pay/pay?id=' + id;
				uni.navigateTo({
					url: url
				});
			} catch (e) {
				console.error(e);
				app.globalData.showToast({
					title: e.data.message,
					icon: 'none'
				});
			}
		},
		startLearning() {

			// 如果是第三方平台课程，引导用户联系客服
			if (this.info.can_play_online != 1) {
				that.showServiceQrcode = true;
				return;
			}

			// 已购买课程，开始学习
			if (this.videoList.length > 0) {
				this.currentVideoIndex = 0;
				this.showVideoPlayer = true;
				// 获取第一个视频的播放地址
				get('video/getInfo', {
					id: this.videoList[0].id
				})
					.then((res) => {
						that.currentVideoUrl = res.data.url;
					});
			} else {
				uni.showToast({
					title: '暂无视频内容',
					icon: 'none'
				});
			}
		}
	}
};