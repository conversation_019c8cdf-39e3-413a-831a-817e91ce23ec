<template>
  <view v-if="isMatched" class="match-media">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'match-media',
  props: {
    // 媒体查询条件
    query: {
      type: String,
      required: true
    },
    // 预设的断点
    breakpoint: {
      type: String,
      validator: value => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
    }
  },
  data() {
    return {
      isMatched: false,
      mediaQueryList: null
    };
  },
  computed: {
    mediaQuery() {
      if (this.breakpoint) {
        const breakpoints = {
          xs: '(max-width: 576px)',
          sm: '(min-width: 577px) and (max-width: 768px)', 
          md: '(min-width: 769px) and (max-width: 1024px)',
          lg: '(min-width: 1025px) and (max-width: 1440px)',
          xl: '(min-width: 1441px)'
        };
        return breakpoints[this.breakpoint];
      }
      return this.query;
    }
  },
  mounted() {
    this.initMediaQuery();
  },
  beforeDestroy() {
    if (this.mediaQueryList && this.mediaQueryList.removeListener) {
      this.mediaQueryList.removeListener(this.handleMediaChange);
    }
  },
  methods: {
    initMediaQuery() {
      // #ifdef H5
      if (window.matchMedia) {
        this.mediaQueryList = window.matchMedia(this.mediaQuery);
        this.isMatched = this.mediaQueryList.matches;
        this.mediaQueryList.addListener(this.handleMediaChange);
      }
      // #endif
      
      // #ifdef MP-WEIXIN
      // 微信小程序使用 uni.createMediaQueryObserver
      if (uni.createMediaQueryObserver) {
        const observer = uni.createMediaQueryObserver(this);
        observer.observe({
          minWidth: this.getMinWidth(),
          maxWidth: this.getMaxWidth()
        }, this.handleMediaChange);
      } else {
        // 降级方案：使用系统信息判断
        this.checkScreenSize();
      }
      // #endif
      
      // #ifndef H5 || MP-WEIXIN
      // 其他平台降级方案
      this.checkScreenSize();
      // #endif
    },
    
    getMinWidth() {
      if (this.breakpoint === 'sm') return 577;
      if (this.breakpoint === 'md') return 769;
      if (this.breakpoint === 'lg') return 1025;
      if (this.breakpoint === 'xl') return 1441;
      return 0;
    },
    
    getMaxWidth() {
      if (this.breakpoint === 'xs') return 576;
      if (this.breakpoint === 'sm') return 768;
      if (this.breakpoint === 'md') return 1024;
      if (this.breakpoint === 'lg') return 1440;
      return 9999;
    },
    
    checkScreenSize() {
      uni.getSystemInfo({
        success: (res) => {
          const width = res.windowWidth;
          const minWidth = this.getMinWidth();
          const maxWidth = this.getMaxWidth();
          this.isMatched = width >= minWidth && width <= maxWidth;
        }
      });
    },
    
    handleMediaChange(matches) {
      // #ifdef H5
      this.isMatched = matches.matches;
      // #endif
      
      // #ifdef MP-WEIXIN
      this.isMatched = matches;
      // #endif
    }
  }
}
</script>

<style scoped>
.match-media {
  width: 100%;
}
</style>
