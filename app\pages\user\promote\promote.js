let that = null;
let app = getApp();
export default {
	data() {
		return {
			info: {},
			isLoad: false,
			userInfo: {},
			showPaymentModal: false
		};
	}
	/**
	 * 生命周期函数--监听页面加载
	 */
	,
	onLoad: function(options) {
		that = this;
		let userInfo = app.globalData.config.storage.getUserInfoData();
		that.userInfo = userInfo;
		that.getInfo();
	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {},
	
	onShareAppMessage: function() {
		return app.globalData.getShareConfig();
	},

	methods: {

		/**
		 * 获取首页信息
		 */
		async getInfo() {
			let res = await app.globalData.service.promoteInfo()
			that.info = res.data;
			that.isLoad = true;
		},

		/**
		 * 点击菜单事件
		 * @param {Object} options
		 */
		menuTap(options) {
			let url = options.currentTarget.dataset.url;
			console.log(url)
			uni.navigateTo({
				url: url
			});
		},

		/**
		 * 输入收款信息事件
		 * @param {Object} option
		 */
		paymentInputTap(option) {
			let field = option.target.dataset.field
			that.userInfo[field] = option.detail.value;
		},

		/**
		 * 保存收款信息
		 */
		async savePaymentTap() {
			await app.globalData.service.updateUser({
				realname: that.userInfo.realname,
				alipay_account: that.userInfo.alipay_account
			})
			app.globalData.config.storage.setUserInfoData(that.userInfo);
			app.showToast('保存成功');
			that.showPaymentModal = false;
		},

		/**
		 * 显示收款表单
		 * @param {Object} option
		 */
		setPaymentModalTap(option) {
			that.showPaymentModal = option;
		},

		/**
		 * 显示宣传素材
		 * @param {Object} option
		 */
		async openSourceMaterialTap(option) {
			let res = await app.globalData.service.shareImage({})
			wx.previewImage({
				urls: res.data
			})

		}
	}
};