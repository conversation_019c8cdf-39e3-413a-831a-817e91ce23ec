<view><back vue-id="2a9d78a5-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="请选择学校" bind:__l="__l"></back><block wx:if="{{load}}"><view><view class="cu-bar bg-white search fixed" style="{{('top:'+CustomBar+'px;')}}"><view class="search-form"><text class="cuIcon-search"></text><input type="text" placeholder="输入学校名称进行搜索" confirm-type="search" data-event-opts="{{[['input',[['inputTap',['$event']]]],['confirm',[['searchTap',['$event']]]]]}}" bindinput="__e" bindconfirm="__e"/></view><view class="action" style="display:none;"><button data-event-opts="{{[['tap',[['searchCityTap',['$event']]]]]}}" class="cu-btn bg-gradual-blue shadow-blur round" bindtap="__e">搜索</button></view><view data-event-opts="{{[['tap',[['cityTap',['$event']]]]]}}" class="action text-blue" bindtap="__e"><text>{{currentCity.name}}</text><text class="cuIcon-triangledownfill"></text></view></view><scroll-view class="indexes margin-top" style="{{('height:calc(100vh - '+CustomBar+'px - 50px)')}}" scroll-y="{{true}}" scroll-into-view="{{'indexes-'+listCurID}}" scroll-with-animation="{{true}}" enable-back-to-top="{{true}}"><block wx:for="{{listData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="cu-list menu menu-avatar no-padding"><view class="cu-item arrow" data-data="{{item}}" data-event-opts="{{[['tap',[['schoolTap',['$event']]]]]}}" bindtap="__e"><view class="cu-avatar lg bg-blue">S</view><view class="content"><view class="text-grey"><text class="text-abc">{{item.name}}</text></view></view></view></view></block></block><adbanner vue-id="2a9d78a5-2" bind:__l="__l"></adbanner></scroll-view><block wx:if="{{!hidden}}"><view class="indexToast">{{''+listCur+''}}</view></block></view></block></view>