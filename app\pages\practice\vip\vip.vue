<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="在线网课"></back>
		<view class="flex flex-direction" style="min-height: 100vh;" v-if="isLoad">
			<!-- 固定顶部搜索栏 -->
			<view class="cu-bar bg-white search ">
				<view class="search-form round">
					<text class="cuIcon-search"></text>
					<input type="text" v-model="keyword" @input="onInputChange" @confirm="searchTap"
						placeholder="搜索关键字" confirm-type="search"></input>
				</view>
				<view class="action" @tap="searchTap">
					<text class="text-blue">搜索</text>
				</view>
			</view>

			<!-- 视频列表 -->
			<view class="video-list padding-lr" style="margin-bottom: 120rpx;">
				<view class="video-card" v-for="(item, index) in videoList" :key="index" @tap="videoTap"
					:data-index="index">
					<view class="video-cover">
						<image :src="item.cover" mode="aspectFill" lazy-load></image>
						<view class="video-duration" v-if="item.video_num">
							<text>共{{item.video_num}}课时</text>
						</view>
						<view class="video-tag" v-if="item.is_hot">
							<text>HOT</text>
						</view>
						<view class="play-icon" v-if="item.can_play_online == 1">
							<text class="cuIcon-playfill"></text>
						</view>
					</view>
					<view class="video-info">
						<view class="video-title text-cut-2">{{ item.name }}</view>
						<view class="video-desc text-cut">{{ item.description || '暂无描述' }}</view>

						<view class="video-meta">
							<view class="video-price-container">
								<view class="video-price text-red text-bold"
									:class="{ 'price-free': item.price === 0 }">
									{{ item.price === 0 ? '免费' : '¥' + item.price }}
								</view>
								<view class="video-original-price text-gray text-del margin-left-xs"
									v-if="item.market_price > 0">
									¥{{ item.market_price }}
								</view>
							</view>
							<view class="video-views text-blue" v-if="item.sale_num > 0">
								<text class="cuIcon-peoplefill text-blue"></text>
								<text class="text-blue">{{item.sale_num}}人购买</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<empty v-if="isLoad == true && videoList.length == 0"></empty>

			</view>

			<!-- 底部安全区域 -->
			<view class="safe-area-bottom"></view>
		</view>
	</view>
</template>

<script src="./vip.js"></script>

<style src="./vip.css"></style>