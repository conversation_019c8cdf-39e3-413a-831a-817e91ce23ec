<view class="material-list-ios data-v-487aa56b"><back vue-id="724aee0a-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="学习资料" class="data-v-487aa56b" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view class="list-content data-v-487aa56b"><scroll-view class="scroll-area data-v-487aa56b" scroll-y="{{true}}"><block wx:if="{{$root.g0==0}}"><empty vue-id="724aee0a-2" info="暂时还没有资料" showAd="{{false}}" class="data-v-487aa56b" bind:__l="__l"></empty></block><block wx:else><view class="data-v-487aa56b"><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="material-card shadow data-v-487aa56b" data-id="{{item.id}}" data-event-opts="{{[['tap',[['itemTap',['$event']]]]]}}" bindtap="__e"><view class="card-main data-v-487aa56b"><view class="card-title-row data-v-487aa56b"><text class="cuIcon-file text-blue data-v-487aa56b"></text><text class="card-title data-v-487aa56b">{{item.name}}</text></view><view class="card-tags data-v-487aa56b"><view class="cu-tag bg-green light price-tag data-v-487aa56b"><text class="data-v-487aa56b">{{item.price}}</text><text class="cuIcon-coin text-yellow data-v-487aa56b" style="margin-left:4rpx;font-size:24rpx;"></text></view><view class="cu-tag bg-blue light version-tag data-v-487aa56b">{{''+(item.version==1?'在线文档':'网盘资料')+''}}</view></view></view><view class="card-meta-row data-v-487aa56b"><view class="meta-item data-v-487aa56b"><text class="cuIcon-hotfill text-red data-v-487aa56b"></text><text class="meta-label data-v-487aa56b">兑换人数</text><text class="meta-value center-value data-v-487aa56b">{{item.hot}}</text></view><view class="meta-action data-v-487aa56b"><button class="cu-btn bg-blue radius detail-btn data-v-487aa56b">查看详情</button></view></view></view></block></view></block></scroll-view></view></block><block wx:if="{{isLoad&&!appIsAudit}}"><view class="float-action-container data-v-487aa56b"><view data-event-opts="{{[['tap',[['shareTap',['$event']]]]]}}" class="share-button data-v-487aa56b" bindtap="__e"><text class="cuIcon-add data-v-487aa56b"></text><text class="data-v-487aa56b">分享资料</text></view></view></block></view>