{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/user/login/auth.vue?ffaa", "webpack:///D:/桌面/thinker/app/pages/user/login/auth.vue?2eb8", "webpack:///D:/桌面/thinker/app/pages/user/login/auth.vue?1146", "webpack:///D:/桌面/thinker/app/pages/user/login/auth.vue?d357", "uni-app:///pages/user/login/auth.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "app", "cache", "data", "load", "onLoad", "that", "onShow", "methods", "getAuthData", "getRequest", "auth<PERSON><PERSON>", "then", "url", "uni", "catch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAuqB,CAAgB,spBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACM3rB;EACAC;EACAC;AAAA,eACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;IACAA;EACA;EACAC;EACAC;IACAC;MACAR,sBACAS;QACAC;MACA,GACAC;QACAN;QACAJ;QACAA;QACAA;QACAA;QACAA;QACAA;QACAD;QACAA;QACA;QACA;UACAY;QACA;QACAC;UACAD;QACA;MACA,GACAE;QACAd;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/user/login/auth.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/login/auth.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./auth.vue?vue&type=template&id=35abd0e9&\"\nvar renderjs\nimport script from \"./auth.vue?vue&type=script&lang=js&\"\nexport * from \"./auth.vue?vue&type=script&lang=js&\"\nimport style0 from \"./auth.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/login/auth.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./auth.vue?vue&type=template&id=35abd0e9&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./auth.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./auth.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"load\">\r\n\t\t<back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"正在自动登录\"></back>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tlet that = null,\r\n\t\tapp = getApp(),\r\n\t\tcache = app.globalData.config.storage;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tload: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(options) {\r\n\t\t\tlet authKey = options.authKey;\r\n\t\t\tthat = this;\r\n\t\t\tthat.getAuthData(authKey);\r\n\t\t},\r\n\t\tonShow: function() {},\r\n\t\tmethods: {\r\n\t\t\tgetAuthData: function(authKey) {\r\n\t\t\t\tapp.globalData.server\r\n\t\t\t\t\t.getRequest('user/auth', {\r\n\t\t\t\t\t\tauthKey: authKey\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(function(e) {\r\n\t\t\t\t\t\tthat.load = true;\r\n\t\t\t\t\t\tcache.setFristData(2);\r\n\t\t\t\t\t\tcache.setUserTokenData(e.data.tokenInfo);\r\n\t\t\t\t\t\tcache.setUserInfoData(e.data.info);\r\n\t\t\t\t\t\tcache.setCurrentCityData(e.data.regionInfo);\r\n\t\t\t\t\t\tcache.setCurrentExamData(e.data.examInfo);\r\n\t\t\t\t\t\tcache.setCurrentProfessionData(e.data.professionInfo);\r\n\t\t\t\t\t\tapp.showToast('登录成功');\r\n\t\t\t\t\t\tapp.globalData.checkLogin();\r\n\t\t\t\t\t\tlet url = '../../index/index';\r\n\t\t\t\t\t\tif (e.data.redirectUrl != '') {\r\n\t\t\t\t\t\t\turl = e.data.redirectUrl\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\turl: url\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(function(e) {\r\n\t\t\t\t\t\tapp.showToast('自动登录失败');\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style src=\"./auth.css\"></style>"], "sourceRoot": ""}