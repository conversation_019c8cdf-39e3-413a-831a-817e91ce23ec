<template>
	<view v-if="isLoad">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" :title="info.name"></back>
		<view class="flex flex-direction" style="min-height: 100vh;">
			<view class="flex-sub" style="margin-bottom: 270rpx;">
				<!-- 学习概况部分 - 仅当有考试倒计时时显示 -->
				<block>
					<view class="cu-bar bg-white margin-top-xs solid-bottom" style="margin-buttom: 3rpx;">
						<view class="action sub-title">
							<text class="text-xl text-bold text-blue text-shadow">学习概况</text>
							<text class="text-ABC text-blue">OVERVIEW</text>
						</view>
					</view>
					<!-- 考试倒计时和答题正确率卡片 -->
					<view class="cu-card case bg-white margin-bottom-sm">
						<view class="cu-item shadow padding-sm">
							<view class="grid col-4">
								<view class="padding-xs">
									<view class="text-center">
										<view class="text-xl text-green text-bold">{{today_question_count}}</view>
										<view class="text-sm text-gray">今日做题</view>
									</view>
								</view>
								<view class="padding-xs">
									<view class="text-center">
										<view class="text-xl text-purple text-bold">{{total_question_count}}</view>
										<view class="text-sm text-gray">总答题数</view>
									</view>
								</view>
								<view class="padding-xs">
									<view class="text-center">
										<view class="text-xl text-blue text-bold">{{correct_rate}}%</view>
										<view class="text-sm text-gray">答题正确率</view>
									</view>
								</view>
								<view class="padding-xs">
									<view class="text-center">
										<view class="text-xl text-red text-bold">{{exam_countdown}}</view>
										<view class="text-sm text-gray">考试倒计时</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</block>
				
				<!-- 基本信息部分 -->
				<view class="cu-bar bg-white margin-top-xs solid-bottom" style="margin-buttom: 3rpx;">
					<view class="action sub-title">
						<text class="text-xl text-bold text-blue text-shadow">题库信息</text>
						<text class="text-ABC text-blue">INFORMATION</text>
					</view>
					<view class="action">
						<button class="cu-btn round bg-blue shadow" @tap="onCollect">
							<text class="cuIcon-favor margin-right-xs"></text>{{info.collect==1 ? '已收藏':'收藏'}}
						</button>
					</view>
				</view>
				<view class="cu-list menu">
					<view class="cu-item" style="font-size: 32rpx;">
						<view class="content">
							<text class="cuIcon-infofill text-blue"></text>
							<text class="text-black">题库名称</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{info.name}}</view>
						</view>
					</view>
					<view class="cu-item" style="font-size: 32rpx;" v-if="info.id && detail_page_set.show_course_code > 0">
						<view class="content">
							<text class="cuIcon-cuIcon text-blue"></text>
							<text class="text-black">题库编码</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{info.code ? info.code:info.id}}</view>
						</view>
					</view>
					<view class="cu-item" style="font-size: 32rpx;" v-if="info.category_name">
						<view class="content">
							<text class="cuIcon-group_fill text-blue"></text>
							<text class="text-black">题库分类</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{info.category_name}}</view>
						</view>
					</view>
					<view class="cu-item" style="font-size: 32rpx;" v-if="info.desc">
						<view class="content">
							<text class="cuIcon-formfill text-blue"></text>
							<text class="text-black">题库描述</text>
						</view>
						<view class="action">
							<view class="cu-tag round bg-green light">{{info.desc}}</view>
						</view>
					</view>
				</view>
				<view class="cu-bar bg-white margin-top-xs solid-bottom" style="margin-buttom: 3rpx;">
					<view class="action sub-title">
						<text class="text-xl text-bold text-blue text-shadow">题库练习</text>
						<text class="text-ABC text-blue">PRACTICE</text>
					</view>
					<view class="action">
						<button class="cu-btn round bg-green shadow" open-type="share">
							<text class="cuIcon-forwardfill margin-right-xs"></text>分享题库
						</button>
					</view>
				</view>
				<view class="cu-list grid col-2 ">
					<view class="cu-item menu-item" @tap="onOrderPractice" style="flex-direction: row-reverse;">
						<view class="text-blue fa-icon fa-sort-alpha-asc" style="width: 100%;margin-top: 28rpx;font-size: 32rpx"></view>
						<text class="text-bold text-xl"
							style="width: 100%;margin-top: 28rpx;color: black;font-size: 28rpx;margin-left: 8rpx;">顺序练习</text>
					</view>
					<view class="cu-item menu-item" @tap="onQuestionTypePractice" style="flex-direction: row-reverse;">
						<view class="text-blue cuIcon-filter"></view>
						<text class="text-bold text-xl"
							style="width: 100%;margin-top: 28rpx;color: black;font-size: 28rpx;margin-left: 8rpx;">题型练习</text>
					</view>
					<view class="cu-item menu-item" @tap="onCategory(item)" v-for="(item, index) in categoryList"
						style="flex-direction: row-reverse" :key="index" v-if="categoryList && categoryList.length > 0">
						<view :class="item.icon" class="text-blue"></view>
						<text class="text-bold text-xl"
							style="width: 100%;margin-top: 28rpx;color: black;font-size: 28rpx;margin-left: 8rpx;">{{ item.name }}</text>
					</view>
					<view class="cu-item menu-item" @tap="onSearchQuestion" style="flex-direction: row-reverse">
						<view class="text-blue cuIcon-searchlist"></view>
						<text class="text-bold text-xl"
							style="width: 100%;margin-top: 28rpx;color: black;font-size: 28rpx;margin-left: 8rpx;">搜索试题</text>
					</view>
					<view class="cu-item menu-item" @tap="onErrorQuestion" style="flex-direction: row-reverse">
						<view class="text-blue cuIcon-roundclose"></view>
						<text class="text-bold text-xl"
							style="width: 100%;margin-top: 28rpx;color: black;font-size: 28rpx;margin-left: 8rpx;">我的错题</text>
					</view>
					<view class="cu-item menu-item" @tap="onColl" style="flex-direction: row-reverse">
						<view class="text-blue cuIcon-favor"></view>
						<text class="text-bold text-xl"
							style="width: 100%;margin-top: 28rpx;color: black;font-size: 28rpx;margin-left: 8rpx;">我的收藏</text>
					</view>
				</view>
			</view>
			<view class="bottom-layout cu-bar bg-white tabbar border shop">
				<view class="action text-gray" style="display: none;" v-if="!checkAppIsAudit" @tap="onNotSupport">
					<view class="cuIcon-lock"></view>
					<text class="text-sm text-gray">考前押密</text>
				</view>
				<view class="action text-blue" v-if="!checkAppIsAudit && info.videoCollectionId && info.videoCollectionId.length > 0" @tap="onOnlineCourse">
					<view class="cuIcon-video"></view>
					<text class="text-sm text-blue">在线网课</text>
				</view>
				<view class="action text-blue" @tap="onLearn" data-type="5">
					<view class="cuIcon-down"></view>
					<text class="text-sm text-blue">资料下载</text>
				</view>
				<view v-if="!checkAppIsAudit && !isIosVirtualPay && !info.isCourseVip" @tap="onBuyCourse"
					class="action text-blue">
					<view class="cuIcon-pay"></view>
					<text class="text-sm ">开通题库</text>
				</view>
				<view @tap="showActivateModal = true"  v-if="!checkAppIsAudit && !isIosVirtualPay && !info.isCourseVip"   class="action text-blue">
					<view class="cuIcon-unlock"></view>
					<text class="text-sm text-blue">激活题库</text>
				</view>
			</view>
		</view>


		<!-- 激活题库弹窗 -->
		<activate-modal 
			:show.sync="showActivateModal" 
			title="激活题库" 
			:business-id="info.id" 
			business-type="2" 
			@showService="showOnlineServiceTap" 
			@success="onActivateSuccess"
		></activate-modal>
	</view>
</template>
<style src="./detail.css"></style>
<script src="./detail.js"></script>
