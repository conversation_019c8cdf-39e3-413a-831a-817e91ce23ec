{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/user/opinion/opinion.vue?f918", "webpack:///D:/桌面/thinker/app/pages/user/opinion/opinion.vue?27ae", "webpack:///D:/桌面/thinker/app/pages/user/opinion/opinion.vue?10ef", "webpack:///D:/桌面/thinker/app/pages/user/opinion/opinion.vue?6e6c", "uni-app:///pages/user/opinion/opinion.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "<PERSON><PERSON><PERSON>ner", "data", "isLoad", "contact", "content", "feedbackList", "appIsAudit", "onLoad", "that", "onShow", "methods", "contactInputTap", "contentInputTap", "submitTap", "app", "postRequest", "detail", "type", "then", "uni", "title", "mask", "complete", "setTimeout", "delta", "catch", "copyTextTap", "success", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,ypBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsB9rB;AACA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAD;EACA;EACAE;IACAC;MACAH;IACA;IAEAI;MACAJ;IACA;IAEAK;MACA;QACAC,sBACAC;UACAC;UACAb;UACAc;QACA,GACAC;UACAC;YACAC;YACAC;YACAC;cACAC;gBACAJ;kBACAK;gBACA;cACA;YACA;UACA;QACA,GACAC;UACAX;QACA;MACA;QACAA;MACA;IACA;IAEAY;MACA;MACAP;QACAlB;QAEA0B;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/user/opinion/opinion.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/opinion/opinion.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./opinion.vue?vue&type=template&id=4cc5972a&\"\nvar renderjs\nimport script from \"./opinion.vue?vue&type=script&lang=js&\"\nexport * from \"./opinion.vue?vue&type=script&lang=js&\"\nimport style0 from \"./opinion.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/opinion/opinion.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./opinion.vue?vue&type=template&id=4cc5972a&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n    adfootbanner: function () {\n      return import(\n        /* webpackChunkName: \"components/adfootbanner/adfootbanner\" */ \"@/components/adfootbanner/adfootbanner.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./opinion.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./opinion.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"意见反馈\"></back>\r\n\t\t<form>\r\n\t\t\t<view class=\"cu-form-group\" v-if=\"!appIsAudit\">\r\n\t\t\t\t<input @input=\"contactInputTap\" maxlength=\"32\" placeholder=\"手机号/微信号/QQ，便于联系您(选填)\"\r\n\t\t\t\t\tplaceholderStyle=\"color:#999;\" type=\"text\" value=\"\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cu-form-group\">\r\n\t\t\t\t<textarea @input=\"contentInputTap\" style=\"font-size: 30rpx\" placeholder=\"请填写您要反馈的具体内容，方便我们尽快改进\"\r\n\t\t\t\t\tplaceholderStyle=\"color:#999;\"></textarea>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"padding flex flex-direction\">\r\n\t\t\t\t<button @tap=\"submitTap\" class=\"cu-btn bg-blue lg\">提交</button>\r\n\t\t\t</view>\r\n\t\t</form>\r\n\t\t<adfootbanner unitId=\"adunit-1fb0622d11b3c262\"></adfootbanner>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport adfootbanner from '@/components/adfootbanner/adfootbanner';\r\n\tlet app = getApp();\r\n\tlet that = null;\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tadfootbanner\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\tcontact: '',\r\n\t\t\t\tcontent: '',\r\n\t\t\t\tfeedbackList: [],\r\n\t\t\t\tappIsAudit: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(t) {\r\n\t\t\tthat = this;\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tthat.appIsAudit = app.globalData.checkAppIsAudit();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tcontactInputTap: function(t) {\r\n\t\t\t\tthat.contact = t.detail.value;\r\n\t\t\t},\r\n\r\n\t\t\tcontentInputTap: function(t) {\r\n\t\t\t\tthat.content = t.detail.value;\r\n\t\t\t},\r\n\r\n\t\t\tsubmitTap: function(n) {\r\n\t\t\t\tif (that.content) {\r\n\t\t\t\t\tapp.globalData.server\r\n\t\t\t\t\t\t.postRequest('app/feedback', {\r\n\t\t\t\t\t\t\tdetail: that.content,\r\n\t\t\t\t\t\t\tcontact: that.contact || '',\r\n\t\t\t\t\t\t\ttype: 1\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(function(t) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: t.message,\r\n\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t\tcomplete: function() {\r\n\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(function(a) {\r\n\t\t\t\t\t\t\tapp.showToast('提交出错');\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.showToast('反馈内容不能为空');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tcopyTextTap: function(options) {\r\n\t\t\t\tlet data = options.target.dataset.content;\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: data,\r\n\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style src=\"./opinion.css\"></style>\r\n"], "sourceRoot": ""}