<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-red text-white" title="积分乐园"></back>
		<view v-if="isLoad == true">
			<view class="layout-section" style="margin-top: 10rpx">
				<view class="headinfo"
					style="border-radius: 15rpx; background: linear-gradient(180deg, #ff2c3c, #ff316a); height: 200rpx">
					<view class="child">
						<view class="title">今日使用</view>
						<view class="num">
							<text>{{ info.score_out }}</text>
						</view>
					</view>
					<view class="child">
						<view class="title">剩余积分</view>
						<view class="num">
							<text>{{ info.score }}</text>
						</view>
					</view>
					<view class="child">
						<view class="title">今日获得</view>
						<view class="num">
							<text>{{ info.score_in }}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="cu-list menu sm-border card-menu margin-bottom">
				<view class="cu-item flex" style="justify-content: flex-start" v-for="(sign, index) in info.record_list"
					:key="index">
					<view class="action flex-sub">
						<view class="cu-tag round bg-green light">{{ sign.source_name }}</view>
					</view>

					<view class="action flex-twice" style="margin-left: 70rpx">
						<view class="cu-tag round bg-green light">{{ sign.add_time }}</view>
					</view>

					<view class="action flex-treble" style="margin-left: 70rpx">
						<view class="cu-tag round bg-green light">{{ sign.score }}</view>
					</view>
				</view>
			</view>

			<view class="label activity_head">
				<view class="tag"></view>
				热门活动
			</view>
			<view>
				<view @tap="confirmSign" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'" v-if="info.is_sign == 0">
					<view style="flex: 1">
						<view class="item-name">每日签到，点我签到</view>
						<courselabel code="每天首次签到" :topicCount="'赠送'+info.sign_number+'积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindWeiXinTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'" v-if="info.is_bind_wx == 0">
					<view style="flex: 1">
						<view class="item-name">绑定微信，快捷登录</view>
						<courselabel code="首次绑定微信" :topicCount="'赠送'+info.wx_number+'积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindMobileTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'"
					v-if="info.is_bind_mobile == 0 && !appIsAudit">
					<view style="flex: 1">
						<view class="item-name">绑定手机，快捷登录</view>
						<courselabel code="首次绑定手机" :topicCount="'赠送'+info.bind_mobile_number+'积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindEmailTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'"
					v-if="info.is_bind_email == 0 && !appIsAudit">
					<view style="flex: 1">
						<view class="item-name">绑定邮箱，快捷登录</view>
						<courselabel code="首次绑定邮箱" :topicCount="'赠送'+info.mail_number+'积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindMpTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'" v-if="info.is_bind_mp == 0">
					<view style="flex: 1">
						<view class="item-name">绑定公众号，学习不迷路</view>
						<courselabel code="绑定公众号," :topicCount="'赠送' + info.mp_number + '积分，每日可领'+info.mp_sign_number+'积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindWxGroupTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'" v-if="info.is_bind_wx_group == 0">
					<view style="flex: 1">
						<view class="item-name">微信学习群，免费领积分</view>
						<courselabel code="微信学习群," :topicCount="'赠送' + info.bind_wx_group_num + '积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindAddDeskTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'" v-if="info.is_desktop == 0">
					<view style="flex: 1">
						<view class="item-name">添加到手机桌面</view>
						<courselabel code="首次添加到桌面" :topicCount="'首次添加赠送' + info.add_desktop_number+'积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindAddMyAppTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'" v-if="info.is_bind_email == 1">
					<view style="flex: 1">
						<view class="item-name">添加到我的小程序</view>
						<courselabel code="首次添加到我的小程序" :topicCount="'赠送' + info.add_my_app_number + '积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindShareTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'">
					<view style="flex: 1">
						<view class="item-name">分享小程序，积分翻倍</view>
						<courselabel code="转发分享微信群" :topicCount="'赠送' + info.share_number + '乘以点击人数的积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindShareFileTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'">
					<view style="flex: 1">
						<view class="item-name">分享学习资料，送海量积分</view>
						<courselabel code="上传课程资料" :topicCount="'赠送' + info.share_file_number + '积分'"></courselabel>
					</view>
				</view>
				<view @tap="bindVideoTap" class="item-layout"
					:style="'border-bottom:' + borderWidth + 'rpx solid #EDEDED;'" v-if="info.is_watch_video == 0">
					<view style="flex: 1">
						<view class="item-name">观看广告，获得奖励</view>
						<courselabel code="广告观看完成"
							:topicCount="'赠送' + info.video_number + '积分(每日最多'+info.video_count+'次)'"></courselabel>
					</view>
				</view>

				<view class="top-layout rules">
					<view style="margin-left: 10rpx; font-weight: bold; margin-bottom: 10rpx">附加说明：</view>
					<view style="padding: 10rpx" v-for="(desc, index) in info.att_desc_list" :key="index">
						{{ desc }}
					</view>
				</view>

				<confirm :title="taskTitle" :content="taskContent" :status.sync="taskDescModal" confirmText="我知道啦"
					confirmTextClass="cuIcon-roundcheck">
				</confirm>
			</view>
		</view>
	</view>
</template>
<style src="./sign.css"></style>
<script src="./sign.js"></script>