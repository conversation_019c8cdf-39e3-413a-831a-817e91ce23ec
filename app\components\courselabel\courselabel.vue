<template>
    <view class="parent">
        <view class="item1" :style="'border: ' + (isIOS ? 2 : 1) + 'rpx solid #76A6FF;'" v-if="code">{{ code }}</view>
        <view class="item2" :style="'border: ' + (isIOS ? 2 : 1) + 'rpx solid #A692FA;'" v-if="paper">{{ paper }}</view>
        <view class="item3" :style="'border: ' + (isIOS ? 2 : 1) + 'rpx solid #FF9460;'" v-if="courseType">{{ courseType }}</view>
        <view class="item4" :style="'border: ' + (isIOS ? 2 : 1) + 'rpx solid #DF93F1;'" v-if="courseApply">{{ courseApply }}</view>
        <view class="item5" :style="'border: ' + (isIOS ? 2 : 1) + 'rpx solid #FFC14F;'" v-if="topicCount">{{ topicCount }}</view>
        <view class="item6" :style="'border: ' + (isIOS ? 2 : 1) + 'rpx solid #89D75A;'" v-if="time">{{ time }}</view>
        <view class="item7" :style="'border: ' + (isIOS ? 2 : 1) + 'rpx solid #FA7762;'" v-if="price">{{ price }}</view>
    </view>
</template>

<script>
var app = getApp();
export default {
	name:'courselabel',
    data() {
        return {
            isIOS: app.globalData.isIOS
        };
    },
    props: {
        code: {
            type: String,
            default: ''
        },
        paper: {
            type: String,
            default: ''
        },
        courseType: {
            type: String,
            default: ''
        },
        courseApply: {
            type: String,
            default: ''
        },
        topicCount: {
            type: String,
            default: ''
        },
        time: {
            type: String,
            default: ''
        },
        price: {
            type: String,
            default: ''
        }
    },
    methods: {}
};
</script>
<style src="./courselabel.css"></style>
