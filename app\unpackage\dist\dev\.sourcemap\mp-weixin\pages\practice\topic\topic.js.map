{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/practice/topic/topic.vue?a79d", "webpack:///D:/桌面/thinker/app/pages/practice/topic/topic.vue?a5a3", "webpack:///D:/桌面/thinker/app/pages/practice/topic/topic.vue?38cd", "webpack:///D:/桌面/thinker/app/pages/practice/topic/topic.vue?b0ee", "uni-app:///pages/practice/topic/topic.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "listData", "title", "isLoad", "mainType", "id", "extend_id", "appIsAudit", "isIosVirtualPay", "onLoad", "that", "onShow", "methods", "getQuestionType", "app", "getRequest", "type", "then", "o", "catch", "practiceTap", "uni", "url", "onBuyTap", "onCourseTap"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,aAAa,sKAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAwqB,CAAgB,upBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwC5rB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAA;IACAA;IACAA;IACAA;IACAA;IACAA;EACA;EACAC;IACA;IACAD;EACA;EACAE;IACAC;MACAC,sBACAC;QACAV;QACAW;QACAV;MAEA,GACAW;QACA;UACAC;QACA;QACAR;QACAA;MACA,GACAS;QACAL;MACA;IACA;IACAM;MACA;MACA;MACA;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACAF;QACAC;MACA;IACA;IACAE;MACA;MACAH;QACAC;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/practice/topic/topic.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/practice/topic/topic.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./topic.vue?vue&type=template&id=80776818&\"\nvar renderjs\nimport script from \"./topic.vue?vue&type=script&lang=js&\"\nexport * from \"./topic.vue?vue&type=script&lang=js&\"\nimport style0 from \"./topic.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/practice/topic/topic.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./topic.vue?vue&type=template&id=80776818&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n    empty: function () {\n      return import(\n        /* webpackChunkName: \"components/empty/empty\" */ \"@/components/empty/empty.vue\"\n      )\n    },\n    adfootbanner: function () {\n      return import(\n        /* webpackChunkName: \"components/adfootbanner/adfootbanner\" */ \"@/components/adfootbanner/adfootbanner.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isLoad ? _vm.listData.length : null\n  var l0 = _vm.isLoad\n    ? _vm.__map(_vm.listData, function (item, idx) {\n        var $orig = _vm.__get_orig(item)\n        var g1 = _vm.listData.length\n        return {\n          $orig: $orig,\n          g1: g1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./topic.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./topic.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" :title=\"title\"></back>\r\n\r\n\t\t<view class=\"flex flex-direction\" style=\"min-height: 100vh;\" v-if=\"isLoad\">\r\n\t\t\t<view class=\"flex-sub\">\r\n\t\t\t\t<empty v-if=\"listData.length == 0\" info=\"暂无题目\"></empty>\r\n\r\n\t\t\t\t<view @tap=\"practiceTap\" v-if=\"listData.length > 0\" class=\"course-layout\" :data-index=\"idx\"\r\n\t\t\t\t\tv-for=\"(item, idx) in listData\" :key=\"item.idx\">\r\n\t\t\t\t\t<image class=\"course-icon\" src=\"/static/img/ic_topic_1.png\" v-if=\"item.question_type == 1\"></image>\r\n\r\n\t\t\t\t\t<image class=\"course-icon\" src=\"/static/img/ic_topic_2.png\" v-else-if=\"item.question_type == 2\">\r\n\t\t\t\t\t</image>\r\n\r\n\t\t\t\t\t<image class=\"course-icon\" src=\"/static/img/ic_topic_3.png\" v-else-if=\"item.question_type == 3\">\r\n\t\t\t\t\t</image>\r\n\r\n\t\t\t\t\t<image class=\"course-icon\" src=\"/static/img/ic_topic_4.png\" v-else-if=\"item.question_type == 4\">\r\n\t\t\t\t\t</image>\r\n\r\n\t\t\t\t\t<image class=\"course-icon\" src=\"/static/img/ic_topic_5.png\" v-else-if=\"item.question_type == 5\">\r\n\t\t\t\t\t</image>\r\n\r\n\t\t\t\t\t<image class=\"course-icon\" src=\"/static/img/ic_topic_8.png\" v-else-if=\"item.question_type == 8\">\r\n\t\t\t\t\t</image>\r\n\r\n\t\t\t\t\t<image class=\"course-icon\" src=\"/static/img/ic_topic_8.png\" v-else></image>\r\n\r\n\t\t\t\t\t<view class=\"course-name\">{{ item.name }}</view>\t\t\t\t\t<view class=\"count\">{{ item.count }}题</view>\r\n\r\n\t\t\t\t\t<text class=\"arrow cuIcon-title\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<adfootbanner></adfootbanner>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet app = getApp();\r\n\tlet that = null;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlistData: [],\r\n\t\t\t\ttitle: '',\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\tmainType: 0,\r\n\t\t\t\tid: '',\r\n\t\t\t\textend_id: '',\r\n\t\t\t\tappIsAudit: true,\r\n\t\t\t\tisIosVirtualPay: true,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthat = this;\r\n\t\t\tthat.id = options.id;\r\n\t\t\tthat.extend_id = options.extend_id ? options.extend_id : 0;\r\n\t\t\tthat.title = options.title;\r\n\t\t\tthat.mainType = options.mainType;\r\n\t\t\tthat.appIsAudit = app.globalData.checkAppIsAudit();\r\n\t\t\tthat.isIosVirtualPay = app.globalData.checkIsIosVirtualPay();\r\n\t\t},\r\n\t\tonShow: function(e) {\r\n\t\t\t//mainType:1.普通题库 2.收藏题目 3.错误题目 4.历史题库\r\n\t\t\tthat.getQuestionType();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetQuestionType() {\r\n\t\t\t\tapp.globalData.server\r\n\t\t\t\t\t.getRequest('course/get_question_type', {\r\n\t\t\t\t\t\tid: that.id,\r\n\t\t\t\t\t\ttype: that.mainType,\r\n\t\t\t\t\t\textend_id: that.extend_id,\r\n\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(function(e) {\r\n\t\t\t\t\t\tfor (var o = [], i = 0; i < e.data.length; i++) {\r\n\t\t\t\t\t\t\to.push(e.data[i]);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.listData = o;\r\n\t\t\t\t\t\tthat.isLoad = true;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(function(a) {\r\n\t\t\t\t\t\tapp.showToast('获取题型失败');\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tpracticeTap(t) {\r\n\t\t\t\tlet e = that.listData[t.currentTarget.dataset.index];\r\n\t\t\t\tlet o = that;\r\n\t\t\t\tlet i = `../practice?title=${o.title}&id=${o.id}&extend_id=${o.extend_id}&mainType=${o.mainType}&max_page=${e.max_page}&question_type=${e.question_type}&question_count=${e.count}`;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: i\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonBuyTap() {\r\n\t\t\t\tlet url = '/pages/practice/course/buy?id=' + that.id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonCourseTap() {\r\n\t\t\t\tlet url = '/pages/practice/course/detail?id=';\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url + that.id\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style src=\"./topic.css\"></style>"], "sourceRoot": ""}