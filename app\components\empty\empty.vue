<template>
	<view>
		<view class="margin-tb-sm padding-top-lg padding-bottom-xl">
			<view class="flex justify-center text-xsl">
				<view class="cuIcon-searchlist text-gray icon"></view>
			</view>
			<view class="flex justify-center padding-top-sm">
				<view class="text-gray text">{{ info }}</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		name: 'empty',
		data() {
			return {
				showBtn: false
			};
		},
		options: {
			multipleSlots: true,
			addGlobalClass: true
		},
		props: {
			info: {
				type: String,
				default: '暂无数据'
			},
			showAd: {
				type: Boolean,
				default: true
			}
		},
		beforeMount() {},
		methods: {

		}
	};
</script>