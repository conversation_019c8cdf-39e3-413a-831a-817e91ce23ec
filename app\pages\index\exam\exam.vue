<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" :showTitle="true" title="选择考试"></back>
		<view v-if="load" class="flex flex-direction" style="height: 100vh;">
			<view class="flex-sub">
				<view class="cu-bar bg-white solid-bottom">
					<view class="action">
						<text class="cuIcon-titles text-orange"></text>
						请选择考试类型
					</view>
				</view>
				<scroll-view scroll-y style="height: calc(100vh - 180rpx);">
					<view class="cu-list grid col-3">
						<view class="cu-item" @tap="examTap" :data-index="index" v-for="(item, index) in list" :key="index">
							<view class="cuIcon-discoverfill text-purple">
								<view class="cu-tag badge" v-if="item.badge != 0">
									<block v-if="item.count">{{ item.count }}人</block>
								</view>
							</view>
							<text>{{ item.name }}</text>
						</view>
					</view>
				</scroll-view>
			</view>
			<adfootbanner></adfootbanner>
		</view>
	</view>
</template>
<style src="./exam.css"></style>
<script src="./exam.js"></script>