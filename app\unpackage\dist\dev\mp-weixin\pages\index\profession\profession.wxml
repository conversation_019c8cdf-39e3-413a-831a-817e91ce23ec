<view><back vue-id="22cdcac6-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" showTitle="{{true}}" title="选择专业" bind:__l="__l"></back><block wx:if="{{$root.g0}}"><view class="flex flex-direction" style="min-height:100vh;"><view class="flex-sub" style="margin-bottom:270rpx;"><block wx:if="{{$root.g1>0}}"><view class="bg-white nav-fixed text-center" style="{{('top:'+customBar+'px')}}"><view class="tab-container"><block wx:for="{{cateData}}" wx:for-item="cate" wx:for-index="index" wx:key="index"><view class="{{['tab-item '+(cate.id==cateId?'active':'')]}}" data-id="{{cate.id}}" data-event-opts="{{[['tap',[['changeTap',['$event']]]]]}}" bindtap="__e">{{''+cate.name+''}}</view></block></view></view></block><view class="{{['cu-list menu '+($root.g2>0?'margin-top':'')]}}" style="{{($root.g3>0?'padding-top:70rpx':'')}}"><block wx:for="{{listData}}" wx:for-item="professionItem" wx:for-index="index" wx:key="index"><view class="cu-item arrow" data-data="{{professionItem}}" data-event-opts="{{[['tap',[['professionTap',['$event']]]]]}}" bindtap="__e"><view class="content"><text class="cuIcon-read text-pink"></text><text>{{professionItem.name}}</text></view><block wx:if="{{professionItem.old_name}}"><view class="action"><text class="text-sm">{{"原"+professionItem.old_name}}</text></view></block></view></block></view></view><adfootbanner vue-id="22cdcac6-2" bind:__l="__l"></adfootbanner></view></block></view>