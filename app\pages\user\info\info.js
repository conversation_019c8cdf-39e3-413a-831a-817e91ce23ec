import {
	appPlatform as getAppPlatform
} from "@/common/js/config";
let app = getApp();
let that = null;
export default {
	data() {
		return {
			user: {},
			isLoad: false,
			appPlatform: null,
		};
	},
	onLoad() {
		that = this;
		that.appPlatform = app.globalData.appPlatform;
	},
	onShow() {
		that.getUserInfo();
	},
	methods: {
		getUserInfo() {
			that.user = app.globalData.config.storage.getUserInfoData();
			that.isLoad = true;
		},
		editInfoTap() {
			let url = '/pages/user/info/edit';
			uni.navigateTo({
				url: url
			});
		},
		bindMailTap() {
			let url = '/pages/user/login/bindemail';
			uni.navigateTo({
				url: url
			});
		},
		bindMobileTap() {
			let url = '/pages/user/login/bindmobile';
			uni.navigateTo({
				url: url
			});
		},
		logoutTap(action) {
			let isExit = action == 2;
			let title = isExit ? '退出登录' : '注销账号';
			let content = isExit ? '确定要退出登录吗？' : '申请注销账号通过后会在7个工作日内删除您的账号全部信息，是否确认此操作？';
			let notcieInfo = isExit ? '退出成功' : '申请成功,受理成功后自动删除账号信息';
			let handleSuccess = res => {
				if (res.confirm) {
					app.showToast(notcieInfo);
					setTimeout(() => {
						app.globalData.config.storage.clearUserData();
						app.globalData.checkLogin();
						uni.navigateBack();
					}, 2000)
				}
			};
			uni.showModal({
				title: title,
				content: content,
				success: handleSuccess
			});
		},
		transferTap() {
			let url = '/pages/user/vip/transfer';
			uni.navigateTo({
				url: url
			});
		},
	}
};