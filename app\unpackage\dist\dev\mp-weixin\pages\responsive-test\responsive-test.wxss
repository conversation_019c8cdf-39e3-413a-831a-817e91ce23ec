
.responsive-test-page.data-v-f1ccd1c4 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.container.data-v-f1ccd1c4 {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20rpx;
}
.section.data-v-f1ccd1c4 {
  margin-bottom: 40rpx;
}
.section-title.data-v-f1ccd1c4 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #e0e0e0;
}
.info-card.data-v-f1ccd1c4 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.info-item.data-v-f1ccd1c4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-item.data-v-f1ccd1c4:last-child {
  border-bottom: none;
}
.label.data-v-f1ccd1c4 {
  font-weight: bold;
  color: #666;
}
.value.data-v-f1ccd1c4 {
  color: #007aff;
  font-weight: bold;
}
.test-grid.data-v-f1ccd1c4 {
  gap: 20rpx;
}
.grid-item.data-v-f1ccd1c4 {
  background: white;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.item-content.data-v-f1ccd1c4 {
  padding: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.display-test.data-v-f1ccd1c4 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.test-item.data-v-f1ccd1c4 {
  margin: 20rpx 0;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
}
.test-text.data-v-f1ccd1c4 {
  font-size: 28rpx;
  color: #333;
}
.text-align-test.data-v-f1ccd1c4 {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.spacing-test.data-v-f1ccd1c4 {
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.spacing-item.data-v-f1ccd1c4 {
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 2rpx solid #e9ecef;
}

/* 大屏优化 */
@media screen and (min-width: 1024rpx) {
.container.data-v-f1ccd1c4 {
    padding: 40rpx;
}
.section-title.data-v-f1ccd1c4 {
    font-size: 36rpx;
}
.info-card.data-v-f1ccd1c4,
  .display-test.data-v-f1ccd1c4,
  .text-align-test.data-v-f1ccd1c4,
  .spacing-test.data-v-f1ccd1c4 {
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}
.grid-item:hover .item-content.data-v-f1ccd1c4 {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
    transition: -webkit-transform 0.3s ease;
    transition: transform 0.3s ease;
    transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.test-item.data-v-f1ccd1c4:hover {
    -webkit-transform: translateY(-2rpx);
            transform: translateY(-2rpx);
    box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}
}

