let that = null;
let app = getApp();
export default {
	data() {
		return {
			user: {}
		};
	},
	onLoad() {
		that = this;
	},
	onShow() {
		let info = app.globalData.config.storage.getUserInfoData();
		if (info.email == "" && info.mobile == "") {
			app.showToast('请先绑定手机号或邮箱再设置密码');
			return;
		}
		that.user = info;
		that.password1 = that.password2 = "";
	},
	methods: {
		passwordInputTap1(options) {
			that.password1 = options.detail.value;
		},
		passwordInputTap2(options) {
			that.password2 = options.detail.value;
		},
		setPasswordTap(e) {
			if (app.globalData.appPlatform != 20 && app.globalData.appPlatform != 21) {
				app.showToast('请在小程序中设置密码');
				return;
			}
			if (that.password1 == "" || that.password2 == "") {
				app.showToast('请输入登录密码');
				return;
			}
			if (that.password1 != that.password2) {
				app.showToast('输入密码和确认密码不一致');
				return;
			}
			uni.login({
				success(e) {
					let code = e.code
					app.globalData.server
						.postRequest('user/changePassword', {
							code: code,
							password: that.password1,
						})
						.then(function(response) {
							app.showToast('设置成功');
							setTimeout(function() {
								uni.navigateBack({
									delta: 1
								});
							}, 1500)

						})
						.catch(function() {
							app.showToast('设置异常');
						});
				},
				fail() {
					app.showToast('获取用户信息失败');
				}
			});
		}
	}
};