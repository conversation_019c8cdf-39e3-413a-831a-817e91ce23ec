<view><back vue-id="456148c2-1" showBackText="{{false}}" customClass="bg-gradual-red text-white" title="积分乐园" bind:__l="__l"></back><block wx:if="{{isLoad==true}}"><view><view class="layout-section" style="margin-top:10rpx;"><view class="headinfo" style="border-radius:15rpx;background:linear-gradient(180deg, #ff2c3c, #ff316a);height:200rpx;"><view class="child"><view class="title">今日使用</view><view class="num"><text>{{info.score_out}}</text></view></view><view class="child"><view class="title">剩余积分</view><view class="num"><text>{{info.score}}</text></view></view><view class="child"><view class="title">今日获得</view><view class="num"><text>{{info.score_in}}</text></view></view></view></view><view class="cu-list menu sm-border card-menu margin-bottom"><block wx:for="{{info.record_list}}" wx:for-item="sign" wx:for-index="index" wx:key="index"><view class="cu-item flex" style="justify-content:flex-start;"><view class="action flex-sub"><view class="cu-tag round bg-green light">{{sign.source_name}}</view></view><view class="action flex-twice" style="margin-left:70rpx;"><view class="cu-tag round bg-green light">{{sign.add_time}}</view></view><view class="action flex-treble" style="margin-left:70rpx;"><view class="cu-tag round bg-green light">{{sign.score}}</view></view></view></block></view><view class="label activity_head"><view class="tag"></view>热门活动</view><view><block wx:if="{{info.is_sign==0}}"><view data-event-opts="{{[['tap',[['confirmSign',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">每日签到，点我签到</view><courselabel vue-id="456148c2-2" code="每天首次签到" topicCount="{{'赠送'+info.sign_number+'积分'}}" bind:__l="__l"></courselabel></view></view></block><block wx:if="{{info.is_bind_wx==0}}"><view data-event-opts="{{[['tap',[['bindWeiXinTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">绑定微信，快捷登录</view><courselabel vue-id="456148c2-3" code="首次绑定微信" topicCount="{{'赠送'+info.wx_number+'积分'}}" bind:__l="__l"></courselabel></view></view></block><block wx:if="{{info.is_bind_mobile==0&&!appIsAudit}}"><view data-event-opts="{{[['tap',[['bindMobileTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">绑定手机，快捷登录</view><courselabel vue-id="456148c2-4" code="首次绑定手机" topicCount="{{'赠送'+info.bind_mobile_number+'积分'}}" bind:__l="__l"></courselabel></view></view></block><block wx:if="{{info.is_bind_email==0&&!appIsAudit}}"><view data-event-opts="{{[['tap',[['bindEmailTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">绑定邮箱，快捷登录</view><courselabel vue-id="456148c2-5" code="首次绑定邮箱" topicCount="{{'赠送'+info.mail_number+'积分'}}" bind:__l="__l"></courselabel></view></view></block><block wx:if="{{info.is_bind_mp==0}}"><view data-event-opts="{{[['tap',[['bindMpTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">绑定公众号，学习不迷路</view><courselabel vue-id="456148c2-6" code="绑定公众号," topicCount="{{'赠送'+info.mp_number+'积分，每日可领'+info.mp_sign_number+'积分'}}" bind:__l="__l"></courselabel></view></view></block><block wx:if="{{info.is_bind_wx_group==0}}"><view data-event-opts="{{[['tap',[['bindWxGroupTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">微信学习群，免费领积分</view><courselabel vue-id="456148c2-7" code="微信学习群," topicCount="{{'赠送'+info.bind_wx_group_num+'积分'}}" bind:__l="__l"></courselabel></view></view></block><block wx:if="{{info.is_desktop==0}}"><view data-event-opts="{{[['tap',[['bindAddDeskTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">添加到手机桌面</view><courselabel vue-id="456148c2-8" code="首次添加到桌面" topicCount="{{'首次添加赠送'+info.add_desktop_number+'积分'}}" bind:__l="__l"></courselabel></view></view></block><block wx:if="{{info.is_bind_email==1}}"><view data-event-opts="{{[['tap',[['bindAddMyAppTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">添加到我的小程序</view><courselabel vue-id="456148c2-9" code="首次添加到我的小程序" topicCount="{{'赠送'+info.add_my_app_number+'积分'}}" bind:__l="__l"></courselabel></view></view></block><view data-event-opts="{{[['tap',[['bindShareTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">分享小程序，积分翻倍</view><courselabel vue-id="456148c2-10" code="转发分享微信群" topicCount="{{'赠送'+info.share_number+'乘以点击人数的积分'}}" bind:__l="__l"></courselabel></view></view><view data-event-opts="{{[['tap',[['bindShareFileTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">分享学习资料，送海量积分</view><courselabel vue-id="456148c2-11" code="上传课程资料" topicCount="{{'赠送'+info.share_file_number+'积分'}}" bind:__l="__l"></courselabel></view></view><block wx:if="{{info.is_watch_video==0}}"><view data-event-opts="{{[['tap',[['bindVideoTap',['$event']]]]]}}" class="item-layout" style="{{('border-bottom:'+borderWidth+'rpx solid #EDEDED;')}}" bindtap="__e"><view style="flex:1;"><view class="item-name">观看广告，获得奖励</view><courselabel vue-id="456148c2-12" code="广告观看完成" topicCount="{{'赠送'+info.video_number+'积分(每日最多'+info.video_count+'次)'}}" bind:__l="__l"></courselabel></view></view></block><view class="top-layout rules"><view style="margin-left:10rpx;font-weight:bold;margin-bottom:10rpx;">附加说明：</view><block wx:for="{{info.att_desc_list}}" wx:for-item="desc" wx:for-index="index" wx:key="index"><view style="padding:10rpx;">{{''+desc+''}}</view></block></view><confirm vue-id="456148c2-13" title="{{taskTitle}}" content="{{taskContent}}" status="{{taskDescModal}}" confirmText="我知道啦" confirmTextClass="cuIcon-roundcheck" data-event-opts="{{[['^updateStatus',[['__set_sync',['$0','taskDescModal','$event'],['']]]]]}}" bind:updateStatus="__e" bind:__l="__l"></confirm></view></view></block></view>