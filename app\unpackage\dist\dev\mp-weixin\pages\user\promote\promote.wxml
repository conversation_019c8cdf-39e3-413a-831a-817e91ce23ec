<block wx:if="{{isLoad}}"><view><back vue-id="1a303bc0-1" showBackText="{{false}}" customClass="bg-gradual-red text-white" title="推广中心" bind:__l="__l"></back><view><view class="cu-bar search bg-white padding solid-bottom"><view class="cu-avatar round" style="{{('background-image:url('+userInfo.avatar+')')}}"></view><view class="cu-capsule round"><view class="cu-tag bg-red border">总收益</view><view class="cu-tag line-red">{{''+info.user.today_income+'元'}}</view></view><view data-event-opts="{{[['tap',[['setPaymentModalTap',[true]]]]]}}" class="action line-red" bindtap="__e"><text>设置</text><text class="cuIcon-triangledownfill"></text></view></view><view class="cu-list grid col-3 no-border"><view class="cu-item" data-url="/pages/user/promote/group" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="cuIcon-friend text-red"></view><text>推广记录</text></view><view class="cu-item" data-url="/pages/user/promote/completedorder" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="cuIcon-order text-red"></view><text>成交订单</text></view><view class="cu-item" data-url="/pages/user/promote/settlement" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="cuIcon-pay text-red"></view><text>结算记录</text></view><view data-event-opts="{{[['tap',[['openSourceMaterialTap',['$event']]]]]}}" class="cu-item" bindtap="__e"><view class="cuIcon-pulldown text-orange"></view><text>宣传素材</text></view></view><view class="cu-bar bg-white solid-bottom"><view class="action"><text class="cuIcon-medalfill text-red"></text><view class="text-df">一级分红比例</view><view class="solid-bottom text-sm padding"><text class="text-grey">(最高50%)</text></view></view><view class="action"><button data-event-opts="{{[['tap',[['LoadModal',['$event']]]]]}}" class="cu-btn bg-red shadow" style="display:none;" bindtap="__e">提升</button></view></view><view class="padding bg-white"><view class="cu-progress striped active"><view class="bg-red" style="{{('width:'+info.user.bonus_rate_one)}}">{{info.user.bonus_rate_one}}</view></view></view><view class="cu-bar bg-white solid-bottom"><view class="action"><text class="cuIcon-medalfill text-red"></text><view class="text-df">二级分红比例</view><view class="solid-bottom text-sm padding"><text class="text-grey">(最高30%)</text></view></view><view class="action"><button data-event-opts="{{[['tap',[['LoadModal',['$event']]]]]}}" class="cu-btn bg-red shadow" style="display:none;" bindtap="__e">提升</button></view></view><view class="padding bg-white"><view class="cu-progress striped active"><view class="bg-red" style="{{('width:'+info.user.bonus_rate_two)}}">{{info.user.bonus_rate_two}}</view></view></view><view class="cu-bar bg-white solid-bottom solid-top"><view class="action"><text class="cuIcon-notificationfill text-red"></text><view class="text-df">推广规则说明</view></view></view><view class="bg-white"><block wx:for="{{info.rule}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="text-left padding-sm">{{item}}</view></block></view><view class="{{['cu-modal',showPaymentModal?'show':'']}}"><view class="cu-dialog"><view class="cu-bar bg-white justify-end"><view class="content text-red">收款设置</view><view class="action"><text data-event-opts="{{[['tap',[['setPaymentModalTap',[false]]]]]}}" class="cuIcon-close text-red" bindtap="__e"></text></view></view><view class="padding-xl"><view class="cu-form-group"><view class="title">姓名</view><input placeholder="请输入真实的姓名" data-field="realname" maxlength="10" data-event-opts="{{[['input',[['paymentInputTap',['$event']]]]]}}" value="{{userInfo.realname}}" bindinput="__e"/></view><view class="cu-form-group"><view class="title">账号</view><input placeholder="请输入支付宝账号" data-field="alipay_account" maxlength="20" data-event-opts="{{[['input',[['paymentInputTap',['$event']]]]]}}" value="{{userInfo.alipay_account}}" bindinput="__e"/></view></view><view class="cu-bar bg-white"><button data-event-opts="{{[['tap',[['savePaymentTap',['$event']]]]]}}" class="action margin flex-sub text-green" bindtap="__e"><text class="confirmTextClass"></text>保存</button></view></view></view></view></view></block>