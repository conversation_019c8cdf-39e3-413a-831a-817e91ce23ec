<template>
  <view>
    <back :showBackText="false" customClass="bg-gradual-blue text-white" :showTitle="true" title="选择专业"></back>
    <view v-if="load == true && allListData.length > 0" class="flex flex-direction" style="min-height: 100vh;">
      <view class="flex-sub" style="margin-bottom: 270rpx;">
        <view class="bg-white nav-fixed text-center" :style="'top:' + customBar + 'px'" v-if="cateData.length > 0">
          <view class="tab-container">
            <view :class="'tab-item ' + (cate.id == cateId ? 'active' : '')" @tap="changeTap"
                  :data-id="cate.id" v-for="(cate, index) in cateData" :key="index">
              {{ cate.name }}
            </view>
          </view>
        </view>
        <view :class="'cu-list menu ' + (cateData.length > 0 ? 'margin-top' : '')"
              :style="(cateData.length > 0 ? 'padding-top:70rpx' : '')">
          <view class="cu-item arrow" @tap="professionTap" :data-data="professionItem"
                v-for="(professionItem, index) in listData" :key="index">
            <view class="content">
              <text class="cuIcon-read text-pink"></text>
              <text class="">{{ professionItem.name }}</text>
            </view>
            <view class="action" v-if="professionItem.old_name">
              <text class="text-sm">原{{ professionItem.old_name }}</text>
            </view>
          </view>
        </view>
      </view>
      <adfootbanner></adfootbanner>
    </view>
  </view>
</template>

<style src="./profession.css"></style>
<script src="./profession.js"></script>