<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="意见反馈"></back>
		<form>
			<view class="cu-form-group" v-if="!appIsAudit">
				<input @input="contactInputTap" maxlength="32" placeholder="手机号/微信号/QQ，便于联系您(选填)"
					placeholderStyle="color:#999;" type="text" value="" />
			</view>
			<view class="cu-form-group">
				<textarea @input="contentInputTap" style="font-size: 30rpx" placeholder="请填写您要反馈的具体内容，方便我们尽快改进"
					placeholderStyle="color:#999;"></textarea>
			</view>
			<view class="padding flex flex-direction">
				<button @tap="submitTap" class="cu-btn bg-blue lg">提交</button>
			</view>
		</form>
		<adfootbanner unitId="adunit-1fb0622d11b3c262"></adfootbanner>
	</view>
</template>

<script>
	import adfootbanner from '@/components/adfootbanner/adfootbanner';
	let app = getApp();
	let that = null;
	export default {
		components: {
			adfootbanner
		},
		data() {
			return {
				isLoad: false,
				contact: '',
				content: '',
				feedbackList: [],
				appIsAudit: false
			};
		},
		onLoad: function(t) {
			that = this;
		},
		onShow: function() {
			that.appIsAudit = app.globalData.checkAppIsAudit();
		},
		methods: {
			contactInputTap: function(t) {
				that.contact = t.detail.value;
			},

			contentInputTap: function(t) {
				that.content = t.detail.value;
			},

			submitTap: function(n) {
				if (that.content) {
					app.globalData.server
						.postRequest('app/feedback', {
							detail: that.content,
							contact: that.contact || '',
							type: 1
						})
						.then(function(t) {
							uni.showToast({
								title: t.message,
								mask: true,
								complete: function() {
									setTimeout(function() {
										uni.navigateBack({
											delta: 1
										});
									}, 500);
								}
							});
						})
						.catch(function(a) {
							app.showToast('提交出错');
						});
				} else {
					app.showToast('反馈内容不能为空');
				}
			},

			copyTextTap: function(options) {
				let data = options.target.dataset.content;
				uni.setClipboardData({
					data: data,

					success(res) {
						console.log(res);
					}
				});
			}
		}
	};
</script>
<style src="./opinion.css"></style>
