<template>
	<view v-if="isLoad">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" :title="!user.email ? '绑定邮箱' : '解绑邮箱' "></back>
		<view v-if="!checkAppIsAudit && isLoad">
			<view class="cu-form-group">
				<view class="title">邮箱</view>
				<input @input="emailInputTap" maxlength="32" placeholder="请输入邮箱地址" placeholderStyle="color:#999;"
					type="text" :value="user.email ? user.email : ''" :disabled="user.email ? true : false" />
			</view>
			<view class="cu-form-group">
				<view class="title">验证</view>
				<input @input="inputVerifyCodeTap" maxlength="8" placeholder="请输入验证码" placeholderStyle="color:#999;"
					type="number" />
				<button @tap="sendVerifyCodeTap" class="cu-btn bg-green shadow">{{ countDown }}</button>
			</view>
			<view class="padding flex flex-direction">
				<button @tap="bindMailTap" class="cu-btn bg-blue lg">确定{{ !user.email ? '绑定' : '解绑' }}</button>
			</view>
			<view class="cu-form-group bg-white solid-bottom margin-bottom">
				<view class="action text-sm">
					<text class="cuIcon-notice text-blue"></text>
					：未收到邮件请尝试在邮件垃圾箱中查找
				</view>
			</view>
		</view>
		<adfootbanner unitId="adunit-e9f553c403a978f6"></adfootbanner>
	</view>
</template>

<script>
	let that = null,
		app = getApp(),
		cache = app.globalData.config.storage,
		service = app.globalData.service;
	export default {
		data() {
			return {
				email: '',
				action: 0,
				isLoad: false,
				verifyCode: '',
				countDown: '发送验证码',
				isCountDowning: false,
				user: {},
				checkAppIsAudit: true
			};
		},
		onLoad() {
			that = this;
			let info = cache.getUserInfoData();
			that.user = info;
			that.isLoad = true;
			that.email = info.email;
			that.action = info.email ? 3 : 2;
		},
		onShow() {
			that = this;
			that.checkAppIsAudit = app.globalData.checkAppIsAudit();
		},
		methods: {
			emailInputTap(options) {
				that.email = options.detail.value;
			},
			inputVerifyCodeTap(options) {
				that.verifyCode = options.detail.value
			},
			async sendVerifyCodeTap() {
				let email = that.email;
				if (!email) {
					app.showToast('邮箱地址不得为空');
					return;
				}
				if (that.isCountDowning) {
					app.showToast('请不要频繁操作');
					return;
				}
				let data = {
					key: email,
					action: that.action
				};
				await service.sendMailCode(data);

				// 倒计时
				let time = 60;
				let func = function() {
					that.isCountDowning = true;
					app.startInterval(function() {
						time--;
						that.countDown = `${time}s`;
						if (time <= 0) {
							app.stopInterval();
							that.countDown = '重新发送';
							that.isCountDowning = false;
						}
					}, 1000);
				};
				func();
			},
			async bindMailTap(e) {
				let email = that.email;
				let verifyCode = that.verifyCode;
				if (!email) {
					app.showToast('邮箱地址不得为空');
					return;
				}
				if (!verifyCode) {
					app.showToast('验证码不得为空');
					return;
				}
				await service.changeMail({
					email: email,
					action: !that.user.email ? 1 : 2,
					verifyCode: verifyCode
				});
				app.showToast('操作成功');
				uni.navigateBack({
					delta: 1
				});
			}
		}
	};
</script>
<style src="./bindemail.css"></style>