<view><back vue-id="5c8e94fd-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="授权登录" bind:__l="__l"></back><view class="sso-auth-container"><view class="sso-auth-header"><view class="sso-auth-icon"><text class="cuIcon-time text-blue" style="font-size:80rpx;"></text></view><view class="sso-auth-title">授权登录网页端</view><view class="sso-auth-desc">若非本人操作，请忽略申请</view></view><view class="sso-auth-btns"><button data-event-opts="{{[['tap',[['confirmTap',['$event']]]]]}}" class="cu-btn bg-blue shadow radius lg sso-auth-confirm" bindtap="__e">确认登录</button><button data-event-opts="{{[['tap',[['cancelTap',['$event']]]]]}}" class="cu-btn line-blue radius lg sso-auth-cancel" bindtap="__e">取消</button></view></view></view>