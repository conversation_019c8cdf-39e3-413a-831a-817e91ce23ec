let that = null;
let app = getApp();
export default {
	data() {
		return {
			load: false,
			list: [],
			appIsAudit: false
		};
	},
	onLoad() {
		that = this;
		app.globalData.checkAppIsAuditAndRedirect();
		that.getInfo();
	},
	onShow() {},
	methods: {
		getInfo() {
			app.globalData.server
				.getRequest('user/about', {})
				.then(function(e) {
					that.setData({
						list: e.data,
						load: true
					});
				})
				.catch(function(e) {
					app.showToast('获取信息失败');
					console.log(e);
				});
		}
	}
};