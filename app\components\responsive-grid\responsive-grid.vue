<template>
  <view class="responsive-grid" :class="gridClass" :style="gridStyle">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'responsive-grid',
  props: {
    // 列数配置 {xs: 1, sm: 2, md: 3, lg: 4, xl: 5}
    cols: {
      type: Object,
      default: () => ({
        xs: 1,
        sm: 2, 
        md: 3,
        lg: 4,
        xl: 5
      })
    },
    // 间距
    gap: {
      type: [String, Number],
      default: '20rpx'
    },
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    gridClass() {
      return [
        'responsive-grid',
        this.customClass
      ].filter(Boolean).join(' ');
    },
    gridStyle() {
      const gap = typeof this.gap === 'number' ? `${this.gap}rpx` : this.gap;
      return {
        gap,
        display: 'grid'
      };
    }
  }
}
</script>

<style scoped>
.responsive-grid {
  display: grid;
  width: 100%;
  box-sizing: border-box;
}

/* 超小屏幕 (xs) */
@media screen and (max-width: 576rpx) {
  .responsive-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* 小屏幕 (sm) */
@media screen and (min-width: 577rpx) and (max-width: 768rpx) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 中等屏幕 (md) */
@media screen and (min-width: 769rpx) and (max-width: 1024rpx) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 大屏幕 (lg) */
@media screen and (min-width: 1025rpx) and (max-width: 1440rpx) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 超大屏幕 (xl) */
@media screen and (min-width: 1441rpx) {
  .responsive-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* 自定义列数类 */
.responsive-grid.cols-1 { grid-template-columns: repeat(1, 1fr); }
.responsive-grid.cols-2 { grid-template-columns: repeat(2, 1fr); }
.responsive-grid.cols-3 { grid-template-columns: repeat(3, 1fr); }
.responsive-grid.cols-4 { grid-template-columns: repeat(4, 1fr); }
.responsive-grid.cols-5 { grid-template-columns: repeat(5, 1fr); }
.responsive-grid.cols-6 { grid-template-columns: repeat(6, 1fr); }
</style>
