{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/user/login/bindemail.vue?5c7c", "webpack:///D:/桌面/thinker/app/pages/user/login/bindemail.vue?02c0", "webpack:///D:/桌面/thinker/app/pages/user/login/bindemail.vue?2419", "webpack:///D:/桌面/thinker/app/pages/user/login/bindemail.vue?6081", "uni-app:///pages/user/login/bindemail.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "app", "cache", "service", "data", "email", "action", "isLoad", "verifyCode", "countDown", "isCountDowning", "user", "checkAppIsAudit", "onLoad", "that", "onShow", "methods", "emailInputTap", "inputVerifyCodeTap", "sendVerifyCodeTap", "key", "time", "func", "bindMailTap", "uni", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA4qB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8BhsB;EACAC;EACAC;EACAC;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACAA;IACAA;IACAA;IACAA;EACA;EACAC;IACAD;IACAA;EACA;EACAE;IACAC;MACAH;IACA;IACAI;MACAJ;IACA;IACAK;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAd;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAJ;gBAAA;cAAA;gBAAA,KAGAa;kBAAA;kBAAA;gBAAA;gBACAb;gBAAA;cAAA;gBAGAG;kBACAgB;kBACAd;gBACA;gBAAA;gBAAA,OACAH;cAAA;gBAEA;gBACAkB;gBACAC;kBACAR;kBACAb;oBACAoB;oBACAP;oBACA;sBACAb;sBACAa;sBACAA;oBACA;kBACA;gBACA;gBACAQ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAlB;gBACAG;gBAAA,IACAH;kBAAA;kBAAA;gBAAA;gBACAJ;gBAAA;cAAA;gBAAA,IAGAO;kBAAA;kBAAA;gBAAA;gBACAP;gBAAA;cAAA;gBAAA;gBAAA,OAGAE;kBACAE;kBACAC;kBACAE;gBACA;cAAA;gBACAP;gBACAuB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B", "file": "pages/user/login/bindemail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/login/bindemail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./bindemail.vue?vue&type=template&id=7d254d8e&\"\nvar renderjs\nimport script from \"./bindemail.vue?vue&type=script&lang=js&\"\nexport * from \"./bindemail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./bindemail.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/login/bindemail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bindemail.vue?vue&type=template&id=7d254d8e&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n    adfootbanner: function () {\n      return import(\n        /* webpackChunkName: \"components/adfootbanner/adfootbanner\" */ \"@/components/adfootbanner/adfootbanner.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bindemail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bindemail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"isLoad\">\r\n\t\t<back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" :title=\"!user.email ? '绑定邮箱' : '解绑邮箱' \"></back>\r\n\t\t<view v-if=\"!checkAppIsAudit && isLoad\">\r\n\t\t\t<view class=\"cu-form-group\">\r\n\t\t\t\t<view class=\"title\">邮箱</view>\r\n\t\t\t\t<input @input=\"emailInputTap\" maxlength=\"32\" placeholder=\"请输入邮箱地址\" placeholderStyle=\"color:#999;\"\r\n\t\t\t\t\ttype=\"text\" :value=\"user.email ? user.email : ''\" :disabled=\"user.email ? true : false\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cu-form-group\">\r\n\t\t\t\t<view class=\"title\">验证</view>\r\n\t\t\t\t<input @input=\"inputVerifyCodeTap\" maxlength=\"8\" placeholder=\"请输入验证码\" placeholderStyle=\"color:#999;\"\r\n\t\t\t\t\ttype=\"number\" />\r\n\t\t\t\t<button @tap=\"sendVerifyCodeTap\" class=\"cu-btn bg-green shadow\">{{ countDown }}</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"padding flex flex-direction\">\r\n\t\t\t\t<button @tap=\"bindMailTap\" class=\"cu-btn bg-blue lg\">确定{{ !user.email ? '绑定' : '解绑' }}</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cu-form-group bg-white solid-bottom margin-bottom\">\r\n\t\t\t\t<view class=\"action text-sm\">\r\n\t\t\t\t\t<text class=\"cuIcon-notice text-blue\"></text>\r\n\t\t\t\t\t：未收到邮件请尝试在邮件垃圾箱中查找\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<adfootbanner unitId=\"adunit-e9f553c403a978f6\"></adfootbanner>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet that = null,\r\n\t\tapp = getApp(),\r\n\t\tcache = app.globalData.config.storage,\r\n\t\tservice = app.globalData.service;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\temail: '',\r\n\t\t\t\taction: 0,\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\tverifyCode: '',\r\n\t\t\t\tcountDown: '发送验证码',\r\n\t\t\t\tisCountDowning: false,\r\n\t\t\t\tuser: {},\r\n\t\t\t\tcheckAppIsAudit: true\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthat = this;\r\n\t\t\tlet info = cache.getUserInfoData();\r\n\t\t\tthat.user = info;\r\n\t\t\tthat.isLoad = true;\r\n\t\t\tthat.email = info.email;\r\n\t\t\tthat.action = info.email ? 3 : 2;\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthat = this;\r\n\t\t\tthat.checkAppIsAudit = app.globalData.checkAppIsAudit();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\temailInputTap(options) {\r\n\t\t\t\tthat.email = options.detail.value;\r\n\t\t\t},\r\n\t\t\tinputVerifyCodeTap(options) {\r\n\t\t\t\tthat.verifyCode = options.detail.value\r\n\t\t\t},\r\n\t\t\tasync sendVerifyCodeTap() {\r\n\t\t\t\tlet email = that.email;\r\n\t\t\t\tif (!email) {\r\n\t\t\t\t\tapp.showToast('邮箱地址不得为空');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.isCountDowning) {\r\n\t\t\t\t\tapp.showToast('请不要频繁操作');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tkey: email,\r\n\t\t\t\t\taction: that.action\r\n\t\t\t\t};\r\n\t\t\t\tawait service.sendMailCode(data);\r\n\r\n\t\t\t\t// 倒计时\r\n\t\t\t\tlet time = 60;\r\n\t\t\t\tlet func = function() {\r\n\t\t\t\t\tthat.isCountDowning = true;\r\n\t\t\t\t\tapp.startInterval(function() {\r\n\t\t\t\t\t\ttime--;\r\n\t\t\t\t\t\tthat.countDown = `${time}s`;\r\n\t\t\t\t\t\tif (time <= 0) {\r\n\t\t\t\t\t\t\tapp.stopInterval();\r\n\t\t\t\t\t\t\tthat.countDown = '重新发送';\r\n\t\t\t\t\t\t\tthat.isCountDowning = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t};\r\n\t\t\t\tfunc();\r\n\t\t\t},\r\n\t\t\tasync bindMailTap(e) {\r\n\t\t\t\tlet email = that.email;\r\n\t\t\t\tlet verifyCode = that.verifyCode;\r\n\t\t\t\tif (!email) {\r\n\t\t\t\t\tapp.showToast('邮箱地址不得为空');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!verifyCode) {\r\n\t\t\t\t\tapp.showToast('验证码不得为空');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tawait service.changeMail({\r\n\t\t\t\t\temail: email,\r\n\t\t\t\t\taction: !that.user.email ? 1 : 2,\r\n\t\t\t\t\tverifyCode: verifyCode\r\n\t\t\t\t});\r\n\t\t\t\tapp.showToast('操作成功');\r\n\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\tdelta: 1\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style src=\"./bindemail.css\"></style>"], "sourceRoot": ""}