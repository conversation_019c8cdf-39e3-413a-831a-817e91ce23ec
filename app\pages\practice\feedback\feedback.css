page {
    background: #f5f5f5;
}

.text-label {
    height: 70rpx;
    line-height: 70rpx;
    font-size: 26rpx;
    color: #666;
    padding-left: 30rpx;
}

.feedback-layout {
    background: white;
    margin: 20rpx 30rpx 30rpx 30rpx;
    border-radius: 30rpx;
    padding: 1rpx 0 30rpx 0;
}

.layout-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-radius: 20rpx;
    background: #f5f5f5;
    margin: 30rpx 30rpx 0 30rpx;
}

.layout-item image {
    width: 30rpx;
    height: 30rpx;
    margin-left: 20rpx;
}

.text-item {
    flex: 1;
    font-size: 30rpx;
    color: #333;
}

.layout-content {
    background: white;
    display: flex;
    padding: 20rpx;
    margin: 20rpx 30rpx 30rpx 30rpx;
    border-radius: 30rpx;
}

.layout-content text {
    font-size: 30rpx;
    color: #333;
}

.input-content {
    flex: 1;
    height: 250rpx;
    font-size: 30rpx;
    color: #333;
}

.input-phone {
    flex: 1;
    margin-left: 20rpx;
    align-self: center;
    font-size: 30rpx;
    color: #333;
}

.btn-submit {
    height: 70rpx;
    line-height: 70rpx;
    text-align: center;
    background: #6097fe;
    color: white;
    font-size: 28rpx;
    border-radius: 10rpx;
    margin: 60rpx 40rpx 60rpx 40rpx;
}