<view><back vue-id="704277e0-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="我的错题" bind:__l="__l"></back><view class="flex flex-direction" style="min-height:100vh;"><view class="flex-sub" style="margin-bottom:270rpx;"><block wx:for="{{listData}}" wx:for-item="course" wx:for-index="idx" wx:key="idx"><view class="course-layout margin-bottom-xs" data-item="{{course}}" data-event-opts="{{[['tap',[['courseTap',['$event']]]]]}}" bindtap="__e"><view class="name-layout"><text>{{course.name}}</text><courselabel vue-id="{{'704277e0-2-'+idx}}" code="{{course.code}}" topicCount="{{'共'+course.count+'题'}}" bind:__l="__l"></courselabel></view><text class="cuIcon-right"></text></view></block><block wx:if="{{$root.g0}}"><empty vue-id="704277e0-3" bind:__l="__l"></empty></block></view><adfootbanner vue-id="704277e0-4" bind:__l="__l"></adfootbanner></view></view>