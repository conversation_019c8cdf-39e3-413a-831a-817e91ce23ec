<block wx:if="{{!appIsAudit&&load}}"><view><back vue-id="7e0affe2-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="{{info.wx_group_title}}" bind:__l="__l"></back><view class="cu-bar bg-white solid-bottom"><view class="action"><text class="cuIcon-weixin text-blue"></text>{{info.wx_group_name+''}}</view></view><view><view class="cu-card case"><view class="cu-item shadow"><view class="image"><image src="{{info.wx_group_image}}" show-menu-by-longpress="true" mode="aspectFit"></image><view class="cu-bar bg-shadeBottom" style="justify-content:center;"><text class="text-cut">{{info.wx_group_scan_title}}</text></view></view></view></view></view><view class="cu-bar bg-white solid-bottom"><view class="action"><text class="cuIcon-markfill text-blue"></text>{{info.wx_group_rule_title+''}}</view></view><view class="cu-list menu"><block wx:for="{{info.wx_group_rule_list}}" wx:for-item="value" wx:for-index="index" wx:key="index"><view class="cu-item"><view class="content"><text class="text-df">{{value}}</text></view></view></block></view></view></block>