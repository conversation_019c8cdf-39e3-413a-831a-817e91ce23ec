<template>
  <view class="responsive-container" :class="containerClass" :style="containerStyle">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'responsive-container',
  props: {
    // 最大宽度
    maxWidth: {
      type: [String, Number],
      default: '1200px'
    },
    // 是否居中
    center: {
      type: Boolean,
      default: true
    },
    // 内边距
    padding: {
      type: [String, Number],
      default: '20rpx'
    },
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    containerClass() {
      return [
        'responsive-container',
        this.center ? 'responsive-container--center' : '',
        this.customClass
      ].filter(Boolean).join(' ');
    },
    containerStyle() {
      const maxWidth = typeof this.maxWidth === 'number' ? `${this.maxWidth}px` : this.maxWidth;
      const padding = typeof this.padding === 'number' ? `${this.padding}rpx` : this.padding;
      
      return {
        maxWidth,
        padding,
        width: '100%',
        boxSizing: 'border-box'
      };
    }
  }
}
</script>

<style scoped>
.responsive-container {
  width: 100%;
  box-sizing: border-box;
}

.responsive-container--center {
  margin: 0 auto;
}

/* 小屏幕 */
@media screen and (max-width: 768rpx) {
  .responsive-container {
    padding: 20rpx !important;
  }
}

/* 中等屏幕 */
@media screen and (min-width: 769rpx) and (max-width: 1024rpx) {
  .responsive-container {
    padding: 40rpx !important;
  }
}

/* 大屏幕 */
@media screen and (min-width: 1025rpx) {
  .responsive-container {
    padding: 60rpx !important;
    max-width: 1200px;
    margin: 0 auto;
  }
}

/* 超大屏幕 */
@media screen and (min-width: 1440rpx) {
  .responsive-container {
    max-width: 1400px;
    padding: 80rpx !important;
  }
}
</style>
