.article-container {
    background-color: #f8f8f8;
    min-height: 100vh;
}

.article-wrapper {
    padding-bottom: 270rpx;
}

.article-content {
    background-color: #fff;
    margin: 10rpx; 
    border-radius: 12rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    padding: 40rpx 30rpx;
}

.article-header {
    margin-bottom: 30rpx;
}

.article-title {
    font-size: 40rpx;
    font-weight: 700;
    color: #333;
    line-height: 1.4;
    display: block;
    margin-bottom: 20rpx;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    font-size: 24rpx;
    color: #999;
    margin-top: 20rpx;
}

.meta-item {
    display: flex;
    align-items: center;
    margin-right: 30rpx;
    margin-bottom: 10rpx;
}

.meta-item text {
    margin-right: 6rpx;
}

.article-summary {
    font-size: 28rpx;
    color: #666;
    background-color: #f5f7fa;
    padding: 20rpx 30rpx;
    border-left: 8rpx solid #3a86ff;
    border-radius: 6rpx;
    margin: 20rpx 0 40rpx;
    line-height: 1.6;
}

.article-body {
    font-size: 30rpx;
    color: #333;
    line-height: 1.8;
    letter-spacing: 0.5rpx;
    margin-bottom: 40rpx;
}

.article-body >>> p {
    margin-bottom: 20rpx;
    text-indent: 2em;
}

.article-body >>> img {
    max-width: 100%;
    height: auto;
    margin: 20rpx 0;
    border-radius: 8rpx;
}

.article-tags {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: 30rpx 0;
    font-size: 24rpx;
}

.tag-title {
    color: #666;
    margin-right: 10rpx;
}

.tag-item {
    background-color: #eef2fd;
    color: #3a86ff;
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
}

.article-actions {
    display: flex;
    justify-content: center;
    margin-top: 50rpx;
    padding-top: 30rpx;
    border-top: 1rpx solid #eee;
}

.action-btn {
    background-color: #3a86ff;
    color: #fff;
    font-size: 28rpx;
    padding: 16rpx 40rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn text {
    margin: 0 6rpx;
}

.share-btn {
    background-color: #3a86ff;
    color: #fff;
}
