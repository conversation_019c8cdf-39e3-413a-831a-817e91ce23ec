.top_bg {
    width: 750rpx;
    height: 650rpx;
    background: url('https://learnfile.20230611.cn/learnAppClient/97/f8/97f8242d971b24c073425d95f9aff86f.png') no-repeat;
    background-size: 750rpx;
    position: relative;
}

.one_box {
    width: 750rpx;
    height: 260rpx;
    position: absolute;
    left: 0;
    bottom: 110rpx;
    display: flex;
    justify-content: space-around;
}

.one_box .top3 {
    width: 210rpx;
    height: 280rpx;
}

.top_name {
    width: 100%;
    height: 55rpx;
    line-height: 60rpx;
    color: #f2f2f2;
    font-size: 26rpx;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.top_sy {
    width: 100%;
    height: 40rpx;
    line-height: 40rpx;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.top_sy span {
    font-size: 20rpx !important;
}

.num_one {
    position: relative;
}

.huangguan1 {
    width: 60px;
    height: 60px;
    position: absolute;
    right: -10rpx;
    top: -36rpx;
}

.num_one .top3_head {
    width: 150rpx;
    height: 150rpx;
    border-radius: 100%;
    margin: 15rpx 0 0 30rpx;
    border: 4rpx solid #ffdd3c;
}

.num_two {
    position: relative;
}

.huangguan2 {
    width: 100rpx;
    height: 100rpx;
    position: absolute;
    right: 15rpx;
}

.num_two .top3_head {
    width: 120rpx;
    height: 120rpx;
    border-radius: 100%;
    margin: 45rpx 0 0 45rpx;
    border: 4rpx solid #bcdcdf;
}

.num_three {
    position: relative;
}

.huangguan2 {
    width: 100rpx;
    height: 100rpx;
    position: absolute;
    right: 15rpx;
}

.num_three .top3_head {
    width: 120rpx;
    height: 120rpx;
    border-radius: 100%;
    margin: 45rpx 0 0 45rpx;
    border: 4rpx solid #e29d85;
}

.number_sy_box {
    width: 700rpx;
    height: 210rpx;
    background-color: #ffffff;
    position: absolute;
    left: 25rpx;
    border-radius: 20rpx;
    bottom: -115rpx;
    z-index: 999;
    padding: 22rpx;
    box-shadow: 3px 3px 15px 3px rgba(0, 0, 0, 0.1);
}

.number_sy_box_title {
    color: #888888;

    font-size: 28rpx;
    display: flex;
    z-index: 9999;
    justify-content: space-between;
}

.number_sy_main {
    width: 100%;
    height: 124rpx;
    padding-top: 20rpx;
    line-height: 52rpx;

    display: flex;
    justify-content: space-around;
    position: relative;
}

.xiaoding_bg {
    position: absolute;
    right: -22rpx;
    bottom: -30rpx;
    width: 180rpx;
}

.number_num1 {
    font-size: 40rpx;
    font-weight: 500;
    color: #2fc04f;
}

.number_num2 {
    font-size: 40rpx;
    font-weight: 500;
    color: #4bac7f;
}

.danwei {
    height: 60rpx;
    line-height: 60rpx;
    font-size: 26rpx;
    color: #c9c9c9;
}

.rankList_box {
    width: 750rpx;
    min-height: 200rpx;
    margin-top: 130rpx;
}

.rankItem:last-child {
    border: none;
}

.rankItem {
    width: 700rpx;
    height: 140rpx;
    margin: 0px auto;
    border-bottom: 1px solid #e9e9e9;
}

.rankIndex {
    width: 60rpx;
    height: 140rpx;
    line-height: 140rpx;
    text-align: center;
    color: #cccccc;
    font-size: 40rpx;
    float: left;
}

.HeardBox {
    width: 100rpx;
    height: 100rpx;
    margin: 20rpx;
    border-radius: 100%;
    overflow: hidden;
    float: left;
    margin-right: 25rpx;
    margin-left: 10rpx !important;
}

.HeardBox image {
    width: 100%;
    height: 100%;
}

.NameBox {
    width: 400rpx;
    height: 140rpx;
    float: left;
    padding-top: 10rpx;
    box-sizing: border-box;
}

.NameBox view {
    height: 50rpx;
    line-height: 70rpx;
}

.userName {
    min-width: 90rpx;
    font-size: 30rpx;
    float: left;
    margin-right: 20rpx;
}

.download_box {
    width: 80rpx;
    height: 140rpx;
    float: right;
}
.download_box image {
    width: 50rpx;
    margin: 68rpx auto;
    display: block;
}
