const storage = {
	homeKey: 'CACHE_HOME',
	agentId<PERSON><PERSON>: 'CACHE_AGENT_ID',
	materialKey: 'CACHE_MATERIAL',
	userInfoKey: 'CACHE_USER',
	userTokenKey: 'CACHE_USER_TOKEN',
	currentExam<PERSON>ey: 'CACHE_EXAM',
	currentCityKey: 'CACHE_CITY',
	currentSchoolKey: 'CACHE_SCHOOL',
	currentProfessionKey: 'CACHE_MAJOR',
	openMiniKey: 'CACHE_OPENAPP',
	usernameKey: 'CACHE_USER_LAST',
	practiceCorrectNextKey: 'CACHE_PRACTICE_CORRECT_NEXT',
	practiceAllNextKey: 'CACHE_PRACTICE_ALL_NEXT',
	practiceIsNightKey: 'CACHE_PRACTICE_IS_NIGHT',
	practiceQuestionFontKey: 'CACHE_PRACTICE_QUESTION_FONT',
	practiceNextDialogKey: 'CACHE_PRACTICE_NEXT_DIALOG',
	setHomeData(data) {
		uni.setStorageSync(this.homeKey, data);
	},
	getHomeData() {
		return uni.getStorageSync(this.homeKey);
	},
	setAgentIdData(data) {
		uni.setStorageSync(this.agentIdKey, data);
	},
	getAgentIdData() {
		return uni.getStorageSync(this.agentIdKey);
	},
	setUserInfoData(data) {
		uni.setStorageSync(this.userInfoKey, data);
	},
	getUserInfoData() {
		return uni.getStorageSync(this.userInfoKey);
	},
	setUserTokenData(data) {
		uni.setStorageSync(this.userTokenKey, data);
	},
	getUserTokenData() {
		return uni.getStorageSync(this.userTokenKey);
	},
	clearUserData() {
		uni.removeStorageSync(this.userInfoKey), uni.removeStorageSync(this.userTokenKey);
	},
	setCurrentCityData(data) {
		uni.setStorageSync(this.currentCityKey, data);
	},
	getCurrentCityData() {
		return uni.getStorageSync(this.currentCityKey);
	},
	setCurrentProfessionData(data) {
		uni.setStorageSync(this.currentProfessionKey, data);
	},
	getCurrentProfessionData() {
		return uni.getStorageSync(this.currentProfessionKey);
	},
	setCurrentExamData(data) {
		uni.setStorageSync(this.currentExamKey, data);
	},
	getCurrentExamData() {
		return uni.getStorageSync(this.currentExamKey);
	},
	setCurrentSchoolData(data) {
		uni.setStorageSync(this.currentSchoolKey, data);
	},
	getCurrentSchoolData() {
		return uni.getStorageSync(this.currentSchoolKey);
	},
	setMaterialData(url, data) {
		uni.setStorageSync(this.materialKey + url, data);
	},
	getMaterialData(url) {
		return uni.getStorageSync(this.materialKey + url);
	},
	setFristData(data) {
		uni.setStorageSync(this.openMiniKey, data);
	},
	getFristData() {
		return uni.getStorageSync(this.openMiniKey);
	}
};

const appId = 1000;
//let apiHost = 'https://api.20230611.cn';
//const apiHost = 'http://127.0.0.1:8000';
const apiHost = 'https://shell-api.5b1.cn';
const ossHost = 'https://oss.5b1.cn';
module.exports = {
	appId,
	appVersion: '20250616',
	apiUrl: apiHost + '/api/',
	apiHost,
	ossHost,
	storage,
	appPlatform() {
		let platform = 0;
		const result = uni.getDeviceInfo();
		const appPlatformText = result.platform;

		//H5
		//#ifdef H5
		const appUserAgent = window.navigator.userAgent.toLowerCase()
		platform = 10; //标准H5
		if (appUserAgent.match(/MicroMessenger/i) == 'micromessenger') {
			platform = 11; //微信浏览器环境 
		}
		//#endif

		//微信小程序
		//#ifdef MP-WEIXIN
		platform = 20; //手机
		if (appPlatformText == 'windows' || appPlatformText == 'mac') {
			platform = 21; //电脑
		}
		//#endif

		// APP环境
		//#ifdef APP-PLUS
		platform = 30;
		//#endif

		//百度小程序
		//#ifdef MP-BAIDU
		platform = 40; //手机
		if (appPlatformText == 'windows' || appPlatformText == 'mac') {
			platform = 41; //电脑
		}
		//#endif

		//百度小程序
		//#ifdef MP-TOUTIAO
		platform = 50; //手机
		if (appPlatformText == 'windows' || appPlatformText == 'mac') {
			platform = 51; //电脑
		}
		//#endif


		//返回
		return platform;
	}
};