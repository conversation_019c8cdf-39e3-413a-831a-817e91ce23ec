<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="我的错题"></back>
		<view class="flex flex-direction" style="min-height: 100vh;">
			<view class="flex-sub" style="margin-bottom: 270rpx;">
				<view @tap="courseTap" class="course-layout margin-bottom-xs" :data-item="course"
					v-for="(course, idx) in listData" :key="idx">
					<view class="name-layout">
						<text>{{ course.name }}</text>
						<courselabel :code="course.code" :topicCount="'共'+course.count + '题'"></courselabel>					</view>
					<text class="cuIcon-right"></text>
				</view>
				<empty v-if="(isLoad  &&  listData.length <= 0)"></empty>
			</view>
			<adfootbanner></adfootbanner>
		</view>
	</view>
</template>
<style src="./error.css"></style>
<script src="./error.js"></script>