let app = getApp();
let that = null;
import {
	get,
	post
} from "@/common/js/http.js";
import faIcon from "@/components/kilvn-fa-icon/fa-icon.vue"
import confirm from "@/components/confirm/confirm.vue"
import activateModal from "@/components/activateModal/activateModal.vue"

export default {
	components: {
		faIcon,
		confirm,
		activateModal
	},
	data() {
		return {
			info: [],
			isLoad: false,
			categoryList: [],
			checkAppIsAudit: true,
			isIosVirtualPay: true,
			showActivateModal: false,
			activateCode: '',
			showOnlineServiceNotice: false,
			detail_page_set: {},
			exam_countdown: 10, // 考试倒计时天数
			correct_rate: 0, // 答题正确率
			today_question_count: 0, // 今日做题数量 
			total_question_count: 0, // 总答题数
			profession_course_id: 0, // 专业课程ID
		};
	},
	onLoad(options) {
		that = this;
		that.profession_course_id = options.profession_course_id || 0;
		that.getInfo(options.id);
		that.getPageConfig();
	},
	onShow(options) {
		app.globalData.showShareMenu();
		that.checkAppIsAudit = app.globalData.checkAppIsAudit();
		that.isIosVirtualPay = app.globalData.checkIsIosVirtualPay();
	},
	onShareAppMessage() {
		let info = that.info;
		let config = app.globalData.getShareConfig();
		config.title = info.name;
		config.path = '/pages/practice/course/detail?id=' + info.id;
		return config;
	},
	methods: {
		async getPageConfig() {
			try {
				let res = await post('appConfig/get', { config_type: 'course_detail_page_set' });
				that.detail_page_set = res.data;
			} catch (error) {
				console.error('获取页面配置失败', error);
			}
		},
		async getInfo(id) {
			const params = {
				id: id
			};
			app.globalData.server
				.getRequest('course/getInfo', params)
				.then(function (res) {
					that.info = res.data;
					
				})
				.catch(function (res) {
					app.showToast('获取信息失败');
				});
			const categoryRes = await get('paper/category/list', {
				course_id: id,
				level: 1
			});
			that.categoryList = categoryRes.data;

			// 获取考试倒计时和答题正确率
			try {
				const statsParams = {
					course_id: id
				};
				
				// 如果有专业课程ID，添加到请求参数中
				if (that.profession_course_id) {
					statsParams.profession_course_id = that.profession_course_id;
				}
				
				const statsRes = await get('course/getUserStats', statsParams);
				if (statsRes && statsRes.data) {
					that.exam_countdown = statsRes.data.exam_countdown || 0;
					that.correct_rate = statsRes.data.correct_rate || 0;
					that.today_question_count = statsRes.data.today_question_count || 0;
					that.total_question_count = statsRes.data.total_question_count || 0;
				}
				that.isLoad = true;
			} catch (error) {
				console.error('获取用户统计数据失败', error);
			}
		},
		onLearn(options) {
			let url = '/pages/practice/material/list?title=' + that.info.name + '&id=' + that.info.id;
			uni.navigateTo({
				url: url
			});
		},
		async onQuestionTypePractice() {
			const countInfo = await get('question/getCount', {
				course_id: that.info.id
			});
			const countInfoData = countInfo.data;
			if (countInfoData.count <= 0) {
				app.showToast('题库未上传题目');
				return;
			}
			let url = '../topic/topic?mainType=0&title=' + that.info.name + '&code=' + that.info.code + '&id=' +
				that.info.id;
			uni.navigateTo({
				url: url
			});
		},
		async onOrderPractice() {
			const countInfo = await get('question/getCount', {
				course_id: that.info.id
			});
			const countInfoData = countInfo.data;
			console.log(countInfoData.count);
			if (countInfoData.count <= 0) {
				app.showToast('题库未上传题目');
				return;
			}
			let url =
				'../practice?title=' +
				that.info.name +
				'&mainType=0' +
				'&id=' +
				that.info.id +
				'&max_page=' + countInfoData.max_page +
				'&question_count=' + countInfoData.count;
			uni.navigateTo({
				url: url
			});
		},
		onCategory(cate) {
			let url = '';
			if (cate.is_multi_level) {
				// 多级分类则进入章节题目
				url = '/pages/practice/paper/category?title=' + cate.name + '&category_id=' + cate.id + '&course_id=' +
					that.info.id;
			} else {
				// 单层级直接进入试卷中心
				url = '/pages/practice/paper/paper?title=' + cate.name + '&category_id=' + cate.id;
			}
			uni.navigateTo({
				url: url
			});
		},
		onCollect() {
			app.globalData.server
				.postRequest('v2/joinCourses', {
					course_id: that.info.id,
					join: that.info.collect != 1 ? 0 : 1
				})
				.then(function (e) {
					that.info.collect = that.info.collect == 1 ? 2 : 1;
				})
				.catch(function (e) {
					app.showToast('收藏失败');
				});
		},
		onSearchQuestion() {
			let url = '/pages/search/list?selectModelIndex=1&course_id=' + that.info.id;
			console.log(url);
			uni.navigateTo({
				url: url
			});
		},
		onErrorQuestion() {
			let url = '/pages/practice/error/error?course_id=' + that.info.id;
			uni.navigateTo({
				url: url
			});
		},
		onBuyCourse() {
			let url = '/pages/practice/course/buy?id=' + that.info.id;
			uni.navigateTo({
				url: url
			});
		},
		onColl() {
			let url = '/pages/practice/coll/coll?course_id=' + that.info.id;
			uni.navigateTo({
				url: url
			});
		},
		onNote() {
			let url = '/pages/practice/material/list?title=' + that.info.name + '&id=' + that.info.id;
			uni.navigateTo({
				url: url
			});
		},
		showOnlineServiceTap() {
			that.showOnlineServiceNotice = true;
		},

		activateVIP() {
			if (!that.activateCode) {
				app.showToast('请输入激活码');
				return;
			}

			// 这里添加激活码验证逻辑
			app.globalData.server
				.postRequest('v2/activateCourse', {
					course_id: that.info.id,
					activate_code: that.activateCode
				})
				.then(function (res) {
					app.showToast('激活成功');
					that.showActivateModal = false;
					that.activateCode = '';
					// 刷新课程信息
					that.getInfo(that.info.id);
				})
				.catch(function (err) {
					app.showToast(err.message || '激活失败');
				});
		},
		// 激活成功回调
		onActivateSuccess(data) {
			// 刷新课程信息
			that.getInfo(that.info.id);
		},
		onOnlineCourse() {
			// 如果有多个视频合集，可以跳转到列表页
			if (that.info.videoCollectionId && that.info.videoCollectionId.length > 0) {
				// 如果只有一个视频合集，直接跳转到详情页
				if (that.info.videoCollectionId.length === 1) {
					let url = '/pages/practice/vip/detail?id=' + that.info.videoCollectionId[0];
					uni.navigateTo({
						url: url
					});
				} else {
					// 如果有多个视频合集，跳转到列表页
					let url = '/pages/practice/vip/vip?course_id=' + that.info.id;
					uni.navigateTo({
						url: url
					});
				}
			}
		}
	}
};