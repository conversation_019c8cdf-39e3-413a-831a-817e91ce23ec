<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" :title="title"></back>
		<view class="cu-list  menu" style="margin-top: 5rpx;" v-for="(item, index) in cateList" :key="index">
			<view class="cu-item" @tap="chapterTap(index)" :data-index="index">
				<view class="content padding-tb-sm">
					<view><text class="text-blue" style="margin-right: 12rpx;"
							:class="'cuIcon-' + (item['isOpen'] ? 'roundrightfill' : 'rounddown')"></text>{{ item['name'] }}
						<text v-if="item.is_need_vip == 1" class="cuIcon-vip text-orange" style="margin-left: 10rpx; font-size: 36rpx;"></text>
					</view>
				</view>
			</view>
			<view :class="'cu-item ' + (item['isOpen'] ? '' : 'piano-hidden-content')"
				v-for="(child, childIndex) in item.childs" :key="childIndex" @tap="sectionTap(child)">
				<view class="content" style="padding: 20rpx 12rpx 20rpx 0rpx;">
					<view>
						<text style="margin-left: 40rpx;font-size: 28rpx;"> {{ child.name }}</text>
						<text v-if="child.is_need_vip == 1" class="cuIcon-vip text-orange" style="margin-left: 10rpx; font-size: 32rpx;"></text>
					</view>
					<view class="text-gray text-sm" style="margin-top: 8rpx; margin-left: 40rpx; font-size: 24rpx;">
						<text class="cuIcon-roundcheckfill text-blue" style="margin-right: 6rpx;"></text>已答题{{child.answered_count || 0}}/{{child.question_count || 0}}
					</view>
				</view>
				<view class="action">
					<view class="rate-container" style="margin-right: 10rpx;">
						<view class="text-blue correct-rate" style="font-size: 30rpx; margin-bottom: 12rpx;">{{child.correct_rate || 0}}%</view>
						<view class="text-grey rate-label" style="font-size: 22rpx;">正确率</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<style src="./category.css"></style>
<script src="./category.js"></script>