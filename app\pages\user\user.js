import responsive from '@/common/js/responsive.js';
let that = null;
let app = getApp();
export default {
	data() {
		return {
			load: false,
			user: [],
			appPlatform: app.globalData.appPlatform,
			paths: {
			},
			// 响应式样式数据
			cssData: {
				userInfo: {},
				menuItem: {},
				textContent: {}
			},
			menuItems: {
				"info": "/pages/user/info/info",
				"about": "/pages/user/about/about",
				"switch": "/pages/user/switch/switch",
				"promote": "/pages/user/promote/promote",
				"ranking": "/pages/user/ranking/ranking",
				"customer": "/pages/user/customer/customer",
				"score": "/pages/user/sign/sign",
				"rechargeMember": "/pages/user/vip/vip",
				"wechat": "/pages/user/login/bindopenid?from=2",
				"email": "/pages/user/login/bindemail",
				"mobile": "/pages/user/info/edit",
				"opinion": "/pages/user/opinion/opinion",
				"password": "/pages/user/set/password",
				"myCourse": "/pages/user/course/course",
				"myCollect": "/pages/practice/coll/coll",
				"myErrorQuestion": "/pages/practice/error/error"
			},
			appIsAudit: false,
			isIosVirtualPay: true,
			showOnlineServiceNotice: false
		};
	},
	onLoad() {
		that = this;
		app.globalData.showShareMenu();
	},
	onShow() {
		that.getUserInfo();
		that.initCss();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		/**
		 * 初始化响应式样式
		 */
		initCss() {
			// 用户信息区域样式
			this.cssData.userInfo = responsive.getResponsiveStyles({
				fontSize: 32,
				padding: '30rpx',
				height: 240
			});
			
			// 菜单项样式
			this.cssData.menuItem = responsive.getResponsiveStyles({
				fontSize: 26,
				padding: '20rpx',
				height: 140
			});
			
			// 文本内容样式
			this.cssData.textContent = responsive.getResponsiveStyles({
				fontSize: 28
			});
		},
		
		getUserInfo() {
			app.globalData.server
				.getRequest('user/info', {})
				.then((e) => {
					app.globalData.config.storage.setUserInfoData(e.data);
					this.setData({
						user: e.data,
						appIsAudit: app.globalData.checkAppIsAudit(),		
						load: true
					});
					that.isIosVirtualPay = app.globalData.checkIsIosVirtualPay();
				})
				.catch((e) => {
					console.log(e);
				});
		},
		copyTap(data) {
			console.log(data);
			uni.setClipboardData({
				data: data.toString(),
				success() {
					app.showToast('复制成功');
				},
				fail(res) {
					console.log(res);
				}
			});
		},
		menuTap(options) {
			let url = options.currentTarget.dataset.url;
			console.log(url)
			uni.navigateTo({
				url: url
			});
		},
		showOnlineServiceTap() {
			that.showOnlineServiceNotice = true;
		},
	}
};