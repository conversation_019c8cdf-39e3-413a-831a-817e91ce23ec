<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="课程详情"></back>
		<view v-if="isLoad" class="flex flex-direction">

			<!-- 视频封面区域 -->
			<view class="video-cover" v-if="!showVideoPlayer" @tap="info.can_play_online == 1 ? playFirstVideo() : void 0">
				<image :src="info.cover" mode="aspectFill" lazy-load></image>
				<view class="video-duration" v-if="videoList.length > 0">
					<text>共{{ videoList.length }}课时</text>
				</view>
				<view class="video-tag" v-if="info.is_hot">
					<text>HOT</text>
				</view>
				<view class="play-icon" v-if="info.can_play_online == 1">
					<text class="cuIcon-playfill"></text>
				</view>
			</view>

			<!-- 视频播放区域 -->
			<view class="video-container" v-if="showVideoPlayer">
				<video :src="currentVideoUrl" controls autoplay class="video-player"></video>
			</view>

			<!-- 课程信息区域 -->
			<view class="course-info bg-white padding">
				<view class="course-title text-bold">{{ info.name }}</view>
				<view class="course-desc text-gray">{{ info.description }}</view>
				<view class="flex justify-between align-center margin-top-sm">
					<view class="">
						<text class="text-red text-xxl text-bold">¥{{ info.price }}</text>
						<text class="text-gray text-del margin-left-xs">¥{{ info.market_price }}</text>
					</view>
					<view class="text-blue" v-if="info.sale_num > 0"><text class="cuIcon-peoplefill margin-right-xs"></text>{{ info.sale_num }}人购买
					</view>
				</view>
			</view>

			<!-- 分隔线 -->
			<view class="divider-line"></view>

			<!-- Tab切换区域 -->
			<view class="cu-bar bg-white">
				<view class="action" style="width: 100%;">
					<view class="tabs">
						<view :class="['tab-item', currentTab == index ? 'text-blue cur' : '']"
							v-for="(item, index) in tabs" :key="index" @tap="tabSelect" :data-id="index"
							v-if="!(item === '目录' && videoList.length <= 0)">
							{{ item }}
						</view>
					</view>
				</view>
			</view>

			<!-- Tab内容区域 -->
			<view class="tab-content">
				<!-- 详情 -->
				<view v-if="currentTab == 0" class="padding bg-white">
					<rich-text :nodes="info.content"></rich-text>
				</view>

				<!-- 目录 -->
				<view v-if="currentTab == 1 && videoList.length > 0" class="bg-white video-list-container">
					<scroll-view scroll-y style="max-height: 60vh;">
						<view v-for="(item, index) in videoList" :key="index" class="cu-item video-item solid-bottom"
							@tap="selectVideo" :data-id="index"
							:class="currentVideoIndex === index ? 'active-video' : ''">
							<view class="flex align-center justify-between">
								<view class="flex align-center" style="flex: 1;">
									<view class="video-index">{{ index + 1 }}</view>
									<view class="flex-sub margin-left-sm" style="max-width: 80%;">
										<view class="text-black text-df" style="display: flex; flex-direction: column;">
											<text class="text-cut"
												style="max-width: 390rpx; display: inline-block;">{{ item.name }}</text>
											<view class="margin-top-xs" style="padding-left: 0;">
												<view v-if="item.is_free == 1" class="free-tag">

													<text class="cu-tag bg-blue light radius sm"
														style="vertical-align: middle;">免费体验</text>
													<text class="cuIcon-hotfill text-orange"
														style="font-size: 32rpx; margin-right: 4rpx; vertical-align: middle;"></text>
												</view>
												<view v-if="item.is_free == 0" class="free-tag">
													<text class="cu-tag bg-blue light radius sm"
														style="vertical-align: middle;">付费解锁</text>
													<text class=" text-orange"
														:class="!info.isPay ? 'cuIcon-lock' : 'cuIcon-unlock'"
														style="font-size: 32rpx; margin-right: 4rpx; vertical-align: middle;"></text>

												</view>
											</view>
										</view>
									</view>
								</view>
								<view class="cuIcon-playfill text-blue" style="font-size: 40rpx; margin-right: 10rpx;"
									v-if="currentVideoIndex === index"></view>
								<view class="cuIcon-play text-gray" style="font-size: 40rpx; margin-right: 10rpx;"
									v-else></view>
							</view>
						</view>
						<view v-if="videoList.length == 0" class="empty-state-container">
							<view class="empty-icon">
								<text class="cuIcon-video text-gray"></text>
							</view>
							<view class="empty-text">
								<text class="title">暂无视频内容</text>
							</view>
						</view>

					</scroll-view>
				</view>

				<!-- 课件 -->
				<view v-if="(videoList.length > 0 && currentTab == 2) || (videoList.length <= 0 && currentTab == 1)" class="bg-white">
					<view v-for="(item, index) in materialList" :key="index"
						class="cu-item material-item solid-bottom padding" @tap="downloadMaterial" :data-id="index">
						<view class="flex align-center justify-between">
							<view class="flex align-center">
								<view class="cuIcon-file text-blue margin-right"></view>
								<view class="text-black">{{ item.name }}</view>
							</view>
							<view class="cuIcon-down text-blue"></view>
						</view>
						<view class="text-gray text-sm margin-top-xs">{{ item.size }} mb| {{ item.type }}</view>
					</view>
					<view v-if="materialList.length == 0" class="empty-state-container">
						<view class="empty-icon">
							<text class="cuIcon-file text-gray"></text>
						</view>
						<view class="empty-text">
							<text class="title">暂无课件内容</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部按钮区域 -->
			<view class="bottom-layout cu-bar bg-white tabbar border shop">
				<button class="action text-blue" open-type="share">
					<view class="cuIcon-forwardfill"></view>
					<text class="text-sm text-blue">分享</text>
				</button>
				<button class="action text-blue" open-type="contact">
					<view class="cuIcon-servicefill"></view>
					<text class="text-sm text-blue">客服</text>
				</button>
				<!-- 根据购买状态显示不同按钮 -->
				<view v-if="!info.isPay" class="bg-red flex-twice radius" @tap="buyCourse">
					<view class="padding-sm text-center">
						<text class="text-white">立即购买</text>
					</view>
				</view>
				<view v-else class="bg-blue flex-twice radius" @tap="startLearning">
					<view class="padding-sm text-center">
						<text class="text-white">{{ info.can_play_online==0 ? '立即学习' : '立即学习' }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 客服二维码弹窗 -->
		<view class="qrcode-modal" v-if="showServiceQrcode">
			<view class="qrcode-container">
				<view class="qrcode-header">
					<text class="qrcode-title">课程观看指引</text>
					<text class="cuIcon-close" @tap="closeQrcodeModal"></text>
				</view>
				<view class="qrcode-content">
					<view class="qrcode-tips">
						<text class="cuIcon-info text-blue"></text>
						<text class="tips-text" v-if="page_config.customerServiceGuideTip">{{ page_config.customerServiceGuideTip }}</text>
					</view>
					<image :src="serviceQrcode" mode="aspectFit" class="qrcode-image" show-menu-by-longpress></image>
					<view class="qrcode-instruction">长按二维码添加客服</view>
				</view>
				<view class="qrcode-footer">
					<button class="cu-btn bg-blue" @tap="closeQrcodeModal">我知道了</button>
				</view>
			</view>
		</view>
	</view>
</template>

<style src="./detail.css"></style>
<script src="./detail.js"></script>