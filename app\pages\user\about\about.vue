<template>
	<view v-if="load">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="关于我们"></back>
		<view class="flex flex-direction" style="min-height: 100vh;">
			<view class="flex-sub">
				<view v-for="(item, index) in list" :key="index">
					<view class="cu-bar bg-white text-lg">
						<view class="action text-black">
							<text class=" text-blue" :class="item.icon"></text>
							{{item.title}}
						</view>
					</view>
					<view class="solid bg-white   flex align-center">
						<view class="flex-sub text-center text-left">
							<view class="padding">
								<rich-text :nodes="item.content"></rich-text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<adfootbanner></adfootbanner>
		</view>
	</view>
</template>
<style src="./about.css"></style>
<script src="./about.js"></script>