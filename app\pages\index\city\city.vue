<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" :showTitle="true" title="选择省份"></back>
		<view v-if="isLoad">
			<view class="cu-bar bg-white search fixed" :style="'top:' + CustomBar + 'px;'">
				<view class="search-form round">
					<text class="cuIcon-search"></text>
					<input type="text" placeholder="输入省名称" @input="handleSearchInput" @confirm="handleSearchCity"
						confirm-type="search" />
				</view>
				<view class="action">
					<button @tap="handleSearchCity" class="cu-btn bg-gradual-blue shadow-blur round">搜索</button>
				</view>
			</view>
			<scroll-view scroll-y class="indexes" :scroll-into-view="'indexes-' + listCurID"
				:style="'height:calc(100vh - ' + CustomBar + 'px - 50px)'" :scroll-with-animation="true"
				:enable-back-to-top="true">
				<block v-for="(item, index) in listData" :key="index">
					<view :class="'padding indexItem-' + index" :id="'indexes-' + index" :data-index="index">
						{{ index }}
					</view>

					<view class="cu-list menu-avatar no-padding">
						<view class="cu-item" @tap="handleCitySelect" :data-data="item" v-for="(item, sub) in listData[index]"
							:key="item.index2">
							<view class="cu-avatar round lg bg-blue">{{ listData[index][sub]['initial'] }}</view>

							<view class="content">
								<view class="">
									<text class="text-abc">{{ listData[index][sub]['name'] }}</text>
								</view>
							</view>
						</view>
					</view>
				</block>
				<adbanner></adbanner>
			</scroll-view>
			<view class="indexBar" :style="'height:calc(100vh - ' + CustomBar + 'px - 50px)'">
				<view class="indexBar-box" @touchstart="handleTouchStart" @touchend="handleTouchEnd" @touchmove.stop.prevent="handleTouchMove">
					<view class="indexBar-item" :id="index" @touchstart="handleGetCurrent" @touchend="handleSetCurrent"
						v-for="(item, index) in listData" :key="index">
						{{ index }}
					</view>
				</view>
			</view>
			<!-- 选择显示 -->
			<view v-if="!isHidden" class="indexToast">
				{{ listCur }}
			</view>
		</view>
	</view>
</template>
<style src="./city.css"></style>
<script src="./city.js"></script>