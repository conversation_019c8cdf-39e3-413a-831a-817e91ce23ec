<view><back vue-id="4463f094-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="在线网课" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view class="flex flex-direction" style="min-height:100vh;"><view class="cu-bar bg-white search"><view class="search-form round"><text class="cuIcon-search"></text><input type="text" placeholder="搜索关键字" confirm-type="search" data-event-opts="{{[['input',[['__set_model',['','keyword','$event',[]]],['onInputChange',['$event']]]],['confirm',[['searchTap',['$event']]]]]}}" value="{{keyword}}" bindinput="__e" bindconfirm="__e"/></view><view data-event-opts="{{[['tap',[['searchTap',['$event']]]]]}}" class="action" bindtap="__e"><text class="text-blue">搜索</text></view></view><view class="video-list padding-lr" style="margin-bottom:120rpx;"><block wx:for="{{videoList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="video-card" data-index="{{index}}" data-event-opts="{{[['tap',[['videoTap',['$event']]]]]}}" bindtap="__e"><view class="video-cover"><image src="{{item.cover}}" mode="aspectFill" lazy-load="{{true}}"></image><block wx:if="{{item.video_num}}"><view class="video-duration"><text>{{"共"+item.video_num+"课时"}}</text></view></block><block wx:if="{{item.is_hot}}"><view class="video-tag"><text>HOT</text></view></block><block wx:if="{{item.can_play_online==1}}"><view class="play-icon"><text class="cuIcon-playfill"></text></view></block></view><view class="video-info"><view class="video-title text-cut-2">{{item.name}}</view><view class="video-desc text-cut">{{item.description||'暂无描述'}}</view><view class="video-meta"><view class="video-price-container"><view class="{{['video-price','text-red','text-bold',(item.price===0)?'price-free':'']}}">{{''+(item.price===0?'免费':'¥'+item.price)+''}}</view><block wx:if="{{item.market_price>0}}"><view class="video-original-price text-gray text-del margin-left-xs">{{'¥'+item.market_price+''}}</view></block></view><block wx:if="{{item.sale_num>0}}"><view class="video-views text-blue"><text class="cuIcon-peoplefill text-blue"></text><text class="text-blue">{{item.sale_num+"人购买"}}</text></view></block></view></view></view></block><block wx:if="{{$root.g0}}"><empty vue-id="4463f094-2" bind:__l="__l"></empty></block></view><view class="safe-area-bottom"></view></view></block></view>