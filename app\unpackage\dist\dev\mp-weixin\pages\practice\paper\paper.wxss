.paper-name {
    color: #333;
    font-size: 30rpx;
}
.text-right {
    text-align: right;
}
.cu-item .action {
    min-width: 120rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.cu-item .content {
    width: calc(100% - 180rpx);
}
.correct-rate {
    font-size: 24rpx;
    font-weight: 500;
    margin-bottom: 20rpx;
}
.rate-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.5;
}
.rate-label {
    font-size: 24rpx;
    display: flex;
    align-items: center;
}

/* 新增页面配置相关样式 */
.padding-sm {
    padding: 20rpx;
}
.padding-bottom-sm {
    padding-bottom: 10rpx;
}
.text-xl {
    font-size: 36rpx;
    font-weight: bold;
}
.text-gray {
    color: #8799a3;
}
.text-center {
    text-align: center;
}
