{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/user/ranking/ranking.vue?3dd3", "webpack:///D:/桌面/thinker/app/pages/user/ranking/ranking.vue?7509", "webpack:///D:/桌面/thinker/app/pages/user/ranking/ranking.vue?4576", "webpack:///D:/桌面/thinker/app/pages/user/ranking/ranking.vue?9a73", "uni-app:///pages/user/ranking/ranking.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "adbanner", "data", "isLoad", "listData", "first", "date", "avg", "last", "onLoad", "app", "onShareAppMessage", "methods", "getUserRanking", "getRequest", "then", "that", "catch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,ypBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACiG9rB;AACA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAEAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACAH,sBACAI,+BACAC;QACAC;QACAA;MACA,GACAC;IACA;EACA;AACA;AAAA,2B", "file": "pages/user/ranking/ranking.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/ranking/ranking.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ranking.vue?vue&type=template&id=5b463edc&\"\nvar renderjs\nimport script from \"./ranking.vue?vue&type=script&lang=js&\"\nexport * from \"./ranking.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ranking.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/ranking/ranking.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ranking.vue?vue&type=template&id=5b463edc&\"", "var components\ntry {\n  components = {\n    adbanner: function () {\n      return import(\n        /* webpackChunkName: \"components/adbanner/adbanner\" */ \"@/components/adbanner/adbanner.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ranking.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ranking.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"isLoad\">\r\n\t\t<view class=\"contaier\" style=\"background-color: #ffffff\">\r\n\t\t\t<view class=\"top_bg\">\r\n\t\t\t\t<view class=\"one_box\">\r\n\t\t\t\t\t<view class=\"top3\">\r\n\t\t\t\t\t\t<view class=\"num_two\">\r\n\t\t\t\t\t\t\t<image class=\"huangguan2\" src=\"https://learnfile.20230611.cn/app_img/two.png\"></image>\r\n\t\t\t\t\t\t\t<image class=\"top3_head\" :src=\"listData.first[1]['avatar']\"></image>\r\n\t\t\t\t\t\t\t<view class=\"top_name\">{{ listData.first[1]['nickname'] }}</view>\r\n\t\t\t\t\t\t\t<view class=\"top_sy\">\r\n\t\t\t\t\t\t\t\t{{ listData.first[1]['region_name'] }} . {{ listData.first[1]['count'] }}\r\n\t\t\t\t\t\t\t\t<label class=\"_span\">题</label>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"top3\">\r\n\t\t\t\t\t\t<view class=\"num_one\">\r\n\t\t\t\t\t\t\t<image class=\"huangguan1\" src=\"https://learnfile.20230611.cn/app_img/one.png\"></image>\r\n\t\t\t\t\t\t\t<image class=\"top3_head\" :src=\"listData.first[0]['avatar']\"></image>\r\n\t\t\t\t\t\t\t<view class=\"top_name text-bold\" style=\"font-size: 30rpx\">\r\n\t\t\t\t\t\t\t\t{{ listData.first[0]['nickname'] }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"top_sy\">\r\n\t\t\t\t\t\t\t\t{{ listData.first[0]['region_name'] }} . {{ listData.first[0]['count'] }}\r\n\t\t\t\t\t\t\t\t<label class=\"_span\">题</label>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"top3\">\r\n\t\t\t\t\t\t<view class=\"num_three\">\r\n\t\t\t\t\t\t\t<image class=\"huangguan2\" src=\"https://learnfile.20230611.cn/app_img/three.png\"></image>\r\n\t\t\t\t\t\t\t<image class=\"top3_head\" :src=\"listData.first[2]['avatar']\"></image>\r\n\t\t\t\t\t\t\t<view class=\"top_name\">{{ listData.first[2]['nickname'] }}</view>\r\n\t\t\t\t\t\t\t<view class=\"top_sy\">\r\n\t\t\t\t\t\t\t\t{{ listData.first[2]['region_name'] }} . {{ listData.first[2]['count'] }}\r\n\t\t\t\t\t\t\t\t<label class=\"_span\">题</label>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"number_sy_box\">\r\n\t\t\t\t\t<view class=\"number_sy_box_title\">\r\n\t\t\t\t\t\t<text>答题·统计</text>\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tstyle=\"position: absolute; right: 20rpx; z-index: 9999; font-size: 24rpx; color: #c3c3c3\">机器人实时计算</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"number_sy_main\">\r\n\t\t\t\t\t\t<view style=\"width: 50%; text-align: center; border-right: 1px solid #eee\">\r\n\t\t\t\t\t\t\t<view class=\"number_num1\">{{ listData.avg }}道</view>\r\n\r\n\t\t\t\t\t\t\t<view class=\"danwei\">平均答题</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"width: 50%; text-align: center; z-index: 9999\">\r\n\t\t\t\t\t\t\t<view class=\"number_num2\">{{ listData.rate}}%</view>\r\n\t\t\t\t\t\t\t<view class=\"danwei\">正确率</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<image class=\"xiaoding_bg\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tsrc=\"https://learnfile.20230611.cn/app_img/Intersect.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"rankList_box\">\r\n\t\t\t\t<view class=\"rankItem\" v-for=\"(item, index) in listData.last\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"rankIndex\">\r\n\t\t\t\t\t\t<text>{{ index + 4 }}</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"HeardBox\">\r\n\t\t\t\t\t\t<image class=\"rankHeard\" :src=\"item.avatar\"></image>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"NameBox\">\r\n\t\t\t\t\t\t<view class=\"userPost text-bold\">\r\n\t\t\t\t\t\t\t{{ item.nickname }}\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"color_ccc\">\r\n\t\t\t\t\t\t\t<view class=\"cu-capsule round\">\r\n\t\t\t\t\t\t\t\t<view class='cu-tag bg-blue'>\r\n\t\t\t\t\t\t\t\t\t{{ item.region_name }}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cu-tag line-blue\">\r\n\t\t\t\t\t\t\t\t\t{{ item.count }}题\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<adbanner unitId=\"adunit-e9f553c403a978f6\"></adbanner>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport adbanner from '@/components/adbanner/adbanner';\r\n\tlet app = getApp();\r\n\tlet that = null;\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tadbanner\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoad: false,\r\n\r\n\t\t\t\tlistData: {\r\n\t\t\t\t\tfirst: '',\r\n\t\t\t\t\tdate: '',\r\n\t\t\t\t\tavg: '',\r\n\t\t\t\t\tlast: []\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(t) {\r\n\t\t\t(that = this).getUserRanking();\r\n\t\t\tapp.globalData.showShareMenu();\r\n\t\t},\r\n\t\tonShareAppMessage: function() {\r\n\t\t\treturn app.globalData.getShareConfig();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetUserRanking: function() {\r\n\t\t\t\tapp.globalData.server\r\n\t\t\t\t\t.getRequest('user/ranking', {})\r\n\t\t\t\t\t.then(function(t) {\r\n\t\t\t\t\t\tthat.isLoad = true;\r\n\t\t\t\t\t\tthat.listData = t.data;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(function(a) {});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style src=\"./ranking.css\"></style>"], "sourceRoot": ""}