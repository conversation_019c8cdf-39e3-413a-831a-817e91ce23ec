/**
 * 练习相关的常量和工具函数
 */

// 选项列表
export const options = ['A', 'B', 'C', 'D', 'E', 'F'];

// 主题类型
export const mainType = {
    course: 1,  // 课程
    year: 2,    // 年份
    vip: 4,     // VIP
    chapter: 5, // 章节
    error: 6,   // 错题
    coll: 7,    // 收藏
    paper: 8    // 试卷
};

// 题目类型
export const topicType = {
    single: 1,  // 单选题
    multi: 2,   // 多选题
    judge: 3,   // 判断题
    answer: 4,  // 问答题
    fill: 5,    // 填空题
    cloze: 6,   // 完形填空
    read: 7,    // 阅读理解
    noun: 8,    // 名词解释
    high: 100,  // 高级题目
    chapter: 101 // 章节题目
};

/**
 * 获取题目类型的标签
 * @param {Number} type 题目类型
 * @returns {String} 题目类型的标签
 */
export function getTopicTypeLabel(type) {
    switch (type) {
        case 1: return '单选题';
        case 2: return '多选题';
        case 3: return '判断题';
        case 4: return '问答题';
        case 5: return '填空题';
        case 6: return '完形填空';
        case 7: return '阅读理解';
        case 8: return '名词解释';
        case 10: return '简答题';
        case 12: return '论述题';
        default: return '未知题型';
    }
}

/**
 * 获取题目类型的图标
 * @param {Number} type 题目类型
 * @returns {String} 题目类型的图标路径
 */
export function getTopicTypeIcon(type) {
    switch (type) {
        case 1: return '/static/img/ic_topic_1.png';
        case 2: return '/static/img/ic_topic_2.png';
        case 3: return '/static/img/ic_topic_3.png';
        case 4: return '/static/img/ic_topic_4.png';
        case 5: return '/static/img/ic_topic_5.png';
        case 8: return '/static/img/ic_topic_8.png';
        default: return '/static/img/ic_topic_1.png';
    }
}

/**
 * 检查题目类型是否支持作答
 * @param {Number} type 题目类型
 * @returns {Boolean} 是否支持作答
 */
export function checkSupportAnswer(type) {
    return ![4, 5, 6, 7, 8, 10, 12].includes(type);
}

/**
 * 构建答案选项
 * @param {String} answer 答案字符串
 * @returns {Array|String} 答案选项数组或字符串
 */
export function buildAnswerOption(answer) {
    if (answer.length == 1) {
        return answer;
    }
    return answer.split('');
}