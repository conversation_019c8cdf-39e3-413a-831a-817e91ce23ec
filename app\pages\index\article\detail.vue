<template>
	<view v-if="isLoad" class="article-container">
		<back :showBackText="false"  customClass="bg-gradual-blue text-white" :showBack="true" :showTitle="false"></back>
		<view class="article-wrapper">
			<view class="article-content">
				<!-- 文章标题 -->
				<view class="article-header">
					<text class="article-title">{{info.title}}</text>
					
					<!-- 文章元信息 -->
					<view class="article-meta">
						<view class="meta-item">
							<text class="cuIcon-time"></text>
							<text>{{info.create_time | formatDate}}</text>
						</view>
						<view class="meta-item">
							<text class="cuIcon-people"></text>
							<text>{{info.author || 'admin'}}</text>
						</view>
						<view class="meta-item">
							<text class="cuIcon-attention"></text>
							<text>{{info.page_view || 0}}阅读</text>
						</view>
					</view>
				</view>
				
				<!-- 文章摘要 -->
				<view class="article-summary" v-if="info.summary">
					<text>{{info.summary}}</text>
				</view>
				
				<!-- 文章内容 -->
				<view class="article-body">
					<rich-text :nodes="info.content"></rich-text>
				</view>
				
				<!-- 文章标签 -->
				<view class="article-tags" v-if="info.tags && info.tags.length">
					<text class="tag-title">标签：</text>
					<view class="tag-item" v-for="(tag, index) in info.tags" :key="index">
						{{tag}}
					</view>
				</view>
				
				<!-- 分享按钮 -->
				<view class="article-actions">
					<button class="action-btn share-btn" open-type="share">
						<text class="cuIcon-share"></text>
						<text>分享好文</text>
					</button>
				</view>
			</view>
		</view>
		<adfootbanner></adfootbanner>
	</view>
</template>
<style src="./detail.css"></style>
<script src="./detail.js"></script>