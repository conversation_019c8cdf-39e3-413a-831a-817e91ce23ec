/* 视频列表 */
.video-list {
	padding-top: 20rpx;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 视频卡片 */
.video-card {
	background-color: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.video-card .video-cover {
	position: relative;
	width: 100%;
	height: 360rpx;
}

.video-card .video-cover image {
	width: 100%;
	height: 100%;
}

.video-card .video-cover .video-duration {
	position: absolute;
	bottom: 16rpx;
	right: 16rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
}

.video-card .video-cover .video-tag {
	position: absolute;
	top: 16rpx;
	left: 16rpx;
	background-color: #FF6A00;
	color: #fff;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
}

.video-card .video-cover .play-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
}

.video-card .video-cover .play-icon .cuIcon-playfill {
	font-size: 50rpx;
	color: #fff;
	margin-left: 8rpx;
	/* 稍微偏右一点，视觉上更居中 */
}

.video-card .video-info {
	padding: 20rpx;
}

.video-card .video-info .video-title {
	font-size: 32rpx;
	font-weight: bold;
	line-height: 1.4;
	margin-bottom: 10rpx;
}

.video-card .video-info .video-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 16rpx;
}

.video-card .video-info .video-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.video-card .video-info .video-meta .video-price-container {
	display: flex;
	align-items: center;
}

.video-card .video-info .video-meta .video-price {
	font-size: 30rpx;
	color: #FF0000;
	font-weight: bold;
}

.video-card .video-info .video-meta .video-price.price-free {
	color: #1890FF;
}

.video-card .video-info .video-meta .video-original-price {
	font-size: 24rpx;
	color: #999;
	text-decoration: line-through;
}

.video-card .video-info .video-meta .video-views {
	font-size: 24rpx;
	color: #999;
	display: flex;
	align-items: center;
	gap: 6rpx;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.empty-state .empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f0f7ff;
	border-radius: 50%;
	box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.05);
}

.empty-state .empty-icon .cuIcon-videofill {
	font-size: 100rpx;
	color: #1890FF;
}

.empty-state text {
	font-size: 30rpx;
	color: #666;
	margin-top: 10rpx;
}

/* 底部安全区域 */
.safe-area-bottom {
	height: env(safe-area-inset-bottom);
}

/* 文本截断 */
.text-cut-2 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
} 