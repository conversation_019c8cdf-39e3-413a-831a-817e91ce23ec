<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="收银台"></back>
		<view v-if="isLoad" class="payment-container">
			<!-- 订单信息卡片 -->
			<view class="order-card">
				<view class="order-header">
					<text class="order-title">订单信息</text>
				</view>
				<view class="order-content">
					<view class="order-item">
						<text class="cuIcon-formfill text-gray"></text>
						<text class="item-label">订单编号：</text>
						<text class="item-value">{{info.orderInfo.order_sn}}</text>
					</view>
					<view class="order-item">
						<text class="cuIcon-infofill text-gray"></text>
						<text class="item-label">订单信息：</text>
						<text class="item-value">{{info.orderInfo.remark}}</text>
					</view>
					<view class="order-item price-item">
						<text class="cuIcon-moneybagfill text-orange"></text>
						<text class="item-label">支付金额：</text>
						<text class="price-value">¥ {{info.orderInfo.order_price}}</text>
					</view>
				</view>
			</view>
			
			<!-- 支付方式区域 -->
			<view class="payment-methods" v-if="!useScanPay">
				<view class="section-title">
					<text class="cuIcon-pay text-blue"></text>
					<text>选择支付方式</text>
				</view>
				<button @tap="confirmPayTap" class="payment-btn">
					<text class="cuIcon-wechat text-green margin-right-xs"></text>微信支付
				</button>
			</view>
			
			<!-- 扫码支付区域 -->
			<view class="scan-pay-container" v-if="useScanPay">
				<view class="section-title">
					<text class="cuIcon-scan text-blue"></text>
					<text>扫码支付</text>
				</view>
				<view class="qrcode-container" @tap="onClickScanPay">
					<image :src="scanPayUrl" mode="widthFix" class="qrcode-image"></image>
					<view class="qrcode-tip">
						<text class="cuIcon-info text-blue margin-right-xs"></text>
						<text>请使用微信扫一扫，扫描二维码完成支付</text>
					</view>
				</view>
				<view class="padding-sm" v-if="useCheckPay">
					<button @tap="paySuccessCheckTap" class="confirm-btn">
						<text class="cuIcon-check margin-right-xs"></text>我已完成支付
					</button>
				</view>
			</view>
			
			<!-- 支付说明 -->
			<view class="payment-tips">
				<text class="cuIcon-notificationfill text-gray"></text>
				<text class="tips-text">付款后，系统将自动为您处理订单</text>
			</view>
		</view>
	</view>
</template>
<style src="./pay.css"></style>
<script src="./pay.js"></script>