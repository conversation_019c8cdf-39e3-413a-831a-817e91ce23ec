<template>
	<view>
		<view class="cu-modal radius" :class="status==true ? 'show':''" @tap="setStatusTap(false)">
			<view class="cu-dialog"  @tap.stop="">
				<view class="cu-bar bg-white justify-end">
					<view class="content">{{title}}</view>
					<view class="action" @tap="setStatusTap(false)">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding-xl" style="word-break: break-all;">
					<rich-text class="explain-text"
						:nodes="content" v-if="content">
					</rich-text>
				</view>
				<view class="cu-bar bg-white" v-if="confirmText" >
					<button class="action  margin flex-sub text-green" :openType="confirmButtonOpenType"
						@tap="setStatusTap(false)">
						<text :class="confirmTextClass ? confirmTextClass:''"></text>{{confirmText}}
					</button>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {};
		},
		props: {
			title: {
				type: String,
				default: ''
			},
			content: {
				type: String,
				default: ''
			},
			status: {
				type: Boolean,
				default: false,
			},
			confirmText: {
				type: String,
				default: ''
			},
			confirmTextClass: {
				type: String,
				default: ''
			},
			confirmButtonOpenType: {
				type: String,
				default: ''
			},
		},
		methods: {
			setStatusTap(status) {
				this.$emit('update:status', status)
			},
		}
	}
</script>
