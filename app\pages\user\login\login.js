import {
	appPlatform as getAppPlatform
} from "@/common/js/config";
import {
	post
} from "@/common/js/http.js";
import {
	isSet
} from "@/common/js/util.js";
let app = getApp();
let that = null;
export default {
	data() {
		return {
			username: '',
			password: '',
			appPlatform: null,
			currentPagesLength: 0,
			checkAppIsAudit: true,
			isAgreement: false,
			selectModel: [{
				'name': '一键快捷登录',
				'icon': 'cuIcon-weixin',
				'show': true
			}, {
				'name': '账号密码登录',
				'icon': 'cuIcon-peoplefill',
				'show': true
			}],
			selectModelIndex: 0,
			login_page_set:null
		};
	},
	onLoad(options) {
		that = this;
		that.init();
	},
	methods: {
		async init() {
			//根据平台设置默认方式
			let appPlatform = that.appPlatform = await getAppPlatform();
			if (appPlatform === 10) {
				that.selectModelIndex = 1; //H5账号和密码登录
				that.selectModel[0].show = false;
			}
			if (appPlatform === 20 || appPlatform === 21) {
				that.selectModelIndex = 0; //微信小程序默认微信登录
			}
			if (appPlatform === 30) {
				that.selectModelIndex = 1; //APP默认账号密码登录
				that.selectModel[0].show = false;
			}
			if (appPlatform === 40 || appPlatform === 41) {
				that.selectModelIndex = 1; //百度小程序默认账号密码登录
				that.selectModel[0].show = false;
			}
			that.currentPagesLength = getCurrentPages().length;
			that.checkAppIsAudit = app.globalData.checkAppIsAudit();
			//读取页面配置
			let res = await post('appConfig/get', {config_type:'login_page_set'});
			that.login_page_set = res.data;
		},
		usernameInputTap(t) {
			that.username = t.detail.value;
		},
		passwordInputTap(t) {
			that.password = t.detail.value;
		},
		selectModelTap(options) {
			that.selectModelIndex = options.currentTarget.dataset.id;
		},
		loginTap() {
			try {
				that.checkForm();
				post('user/login', {
						username: that.username,
						password: that.password
					})
					.then((t) => {
						that.loginSuccess(t);
					})
					.catch((t) => {
						console.log(t.message);
						app.showToast(t.message);
					});
			} catch (error) {
				console.log(error);
				app.showToast(error.message);
			}
		},
		checkForm(t) {
			if (that.username == '') {
				throw new Error('登录账号不得为空');
			}
			if (that.password == '') {
				throw new Error('登录密码不得为空');
			}
			if (that.appPlatform == 30 && !that.isAgreement) {
				throw new Error('请先阅读协议并勾选');
			}
		},
		wxloginTap(options) {
			uni.login({
				success(res) {
					let data = {
						code: res.code,
					};
					post('user/wxLogin', data)
						.then((t)=> {
							if (t.code == 1) {
								that.loginSuccess(t);
							} else {
								app.showToast(t.message);
							}
						})
						.catch(function(t) {
							app.showToast('微信登录异常');
						});
				}
			});
		},
		wxPhoneloginTap(options) {
			if (!isSet(options.detail.code)) {
				return;
			}
			uni.login({
				success(res) {
					let data = {
						code: res.code,
						wx_phone_code: options.detail.code
					};
					post('user/wxLogin', data)
						.then((t) => {
							if (t.code == 1) {
								that.loginSuccess(t);
							} else {
								app.showToast(t.message);
							}
						})
						.catch((t) => {
							app.showToast('微信登录异常');
						});
				}
			});
		},
		loginSuccess(a) {
			app.globalData.config.storage.setAgentIdData(0);
			app.globalData.config.storage.setUserInfoData(a.data);
			app.globalData.config.storage.setUserTokenData(a.data.token);
			app.showToast('登录成功');
			app.globalData.checkLogin();
			that.navBackEvent();
		},
		navBackEvent() {
			if (1 == that.currentPagesLength) {
				uni.reLaunch({
					url: '../../index/city/city'
				});
			} else {
				uni.navigateBack();
			}
		},
		agreementTap(e) {
			that.isAgreement = e.detail.value
		}
	}
};