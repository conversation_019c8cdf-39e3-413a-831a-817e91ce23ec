
.back-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 90rpx;
  padding: 0 10rpx;
  position: relative;
}
.left-actions {
  display: flex;
  align-items: center;
  height: 100%;
}
.action-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 30rpx;
  padding: 8rpx 20rpx;
}
.left-action {
  display: flex;
  align-items: center;
  height: 100%;
}
.home-action {
  display: flex;
  align-items: center;
  height: 100%;
  margin-left: 0;
}
.divider-line {
  height: 28rpx;
  width: 2rpx;
  background-color: rgba(255, 255, 255, 0.7);
  margin: 0 15rpx;
}
.back-text {
  font-size: 28rpx;
  margin-left: 10rpx;
  color: #ffffff;
}
.cuIcon-back, .cuIcon-home {
  font-size: 36rpx;
  color: #ffffff;
}
.content {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 1;
  width: 60%;
  margin: 0 auto;
}
.content text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: block;
}

