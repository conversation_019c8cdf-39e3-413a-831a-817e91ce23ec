<block wx:if="{{load}}"><view><back vue-id="088798bc-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="切换版本" bind:__l="__l"></back><view class="cu-bar bg-white solid-bottom"><view class="action"><text class="cuIcon-titles text-orange"></text>版本列表</view></view><view class="cu-list menu"><view class="cu-item arrow"><view data-event-opts="{{[['tap',[['copyTap',[1]]]]]}}" class="content" bindtap="__e"><text class="cuIcon-discoverfill text-blue"></text><text class="text-blue">网页版刷题（复制网址）</text></view></view><view class="cu-item arrow"><view data-event-opts="{{[['tap',[['copyTap',[2]]]]]}}" class="content" bindtap="__e"><text class="cuIcon-discoverfill text-blue"></text><text class="text-blue">安卓APP版（下载地址）</text></view></view><view class="cu-item arrow"><view class="content"><text class="cuIcon-discoverfill text-gray"></text><text class="text-gray">苹果APP版（即将上线）</text></view></view></view><view class="{{['cu-modal',showNotice?'show':'']}}"><view class="cu-dialog"><view class="cu-bar bg-white justify-end"><view class="content">提示</view><view data-event-opts="{{[['tap',[['hideNoticeTap',['$event']]]]]}}" class="action" bindtap="__e"><text class="cuIcon-close text-red"></text></view></view><view class="padding-xl">{{''+showNoticeText+''}}</view><view class="cu-bar bg-white"><button data-event-opts="{{[['tap',[['hideNoticeTap',['$event']]]]]}}" class="action margin flex-sub text-green" bindtap="__e"><text class="cuIcon-check"></text>知道啦</button></view></view></view></view></block>