<template>
	<view class="search-page" :class="responsiveClass">
		<back :showBackText="false" :showBackLeft="false" customClass="bg-gradual-blue text-white" title="搜索"></back>

		<responsive-container>
			<view class="search-container">
				<!-- 搜索输入区域 -->
				<view class="search-input-section">
					<view class="cu-form-group search-form" :style="cssData.searchBar">
						<textarea @input="onInputChange"
							placeholder="输入搜索关键字，搜课程（可以按照编码搜索），搜题目（支持拍照搜，语音搜）"
							:value="keyword"
							class="search-textarea"></textarea>
					</view>
				</view>

				<!-- 操作按钮区域 -->
				<view class="action-buttons-section" v-if="!isAudioing && !appIsAudit">
					<view class="action-buttons-container" :class="getResponsiveValue({
						xs: 'grid-xs-2',
						sm: 'grid-sm-2',
						md: 'grid-md-4',
						lg: 'grid-lg-4',
						xl: 'grid-xl-4'
					})">
						<view class="action-group">
							<button :style="cssData.button" class="cu-btn action-button responsive-btn"
								@tap="audioTap">
								<text class="cuIcon-voicefill"></text>
								<text class="btn-text">语音</text>
							</button>
						</view>
						<view class="action-group">
							<button class="cu-btn action-button responsive-btn"
								@tap="chooseImageTap">
								<text class="cuIcon-camerafill"></text>
								<text class="btn-text">拍照</text>
							</button>
						</view>
						<view class="action-group">
							<button class="cu-btn action-button responsive-btn"
								@tap="clearAndPasteTap">
								<text class="cuIcon-copy"></text>
								<text class="btn-text d-xs-none d-sm-none">清除并粘贴</text>
								<text class="btn-text d-md-none d-lg-none d-xl-none">粘贴</text>
							</button>
						</view>
						<view class="action-group">
							<button class="cu-btn action-button responsive-btn"
								@tap="clearTap">
								<text class="cuIcon-deletefill"></text>
								<text class="btn-text">清除</text>
							</button>
						</view>
					</view>
				</view>

				<!-- 语音录制区域 -->
				<view class="voice-recording-section" v-if="isAudioing" @tap="audioActionTap(false)">
					<view class="voice-container">
						<view class="cu-progress radius striped active voice-progress">
							<view class="bg-green voice-progress-bar">录音中，点击停止</view>
						</view>
					</view>
				</view>

				<!-- 搜索按钮区域 -->
				<view class="search-button-section">
					<view class="search-button-container">
						<button :class="['cu-btn lg bg-gradual-blue shadow search-btn', cssData.button]"
							data-type="1" @tap="onSearchClick">
							<text class="cuIcon-search"></text>
							<text class="search-btn-text">立即搜索</text>
						</button>
					</view>
				</view>

				<!-- 搜索动态区域 -->
				<view class="search-dynamics-section">
					<view class="dynamics-header">
						<view class="dynamics-title">
							<text class="cuIcon-title text-red"></text>
							<text class="title-text">搜索动态</text>
						</view>
					</view>

					<responsive-grid :cols="getResponsiveValue({
						xs: 1,
						sm: 1,
						md: 2,
						lg: 2,
						xl: 3
					})" class="dynamics-grid">
						<view class="dynamics-item" v-for="(item, index) in recordList" :key="index"
							@tap="onClickQuestion(item.question_id)">
							<view class="dynamics-card">
								<view class="dynamics-header-info">
									<view class="dynamics-avatar">
										<view class="cu-avatar sm round" :style="'background-image:url(' + item.avatar + ');'"></view>
									</view>
									<view class="dynamics-meta">
										<text :class="['dynamics-time text-grey text-df', cssData.textContent]">{{ index + 3 }}秒前</text>
									</view>
								</view>
								<view class="dynamics-content">
									<text :class="['dynamics-nickname text-orange text-df', cssData.textContent]">{{ item.nickname }}</text>
									<text :class="['dynamics-action text-gray text-df', cssData.textContent]">搜索</text>
									<text :class="['dynamics-question text-blue text-df', cssData.textContent]">{{ item.question_name }}</text>
								</view>
							</view>
						</view>
					</responsive-grid>

					<adfootbanner></adfootbanner>
				</view>
			</view>
		</responsive-container>
	</view>
</template>
<style src="./search.css"></style>
<script src="./search.js"></script>