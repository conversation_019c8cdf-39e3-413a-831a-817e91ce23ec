<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="题目反馈"></back>
		<view>
			<text class="text-label">请选择在做题过程中遇到的问题</text>
			<view class="feedback-layout">
				<view @tap="feedbackTypeTap" class="layout-item" :data-feedbacktype="item"
					:style="'background: ' + (item == feedbackType ? '#6097FE' : '#F5F5F5') + ';'"
					v-for="(item, index) in feedbackTypes" :key="index">
					<text class="text-item"
						:style="'color: ' + (item == feedbackType ? 'white' : '#333')">{{ item }}</text>
				</view>
			</view>
			<text class="text-label">具体描述</text>
			<view class="layout-content">
				<textarea @input="bindContentInput" class="input-content" placeholder="选填，描述详细情况"></textarea>
			</view>
			<view @tap="commitTap" class="btn-submit">提交</view>
		</view>
	</view>
</template>

<script src="./feedback.js"></script>
<style src="./feedback.css"></style>