let that = null;
let app = getApp();
export default {
	data() {
		return {
			load: false,
			info: [],
			appIsAudit: false
		};
	},
	onLoad() {
		that = this;
	},
	onShow() {
		that.appIsAudit = app.globalData.checkAppIsAudit();
		that.getInfo();
	},
	methods: {
		getInfo: function() {
			let cityData = app.globalData.config.storage.getCurrentCityData();
			let examData = app.globalData.config.storage.getCurrentExamData();
			app.globalData.server
				.getRequest('wxGroup/get', {
					region_id: cityData.id,
					exam_id: examData.id,
				})
				.then(function(e) {
					that.setData({
						info: e.data,
						load: true
					});
				})
				.catch(function(e) {
					app.showToast('获取信息失败');
				});
		}
	}
};