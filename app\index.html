<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<title>
			<%= htmlWebpackPlugin.options.title %>
		</title>
		<!-- Open Graph data -->
		<!-- <meta property="og:title" content="Title Here" /> -->
		<!-- <meta property="og:url" content="http://www.example.com/" /> -->
		<!-- <meta property="og:image" content="http://example.com/image.jpg" /> -->
		<!-- <meta property="og:description" content="Description Here" /> -->
		<script>
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
			document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script>
		<link rel="icon" href="//file.20230611.cn/app_img/favicon.png">
		<!--iphone桌面图标-->
		<link rel="apple-touch-icon-precomposed" sizes="20x20" href="//file.20230611.cn/app_img/20x20.png">
		<link rel="apple-touch-icon-precomposed" sizes="29x29" href="//file.20230611.cn/app_img/29x29.png">			
		<link rel="apple-touch-icon-precomposed" sizes="40x40" href="//file.20230611.cn/app_img/40x40.png">
		<link rel="apple-touch-icon-precomposed" sizes="58x58" href="//file.20230611.cn/app_img/58x58.png">		
		<link rel="apple-touch-icon-precomposed" sizes="60x60" href="//file.20230611.cn/app_img/60x60.png">
		<link rel="apple-touch-icon-precomposed" sizes="76x76" href="//file.20230611.cn/app_img/76x76.png">		
		<link rel="apple-touch-icon-precomposed" sizes="80x80" href="//file.20230611.cn/app_img/80x80.png">
		<link rel="apple-touch-icon-precomposed" sizes="87x87" href="//file.20230611.cn/app_img/87x87.png">
		<link rel="apple-touch-icon-precomposed" sizes="120x120" href="//file.20230611.cn/app_img/120x120.png">
		<link rel="apple-touch-icon-precomposed" sizes="152x152" href="//file.20230611.cn/app_img/152x152.png">
		<link rel="apple-touch-icon-precomposed" sizes="152x152" href="//file.20230611.cn/app_img/167x167.png">
		<link rel="apple-touch-icon-precomposed" sizes="180x180" href="//file.20230611.cn/app_img/180x180.png">
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
	</head>
	<body>
		<noscript>
			<strong>Please enable JavaScript to continue.</strong>
		</noscript>
		<div id="app"></div>
		<!-- built files will be auto injected -->
	</body>
</html>
