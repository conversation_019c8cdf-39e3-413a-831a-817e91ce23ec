<template>
  <view class="material-detail-ios">
    <!-- 顶部大标题导航栏 -->
    <back :showBackText="false" customClass="bg-gradual-blue text-white" title="资料详情"></back>
    <scroll-view scroll-y class="main-content" v-if="isLoad">
      <!-- 资料主卡片 -->
      <view class="info-card shadow">
        <view class="info-header">
          <text class="info-title">{{ info.name }}</text>
          <view class="info-tags">
            <view class="cu-tag bg-green light price-tag">
              <text>{{ info.price }}</text>
              <text class="cuIcon-coin text-yellow" style="margin-left: 4rpx; font-size: 24rpx;"></text>
            </view>
          </view>
        </view>
        <view class="info-desc">{{ info.desc }}</view>
        <view class="info-meta">
          <view class="meta-item">
            <text class="cuIcon-hotfill text-red"></text>
            <text class="meta-label">兑换人数</text>
            <text class="meta-value">{{ info.hot }}</text>
          </view>
          <view class="meta-item" v-if="info.expire_text">
            <text class="cuIcon-timefill text-blue"></text>
            <text class="meta-label">有效期</text>
            <text class="meta-value">{{ info.expire_text }}</text>
          </view>
        </view>
      </view>

      <!-- 资料内容预览按钮（iOS风格圆角卡片按钮） -->
      <view class="preview-section" v-if="info.version==2">
        <button class="cu-btn block bg-blue radius preview-btn" @tap="openTap">
          <text class="cuIcon-read" style="margin-right: 8rpx;"></text> 查看内容
        </button>
      </view>
      <view class="preview-section" v-if="info.version==1">
        <button class="cu-btn block bg-blue radius preview-btn" @tap="readTap">
          <text class="cuIcon-read" style="margin-right: 8rpx;"></text> 阅读资料
        </button>
      </view>

      <!-- 资料介绍卡片 -->
      <view class="desc-card shadow">
        <view class="desc-title">资料介绍</view>
        <view class="desc-content">{{ info.desc }}</view>
      </view>

      <!-- 广告位 -->
      <view class="ad-section">
        <adbanner unitId="adunit-e9f553c403a978f6"></adbanner>
      </view>
    </scroll-view>

    <!-- 底部操作栏 - 只在isLoad为true时显示 -->
    <view class="action-bar safe-area-inset-bottom" v-if="isLoad">
      <button class="cu-btn bg-green radius action-btn" @tap="exchangeTap">积分兑换</button>
      <button class="cu-btn bg-orange radius action-btn" @tap="buyTap">直接购买</button>
    </view>

    <!-- 加载进度弹窗 -->
    <view class="cu-modal" :class="showProcessModal ? 'show' : ''">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">正在加载资料中</view>
        </view>
        <view class="padding bg-white">
          <view class="cu-progress">
            <view class="bg-blue" :style="[{ width: processRate }]">{{ processRate }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 资料内容弹窗 -->
    <confirm title="资料内容" :content="info.content" :status.sync="showContent"
      confirmText="已复制内容" confirmTextClass="cuIcon-check">
    </confirm>
  </view>
</template>

<style scoped>
.material-detail-ios {
  background: #f7f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 80vh;
}
.loading-text {
  margin-top: 24rpx;
  color: #8799a3;
  font-size: 28rpx;
}
.main-content {
  flex: 1;
  padding: 32rpx 0 120rpx 0;
}
.info-card {
  background: #fff;
  border-radius: 24rpx;
  margin: 32rpx 32rpx 0 32rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
}
.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.info-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #222;
  flex: 1;
}
.info-tags {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}
.price-tag {
  font-size: 28rpx;
  padding: 8rpx 24rpx;
}
.version-tag {
  font-size: 22rpx;
  padding: 4rpx 16rpx;
}
.info-desc {
  margin: 24rpx 0 0 0;
  color: #666;
  font-size: 28rpx;
  line-height: 1.6;
}
.info-meta {
  display: flex;
  gap: 48rpx;
  margin-top: 32rpx;
}
.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #888;
}
.meta-label {
  margin-left: 4rpx;
}
.meta-value {
  color: #39b54a;
  font-weight: 600;
  margin-left: 8rpx;
}
.preview-section {
  margin: 32rpx 32rpx 0 32rpx;
}
.preview-btn {
  font-size: 30rpx;
  padding: 24rpx 0;
  font-weight: 500;
}
.desc-card {
  background: #fff;
  border-radius: 24rpx;
  margin: 32rpx 32rpx 0 32rpx;
  padding: 32rpx;
}
.desc-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.desc-content {
  color: #666;
  font-size: 28rpx;
  line-height: 1.7;
}
.ad-section {
  margin: 40rpx 32rpx 0 32rpx;
}
.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 24rpx 0 32rpx 0;
  box-shadow: 0 -4rpx 32rpx rgba(0,0,0,0.04);
  z-index: 99;
}
.action-btn {
  flex: 1;
  margin: 0 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  padding: 24rpx 0;
  border-radius: 16rpx;
}
.shadow {
  box-shadow: 0 4rpx 32rpx rgba(0,0,0,0.06);
}
.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>

<script src="./detail.js"></script>