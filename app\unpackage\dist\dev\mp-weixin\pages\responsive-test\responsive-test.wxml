<view class="responsive-test-page data-v-f1ccd1c4"><back vue-id="5c19cda8-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="响应式测试" class="data-v-f1ccd1c4" bind:__l="__l"></back><view class="container data-v-f1ccd1c4"><view class="section data-v-f1ccd1c4"><view class="section-title data-v-f1ccd1c4">屏幕信息</view><view class="info-card data-v-f1ccd1c4"><view class="info-item data-v-f1ccd1c4"><text class="label data-v-f1ccd1c4">当前断点:</text><text class="value data-v-f1ccd1c4">{{currentBreakpoint}}</text></view><view class="info-item data-v-f1ccd1c4"><text class="label data-v-f1ccd1c4">屏幕宽度:</text><text class="value data-v-f1ccd1c4">{{screenWidth+"px"}}</text></view><view class="info-item data-v-f1ccd1c4"><text class="label data-v-f1ccd1c4">是否大屏:</text><text class="value data-v-f1ccd1c4">{{isLargeScreen?'是':'否'}}</text></view></view></view><view class="section data-v-f1ccd1c4"><view class="section-title data-v-f1ccd1c4">响应式网格</view><view class="test-grid grid grid-xs-1 grid-sm-2 grid-md-3 grid-lg-4 grid-xl-5 data-v-f1ccd1c4"><block wx:for="{{10}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><view class="grid-item data-v-f1ccd1c4"><view class="item-content data-v-f1ccd1c4">{{n}}</view></view></block></view></view><view class="section data-v-f1ccd1c4"><view class="section-title data-v-f1ccd1c4">响应式显示/隐藏</view><view class="display-test data-v-f1ccd1c4"><view class="test-item d-xs-block d-sm-none data-v-f1ccd1c4"><text class="test-text data-v-f1ccd1c4">只在超小屏显示 (xs)</text></view><view class="test-item d-xs-none d-sm-block d-md-none data-v-f1ccd1c4"><text class="test-text data-v-f1ccd1c4">只在小屏显示 (sm)</text></view><view class="test-item d-xs-none d-sm-none d-md-block d-lg-none data-v-f1ccd1c4"><text class="test-text data-v-f1ccd1c4">只在中屏显示 (md)</text></view><view class="test-item d-xs-none d-sm-none d-md-none d-lg-block d-xl-none data-v-f1ccd1c4"><text class="test-text data-v-f1ccd1c4">只在大屏显示 (lg)</text></view><view class="test-item d-xs-none d-sm-none d-md-none d-lg-none d-xl-block data-v-f1ccd1c4"><text class="test-text data-v-f1ccd1c4">只在超大屏显示 (xl)</text></view></view></view><view class="section data-v-f1ccd1c4"><view class="section-title data-v-f1ccd1c4">响应式文本对齐</view><view class="text-align-test data-v-f1ccd1c4"><text class="test-text text-xs-center text-sm-left text-md-center text-lg-right text-xl-center data-v-f1ccd1c4">这段文字在不同屏幕尺寸下有不同的对齐方式</text></view></view><view class="section data-v-f1ccd1c4"><view class="section-title data-v-f1ccd1c4">响应式间距</view><view class="spacing-test data-v-f1ccd1c4"><view class="spacing-item p-xs-1 p-sm-2 p-md-3 p-lg-3 p-xl-3 data-v-f1ccd1c4"><text class="test-text data-v-f1ccd1c4">响应式内边距</text></view></view></view></view></view>