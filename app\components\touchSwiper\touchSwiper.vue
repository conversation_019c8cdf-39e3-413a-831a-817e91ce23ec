<template>
	<view @touchstart="touchStart" @touchend="touchEnd">
		<slot></slot>
	</view>
</template>
<script>
	export default {
		name: "touchSwiper",
		props: {
			onSwiperLeft: {
				type: Function,
				default: () => {}
			},
			onSwiperRight: {
				type: Function,
				default: () => {}
			},
			onSwiperUp: {
				type: Function,
				default: () => {}
			},
			onSwiperDown: {
				type: Function,
				default: () => {}
			}
		},
		data() {
			return {
				touchStartX: 0,
				touchStartY: 0,
			};
		},
		methods: {
			touchStart(e) {
				this.touchStartX = e.touches[0].clientX;
				this.touchStartY = e.touches[0].clientY;
			},
			touchEnd(e) {
				let deltaX = e.changedTouches[0].clientX - this.touchStartX;
				let deltaY = e.changedTouches[0].clientY - this.touchStartY;
				if (Math.abs(deltaX) > 50 && Math.abs(deltaX) > Math.abs(deltaY)) {
					if (deltaX >= 0) {
						console.log("左滑");
						this.onSwiperLeft();
					} else {
						console.log("右滑");
						this.onSwiperRight();
					}
				} else if (Math.abs(deltaY) > 50 && Math.abs(deltaX) < Math.abs(deltaY)) {
					if (deltaY < 0) {
						console.log("上滑");
						this.onSwiperUp();
					} else {
						console.log("下滑");
					    this.onSwiperDown();
					}
				} else {
					console.log("误触")
				}
			},
		}
	};
</script>