<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="分享资料"></back>
		<view v-if="isLoad">
			<view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-infofill text-blue"></text> 分享指引
				</view>
				<view class="action">
					<button class="cu-btn bg-green shadow" @tap="showRuleTap">查看</button>
				</view>
			</view>
			<view class="cu-form-group">
				<view class="title">课程名称</view>
				<input name="input" :value="info.name" disabled="true"></input>
			</view>
			<view class="cu-form-group" v-if="info.code">
				<view class="title">课程代码</view>
				<input name="input" :value="info.code" disabled="true"></input>
			</view>
			<view class="cu-form-group">
				<textarea maxlength="128" @input="shareContentTap" :placeholder="ruleInfo.shareContentTip"></textarea>
			</view>
			<view class="box">
				<view class="padding flex flex-direction">
					<button @tap="submitTap" class="cu-btn bg-blue lg shadow-blur">立即提交</button>
				</view>
				<view class="padding-left padding-right flex flex-direction">
					<button @tap="recordTap" class="cu-btn bg-grey  shadow-blur ">查看已提交记录</button>
				</view>
			</view>
			<confirm title="分享指引" :content="ruleInfo.content" :status.sync="showRuleModal"
				confirmText="我知道啦" confirmTextClass="cuIcon-roundcheck">
			</confirm>
		</view>
	</view>
</template>

<script>
	let app = getApp();
	let that = null;
	let service = app.globalData.service;
	export default {
		data() {
			return {
				id: 0,
				info: [],
				isLoad: false,
				ruleInfo: [],
				shareContent: "",
				showRuleModal: false,
				checkAppIsAudit: true,
			};
		},
		onLoad: function(options) {
			that = this;
			that.id = options.id;
			that.checkAppIsAudit = app.globalData.checkAppIsAudit();
			that.getInfo();
		},
		onShow: function(options) {},
		methods: {
			shareContentTap(options) {
				that.shareContent = options.detail.value;
			},
			recordTap() {
				let url = `/pages/practice/material/record`;
				uni.navigateTo({
					url: url
				});
			},
			async submitTap() {
				let res = await service.saveMaterialShare(that.id, that.shareContent);
				app.showToast('提交成功，请等待审核！');
				setTimeout(() => {
					that.recordTap();
				}, 1000);
			},
			showRuleTap() {
				that.showRuleModal = true;
			},
			async getInfo() {
				let info = await service.courseInfo(that.id);
				let ruleInfo = await service.getMaterialShareRule();
				that.info = info.data;
				that.ruleInfo = ruleInfo.data;
				that.isLoad = true;
			},
		}
	};
</script>
<style src="./detail.css"></style>
