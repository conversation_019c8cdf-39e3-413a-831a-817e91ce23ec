<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" :title="title"></back>
		<view class="flex flex-direction" style="min-height: 100vh;">
			<!-- 页面标题区域 -->
			<view class="padding-sm" v-if="paper_page_set.show_header">
				<view class="text-xl padding-bottom-sm" v-if="paper_page_set.header_title">{{paper_page_set.header_title}}</view>
				<view class="text-sm text-gray" v-if="paper_page_set.header_desc">{{paper_page_set.header_desc}}</view>
			</view>
			
			<view class="flex-sub" style="margin-bottom: 270rpx;">
				<view class="cu-list menu" v-if="isLoad">
					<view class="cu-item margin-top-xs" v-for="(item, index) in paperList" :key="index" :data-id="item.id"
						@tap="itemTap(item)">
						<view class="content" style="padding: 24rpx 12rpx 20rpx 0rpx;line-height: 1.8em;">
							<view>
								<text class="paper-name text-sm">{{item.name}}</text>
								<text v-if="item.is_need_vip == 1" class="cuIcon-vip text-orange" style="margin-left: 10rpx; font-size: 36rpx;"></text>
							</view>
							<view class="text-gray text-sm" style="margin-top: 4rpx;">
								<text class="cuIcon-roundcheckfill text-blue" style="margin-right: 6rpx;"></text>已答题{{item.answered_count}}/{{item.question_count}}
							</view>
						</view>
						<view class="action">
							<view class="rate-container">
								<view class="text-blue correct-rate">{{item.correct_rate || 0}}%</view>
								<view class="text-grey rate-label">正确率</view>
							</view>
						</view>
					</view>
				</view>
				<empty v-if="(isLoad  && paperList.length <= 0)"></empty>
			</view>
			
			<!-- 底部提示信息 -->
			<view class="padding-sm text-center text-sm text-gray" v-if="paper_page_set.show_footer && paper_page_set.footer_text">
				{{paper_page_set.footer_text}}
			</view>
			
			<adfootbanner></adfootbanner>
		</view>
	</view>
</template>
<style src="./paper.css"></style>
<script src="./paper.js"></script>