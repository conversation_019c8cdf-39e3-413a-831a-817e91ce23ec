let that = null;
let app = getApp();
import {
	post
} from "@/common/js/http.js";

export default {
	data() {
		return {
			load: false,
			appIsAudit: false,
			info: []
		};
	},
	onLoad() {
		that = this;
		that.appIsAudit = app.globalData.checkAppIsAudit();
		if (that.appIsAudit) {
			return;
		}
		that.getInfo();
	},
	onShow() {},
	methods: {
		async getInfo() {
			let res = await post('appConfig/get', {
				config_type: 'thesis_page_set'
			});
			that.info = res.data;
			that.load = true;
		},
		saveQrCode() {
			uni.saveImageToPhotosAlbum({
				filePath: that.info.wx_qrcode_url,
				success: function() {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
				},
				fail: function() {
					uni.showToast({
						title: '保存失败',
						icon: 'none'
					});
				}
			});
		}
	}
};