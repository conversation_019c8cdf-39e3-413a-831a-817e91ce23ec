import {
	get,
	post,
	upload
} from "@/common/js/http";
let that = null;
let app = getApp();
const recorderManager = uni.getRecorderManager();
export default {
	data() {
		return {
			keyword: '',
			course_id: 0,
			isBack: false,
			isHome: false,
			isLoad: false,
			recordList: [],
			isAudioing: false,
			appIsAudit: false,
		};
	},
	onLoad(options) {
		console.log(options);
		const {
			course_id = 0
		} = options || {};
		that = this;
		that.course_id = course_id;
		that.isBack = that.isHome = course_id > 0;
		//that.listenRecorderManager();
		that.searchRecord();
		app.globalData.showShareMenu();
	},
	onShow() {
		that.appIsAudit = app.globalData.checkAppIsAudit();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		listenRecorderManager() {
			recorderManager.onError((res) => {
				console.log('录音异常:');
				console.log(res);
				that.isAudioing = false;
				app.showToast(res.errMsg);
			});
			recorderManager.onStart((res) => {
				console.log('录音开始:');
				that.isAudioing = true;
			});
			recorderManager.onPause((res) => {
				console.log('录音暂停:');
			});
			recorderManager.onStop((res) => {
				console.log('录音结束:');
				console.log(res);
				that.isAudioing = false;
				// 文件上传OSS
				let filePath = res.tempFilePath;
				let formData = {
					'app': 'learnAppSearch'
				};
				upload('file', filePath, formData, {}).then((upRes) => {
					let upResData = JSON.parse(upRes.data)
					if (upResData.code == 0) {
						app.showToast(upResData.message);
						return;
					}

					// 文件进行语音识别
					let url = upResData.data;
					post('ai/speech', {
						url: url
					}).then((aiRes) => {
						that.keyword = aiRes.data.join(",");
						app.showToast("识别成功");
					});
				});
			});
			recorderManager.onInterruptionBegin((res) => {
				console.log('录音被系统中断:');
				that.isAudioing = false;
				console.log(res);
			});
		},
		audioActionTap(action) {
			recorderManager.stop();
			that.isAudioing = false;
		},
		audioTap() {
			let checkHasVip = app.checkHasVip();
			if (!checkHasVip) {
				app.showToast('仅对邀请用户开放');
				return;
			}
			recorderManager.start();
		},
		async chooseImageTap() {
			let checkHasVip = app.checkHasVip();
			if (!checkHasVip) {
				app.showToast('仅对邀请用户开放');
				return;
			}

			// #ifdef MP-WEIXIN
			uni.chooseMedia({
				count: 1,
				mediaType: ['image'],
				sourceType: ['album', 'camera'],
				maxDuration: 30,
				async success(res) {
					await that.chooseImageOverTap(res);
				}
			})
			// #endif

			// #ifndef MP-WEIXIN
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				async success(res) {
					await that.chooseImageOverTap(res);
				}
			})
			// #endif
		},
		async chooseImageOverTap(res) {
			// 文件上传OSS
			let filePath = res.tempFiles[0].tempFilePath;
			let formData = {
				'app': 'learnAppSearch',
				'sign': app.globalData.getTimestamp()
			};
			let upRes = await upload('file', filePath, formData, {});

			// 文件进行OCR识别
			let url = upRes.data;
			let aiRes = await post('ai/ocr', {
				url: url
			});
			that.keyword = aiRes.data.join(",");
			app.showToast("识别成功");
		},
		clearTap() {
			that.keyword = '';
		},
		clearAndPasteTap() {
			that.keyword = '';
			uni.getClipboardData({
				success: (res) => {
					that.keyword = res.data;
				}
			});
		},
		onInputChange(options) {
			that.keyword = options.detail.value;
		},
		onSearchClick() {
			if (that.keyword == '') {
				app.showToast('请输入搜索关键字');
				return;
			}
			let url = '../search/list?keyword=' + that.keyword;
			uni.navigateTo({
				url: url
			});
		},
		onClickQuestion(id) {
			let url = '../practice/question/detail?id=';
			uni.navigateTo({
				url: url + id
			});
		},
		searchRecord() {
			app.globalData.server
				.getRequest('question/searchRecord', {})
				.then(function(res) {
					console.log(res);
					that.recordList = res.data.list;
					that.isLoad = true;
				})
				.catch(function(res) {
					console.log(res);
				});
		}
	}
};