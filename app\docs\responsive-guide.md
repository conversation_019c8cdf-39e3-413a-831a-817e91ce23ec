# 小程序大屏响应式适配指南

本指南介绍如何在uni-app小程序中实现类似web端的响应式适配。

## 📱 断点配置

我们定义了5个响应式断点：

- **xs**: < 576px (超小屏幕，手机竖屏)
- **sm**: 576px - 768px (小屏幕，手机横屏)
- **md**: 768px - 1024px (中等屏幕，平板竖屏)
- **lg**: 1024px - 1440px (大屏幕，平板横屏)
- **xl**: > 1440px (超大屏幕，桌面)

## 🧩 核心组件

### 1. responsive-container
响应式容器组件，提供最大宽度限制和居中布局。

```vue
<template>
  <responsive-container :maxWidth="'1200px'" :center="true">
    <view>内容</view>
  </responsive-container>
</template>
```

### 2. responsive-grid
响应式网格组件，根据屏幕尺寸自动调整列数。

```vue
<template>
  <responsive-grid :cols="{xs: 1, sm: 2, md: 3, lg: 4, xl: 5}">
    <view v-for="item in items" :key="item.id">
      {{ item.content }}
    </view>
  </responsive-grid>
</template>
```

### 3. match-media
媒体查询匹配组件，根据屏幕尺寸显示/隐藏内容。

```vue
<template>
  <match-media breakpoint="lg">
    <view>只在大屏幕显示</view>
  </match-media>
</template>
```

## 🎨 CSS工具类

### 显示/隐藏类
```css
.d-xs-none    /* 超小屏隐藏 */
.d-sm-block   /* 小屏显示为block */
.d-md-flex    /* 中屏显示为flex */
.d-lg-grid    /* 大屏显示为grid */
.d-xl-none    /* 超大屏隐藏 */
```

### 网格类
```css
.grid-xs-1    /* 超小屏1列 */
.grid-sm-2    /* 小屏2列 */
.grid-md-3    /* 中屏3列 */
.grid-lg-4    /* 大屏4列 */
.grid-xl-5    /* 超大屏5列 */
```

### 文本对齐类
```css
.text-xs-center   /* 超小屏居中 */
.text-md-left     /* 中屏左对齐 */
.text-lg-right    /* 大屏右对齐 */
```

### 间距类
```css
.p-xs-1, .p-xs-2, .p-xs-3    /* 超小屏内边距 */
.m-md-1, .m-md-2, .m-md-3    /* 中屏外边距 */
.p-lg-1, .p-lg-2, .p-lg-3    /* 大屏内边距 */
```

## 🔧 JavaScript工具

### 响应式混入
在页面中使用响应式混入：

```javascript
import { responsiveMixin } from '@/common/js/responsive.js'

export default {
  mixins: [responsiveMixin],
  computed: {
    // 可以使用以下计算属性
    // isXs, isSm, isMd, isLg, isXl
    // isLargeDevice (md及以上)
    // responsiveClass (包含所有断点类名)
  },
  methods: {
    handleClick() {
      // 根据屏幕尺寸获取不同的值
      const columns = this.getResponsiveValue({
        xs: 1,
        sm: 2,
        md: 3,
        lg: 4,
        xl: 5
      });
    }
  }
}
```

### 工具函数
```javascript
import { getCurrentBreakpoint, isLargeScreen } from '@/common/js/responsive.js'

// 获取当前屏幕信息
const screenInfo = await getCurrentBreakpoint();
console.log(screenInfo.breakpoint); // 'md'
console.log(screenInfo.isLargeScreen); // true

// 判断是否为大屏
const isLarge = await isLargeScreen();
```

## 📝 使用示例

### 1. 基础页面布局
```vue
<template>
  <view class="page" :class="responsiveClass">
    <responsive-container>
      <!-- 头部 -->
      <view class="header">
        <text class="title text-xs-center text-md-left">页面标题</text>
      </view>
      
      <!-- 内容区域 -->
      <responsive-grid class="content-grid">
        <view class="card" v-for="item in items" :key="item.id">
          {{ item.content }}
        </view>
      </responsive-grid>
      
      <!-- 侧边栏（仅大屏显示） -->
      <match-media breakpoint="lg">
        <view class="sidebar">侧边栏内容</view>
      </match-media>
    </responsive-container>
  </view>
</template>

<script>
import { responsiveMixin } from '@/common/js/responsive.js'

export default {
  mixins: [responsiveMixin],
  data() {
    return {
      items: [...]
    }
  }
}
</script>

<style>
.page {
  min-height: 100vh;
}

.header {
  padding: 20rpx 0;
  border-bottom: 2rpx solid #eee;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
}

.content-grid {
  margin-top: 20rpx;
}

.card {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

/* 大屏优化 */
@media screen and (min-width: 1024rpx) {
  .card:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 25rpx rgba(0,0,0,0.15);
  }
}
</style>
```

### 2. 响应式表单
```vue
<template>
  <responsive-container>
    <view class="form-container">
      <view class="form-grid" :class="getResponsiveValue({
        xs: 'grid-xs-1',
        sm: 'grid-sm-1', 
        md: 'grid-md-2',
        lg: 'grid-lg-2',
        xl: 'grid-xl-3'
      })">
        <view class="form-item">
          <input placeholder="姓名" />
        </view>
        <view class="form-item">
          <input placeholder="邮箱" />
        </view>
        <view class="form-item">
          <input placeholder="电话" />
        </view>
      </view>
    </view>
  </responsive-container>
</template>
```

## 🎯 最佳实践

### 1. 移动优先设计
始终从最小屏幕开始设计，然后逐步增强到大屏幕。

### 2. 合理使用断点
不要为每个断点都设置不同的样式，选择关键断点即可。

### 3. 性能优化
- 使用CSS媒体查询而不是JavaScript来控制样式
- 避免在小屏幕上加载大屏幕的资源
- 合理使用图片和字体大小

### 4. 测试
在不同尺寸的设备上测试你的应用：
- 手机竖屏 (375px)
- 手机横屏 (667px)
- 平板竖屏 (768px)
- 平板横屏 (1024px)
- 桌面 (1440px+)

## 🔧 配置文件

所有响应式配置都在 `@/common/config/responsive-config.js` 中，你可以根据需要修改断点、间距、字体大小等配置。

## 📱 小程序配置

确保在 `manifest.json` 中启用了大屏适配：

```json
{
  "mp-weixin": {
    "setting": {
      "resizable": true
    }
  }
}
```

这样就完成了小程序的大屏响应式适配，让你的小程序在各种屏幕尺寸下都能提供良好的用户体验！
