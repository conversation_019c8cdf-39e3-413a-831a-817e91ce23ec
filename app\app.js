let config = require('./common/js/config.js'),
	helper = require('./common/js/helper.js'),
	server = require('./common/js/server.js'),
	service = require('./common/js/service.js');
import * as practice from './common/js/practice.js';
import Vue from 'vue';
export default {
	data() {
		return {};
	},

	//全局变量
	globalData: {
		isIOS: false,
		appPlatform: 0,
		statusBarHeight: 0,
		titleBarHeight: 48,
		isAllScreen: false,
		isLogin: false,
		scene: 0,
		config: config,
		helper: helper,
		interval: null,
		server: server,
		service: service,
		practice: practice,
		screenHeight: 0,
		deviceInfo: null,
		windowsInfo: null,
		checkLogin() {
			var that = this;
			that.isLogin = null != uni.getStorageSync(config.storage.userInfoKey).id;
		},

		checkAppIsAudit() {
			let homeData = uni.getStorageSync(this.config.storage.homeKey);
			console.log(helper.isSet(homeData.appConfig.appVersion));
			if (!helper.isSet(homeData) || !helper.isSet(homeData.appConfig) || !helper.isSet(homeData.appConfig
					.appIsAudit)) {
				return true;
			}
			return homeData.appConfig.appIsAudit;
		},

		checkIsIosVirtualPay() {
			let homeData = uni.getStorageSync(this.config.storage.homeKey);
			return this.helper.variableDefalut(homeData.appConfig.isIosVirtualPay, true);
		},

		checkAppIsAuditAndRedirect() {
			console.log(this.checkAppIsAudit());
			if (this.checkAppIsAudit()) {
				uni.reLaunch({
					url: '/pages/index/index'
				});
			}
		},

		checkLoginAndRedirect() {
			if (!app.globalData.isLogin) {
				uni.navigateTo({
					url: '../../../../user/login/login'
				});
				return false;
			}

			return true;
		},

		/**
		 * 全局函数--获取分享配置
		 */
		getShareConfig() {
			let path = '/pages/index/city/city';
			let userInfo = this.config.storage.getUserInfoData();
			let agent_id = this.helper.variableDefalut(userInfo.id, 0);
			if (this.helper.isSet(userInfo) && userInfo != '') {
				path += '?agent_id=' + userInfo.id;
			}
			let title = '免费刷题小程序';
			return {
				title: title,
				path: path
			};
		},

		/**
		 * 全局函数-显示分享按钮
		 */
		showShareMenu() {
			// #ifndef MP-BAIDU
			uni.showShareMenu({
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			});
			// #endif		
		},

		/**
		 * 全局函数-获取当前时间戳
		 */
		getTimestamp() {
			return new Date().getTime() / 1000;
		},

		async initSystemInfo() {
			var that = this;
			that.deviceInfo = uni.getDeviceInfo();
			console.log(that.deviceInfo);
			that.windowsInfo = uni.getWindowInfo();



			Vue.prototype.CustomBar = that.windowsInfo.statusBarHeight + 50;

			// #ifndef MP
			Vue.prototype.StatusBar = that.windowsInfo.statusBarHeight;
			if (that.deviceInfo.platform == 'android') {
				Vue.prototype.CustomBar = that.windowsInfo.statusBarHeight + 50;
			} else {
				Vue.prototype.CustomBar = that.windowsInfo.statusBarHeight + 45;
			};
			// #endif

			// #ifdef MP-WEIXIN
			Vue.prototype.StatusBar = that.windowsInfo.statusBarHeight;
			let custom = wx.getMenuButtonBoundingClientRect();
			console.log(custom);
			Vue.prototype.Custom = custom;
			Vue.prototype.CustomBar = custom.bottom + custom.top - that.windowsInfo.statusBarHeight;
			// #endif		


			// 设置状态栏高度
			that.statusBarHeight = that.windowsInfo.statusBarHeight;

			// 设置屏幕高度
			that.screenHeight = that.windowsInfo.screenHeight;

			// 根据屏幕高度判断是否全面屏
			if (that.windowsInfo.screenHeight > 750) {
				that.isAllScreen = true;
			}
			if ('ios' == that.deviceInfo.osName) {
				that.isIOS = true;
			}

			that.appPlatform = await config.appPlatform();
		}
	},

	/**
	 * 生命周期函数--APP初始化
	 */
	onLaunch() {
		// 初始化系统信息
		this.globalData.initSystemInfo();
		this.globalData.checkLogin();
		// 微信小程序兼容finally	
		helper.supportPromiseFinally();
	},

	/**
	 * 生命周期函数--监听APP显示
	 */
	onShow(options) {

		// APP更新检查
		setTimeout(() => {
			this.checkAppUpdate
		}, 5000);

		// APP场景信息
		this.globalData.scene = options.scene;
		if (options == null || options == '' || options.referrerInfo == null || options.referrerInfo == '') {
			return;
		}

		// 个人跳转支付数据	
		let extraData = options.referrerInfo.extraData;
		if (extraData) {
			this.globalData.otherMcpExtraData = extraData;
		}
	},
	onBackPress(options) {
		console.log("监听到返回事件");
		uni.navigateBack({
			delta: 1
		});
	},
	methods: {

		/**
		 * 全局函数-启动定时器
		 */
		startInterval(callback, time) {
			this.stopInterval();
			this.interval = setInterval(callback, time);
		},

		/**
		 * 全局函数-停止定时器
		 */
		stopInterval() {
			if (this.interval !== null) {
				clearInterval(this.interval);
				this.interval = null;
			}
		},

		/**
		 * 全局函数--检查是否专业VIP
		 */
		checkHasVip() {
			let userInfo = this.globalData.config.storage.getUserInfoData();
			if (!userInfo) {
				return false;
			}
			return userInfo.has_vip;
		},

		/**
		 * 全局函数--检查是否代理
		 */
		checkIsAgent() {
			let userInfo = this.globalData.config.storage.getUserInfoData();
			if (!userInfo) {
				return false;
			}
			return userInfo.is_agent == 1;
		},

		/**
		 * 全局函数--提示信息
		 */
		showToast() {
			let title = arguments[0] ? arguments[0] : '';
			let mask = arguments[1] ? arguments[1] : false;
			uni.showToast({
				title: title + '',
				icon: 'none',
				duration: 2000,
				mask: mask
			});
		},

		/**
		 * 全局函数-弹窗询问
		 */
		showConfirm(content, confirm, cancel) {
			uni.showModal({
				content: content,
				confirmText: "确定",
				cancelText: "取消",
				success(res) {
					if (res.confirm) {
						confirm && confirm.call(this);
					} else {
						cancel && cancel.call(this);
					}
				}
			});
		},

		/**
		 * 全局函数--IOS拦截
		 */
		showIosNotSupport() {
			wx.showModal({
				title: "温馨提示",
				content: "十分抱歉，由于相关规范，iOS功能暂不可用",
				showCancel: !1,
				confirmText: "我知道了"
			});
		},

		/**
		 * 获取节点高度
		 * @param {Object} selector
		 */
		getNodeHeight(selector) {
			return new Promise((resolve, reject) => {
				let selectorQuery = uni.createSelectorQuery().select(selector);
				selectorQuery.boundingClientRect((res) => {
					resolve(res.height);
				}).exec();
			});
		},

		/**
		 * 全局函数-检查APP是否可更新
		 */
		checkAppUpdate() {
			if (uni.getUpdateManager()) {
				let updateManager = uni.getUpdateManager();
				updateManager.onCheckForUpdate(function(res) {
					if (res.hasUpdate) {
						updateManager.onUpdateReady(function() {
							uni.showModal({
								title: '更新提示',
								content: '新版本已经准备好，是否重启应用？',
								success(res) {
									if (res.confirm) {
										updateManager.applyUpdate();
									}
								}
							});
						});
					}
				});
			}
		}
	},
};