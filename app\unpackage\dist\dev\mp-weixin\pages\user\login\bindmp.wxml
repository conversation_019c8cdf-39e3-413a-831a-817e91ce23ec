<block wx:if="{{isLoad}}"><view><back vue-id="7765a4f6-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="绑定公众号" bind:__l="__l"></back><view><view class="cu-form-group margin-top"><view class="title">验证码</view><input maxlength="32" placeholder="请输入公众号验证码" placeholderStyle="color:#999;" type="text" disabled="{{user.mp_openid?true:false}}" data-event-opts="{{[['input',[['inputVerifyCodeTap',['$event']]]]]}}" value="{{user.mp_openid?user.mp_openid:''}}" bindinput="__e"/></view><view class="padding"><button data-event-opts="{{[['tap',[['bindVerifyTap',['$event']]]]]}}" class="cu-btn block bg-blue margin-tb-sm lg" bindtap="__e">确定绑定</button></view><view class="cu-bar bg-white solid-bottom"><view class="action"><text class="cuIcon-title text-red"></text>绑定指南</view><view class="action"><button data-event-opts="{{[['tap',[['bindOpenMp',['$event']]]]]}}" class="cu-btn bg-blue shadow" bindtap="__e"><text>显示公众号二维码</text></button></view></view><view class="cu-list menu"><block wx:for="{{pageSetData.rule}}" wx:for-item="value" wx:for-index="index" wx:key="index"><view class="cu-item"><view class="content"><text class="text-grey">{{value}}</text></view></view></block></view></view><adfootbanner vue-id="7765a4f6-2" bind:__l="__l"></adfootbanner></view></block>