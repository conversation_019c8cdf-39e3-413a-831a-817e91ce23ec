module.exports = {
	isSet(exp) {
		return typeof exp !== 'undefined' && exp !== null;
	},
	variableDefalut(exp,
		val) {
		if (this.isSet(exp)) {
			return exp;
		}
		return val;
	},
	supportPromiseFinally() {
		Promise.prototype.finally = function(callback){
			var p = this.constructor;
			return this.then(
				(value) => {
					p.resolve(callback()).then(() => value);
				},
				(reason) => {
					p.resolve(callback()).then(() => {
						throw reason;
					});
				}
			);
		};
	}
};