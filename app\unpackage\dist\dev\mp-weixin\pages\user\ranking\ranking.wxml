<block wx:if="{{isLoad}}"><view><view class="contaier" style="background-color:#ffffff;"><view class="top_bg"><view class="one_box"><view class="top3"><view class="num_two"><image class="huangguan2" src="https://learnfile.20230611.cn/app_img/two.png"></image><image class="top3_head" src="{{listData.first[1]['avatar']}}"></image><view class="top_name">{{listData.first[1]['nickname']}}</view><view class="top_sy">{{''+listData.first[1]['region_name']+" . "+listData.first[1]['count']+''}}<label class="_span">题</label></view></view></view><view class="top3"><view class="num_one"><image class="huangguan1" src="https://learnfile.20230611.cn/app_img/one.png"></image><image class="top3_head" src="{{listData.first[0]['avatar']}}"></image><view class="top_name text-bold" style="font-size:30rpx;">{{''+listData.first[0]['nickname']+''}}</view><view class="top_sy">{{''+listData.first[0]['region_name']+" . "+listData.first[0]['count']+''}}<label class="_span">题</label></view></view></view><view class="top3"><view class="num_three"><image class="huangguan2" src="https://learnfile.20230611.cn/app_img/three.png"></image><image class="top3_head" src="{{listData.first[2]['avatar']}}"></image><view class="top_name">{{listData.first[2]['nickname']}}</view><view class="top_sy">{{''+listData.first[2]['region_name']+" . "+listData.first[2]['count']+''}}<label class="_span">题</label></view></view></view></view><view class="number_sy_box"><view class="number_sy_box_title"><text>答题·统计</text><text style="position:absolute;right:20rpx;z-index:9999;font-size:24rpx;color:#c3c3c3;">机器人实时计算</text></view><view class="number_sy_main"><view style="width:50%;text-align:center;border-right:1px solid #eee;"><view class="number_num1">{{listData.avg+"道"}}</view><view class="danwei">平均答题</view></view><view style="width:50%;text-align:center;z-index:9999;"><view class="number_num2">{{listData.rate+"%"}}</view><view class="danwei">正确率</view></view><image class="xiaoding_bg" mode="widthFix" src="https://learnfile.20230611.cn/app_img/Intersect.png"></image></view></view></view><view class="rankList_box"><block wx:for="{{listData.last}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="rankItem"><view class="rankIndex"><text>{{index+4}}</text></view><view class="HeardBox"><image class="rankHeard" src="{{item.avatar}}"></image></view><view class="NameBox"><view class="userPost text-bold">{{''+item.nickname+''}}</view><view class="color_ccc"><view class="cu-capsule round"><view class="cu-tag bg-blue">{{''+item.region_name+''}}</view><view class="cu-tag line-blue">{{''+item.count+'题'}}</view></view></view></view></view></block></view></view><adbanner vue-id="793fb2b4-1" unitId="adunit-e9f553c403a978f6" bind:__l="__l"></adbanner></view></block>