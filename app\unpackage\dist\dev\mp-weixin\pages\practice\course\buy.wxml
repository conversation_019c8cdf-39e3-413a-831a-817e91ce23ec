<block wx:if="{{isLoad&&!appIsAudit}}"><view class="course-buy-container"><back vue-id="6feb0ea9-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="开通题库" bind:__l="__l"></back><view class="main-content"><view class="course-card"><view class="card-header"><text class="card-title">题库详情</text></view><view class="card-body"><view class="info-item"><view class="info-label"><text class="cuIcon-infofill icon-custom"></text><text>题库名称</text></view><view class="info-value">{{info.courseInfo.name+info.courseInfo.code}}</view></view><block wx:if="{{info.courseInfo.price}}"><view class="info-item"><view class="info-label"><text class="cuIcon-rechargefill icon-custom"></text><text>题库价格</text></view><view class="info-value price">{{"¥ "+info.courseInfo.price}}</view></view></block><view class="info-item"><view class="info-label"><text class="cuIcon-babyfill icon-custom"></text><text>题库特权</text></view><view class="info-value"><view class="tag-container"><text class="content-tag">尊享VIP题库</text></view></view></view><view class="info-item"><view class="info-label"><text class="cuIcon-timefill icon-custom"></text><text>有效时长</text></view><view class="info-value">{{info.courseInfo.expire+info.courseInfo.expire_type_name}}</view></view></view></view><view class="agreement-card"><view class="card-header"><text class="cuIcon-titles icon-title"></text><text class="card-title">服务协议</text></view><view class="card-body"><view class="agreement-list"><block wx:for="{{info.noticeList}}" wx:for-item="notice" wx:for-index="index" wx:key="index"><view class="agreement-item"><text class="agreement-number">{{index+1}}</text><text class="agreement-text">{{notice}}</text></view></block></view></view></view></view><view class="footer-action"><button data-event-opts="{{[['tap',[['onBuyCommit',['$event']]]]]}}" class="confirm-btn" bindtap="__e">确认开通</button></view></view></block>