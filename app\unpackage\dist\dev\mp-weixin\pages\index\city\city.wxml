<view><back vue-id="14162bfd-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" showTitle="{{true}}" title="选择省份" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view><view class="cu-bar bg-white search fixed" style="{{('top:'+CustomBar+'px;')}}"><view class="search-form round"><text class="cuIcon-search"></text><input type="text" placeholder="输入省名称" confirm-type="search" data-event-opts="{{[['input',[['handleSearchInput',['$event']]]],['confirm',[['handleSearchCity',['$event']]]]]}}" bindinput="__e" bindconfirm="__e"/></view><view class="action"><button data-event-opts="{{[['tap',[['handleSearchCity',['$event']]]]]}}" class="cu-btn bg-gradual-blue shadow-blur round" bindtap="__e">搜索</button></view></view><scroll-view class="indexes" style="{{('height:calc(100vh - '+CustomBar+'px - 50px)')}}" scroll-y="{{true}}" scroll-into-view="{{'indexes-'+listCurID}}" scroll-with-animation="{{true}}" enable-back-to-top="{{true}}"><block wx:for="{{listData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['padding indexItem-'+index]}}" id="{{'indexes-'+index}}" data-index="{{index}}">{{''+index+''}}</view><view class="cu-list menu-avatar no-padding"><block wx:for="{{listData[index]}}" wx:for-item="item" wx:for-index="sub" wx:key="index2"><view class="cu-item" data-data="{{item}}" data-event-opts="{{[['tap',[['handleCitySelect',['$event']]]]]}}" bindtap="__e"><view class="cu-avatar round lg bg-blue">{{listData[index][sub]['initial']}}</view><view class="content"><view><text class="text-abc">{{listData[index][sub]['name']}}</text></view></view></view></block></view></block></block><adbanner vue-id="14162bfd-2" bind:__l="__l"></adbanner></scroll-view><view class="indexBar" style="{{('height:calc(100vh - '+CustomBar+'px - 50px)')}}"><view data-event-opts="{{[['touchstart',[['handleTouchStart',['$event']]]],['touchend',[['handleTouchEnd',['$event']]]],['touchmove',[['handleTouchMove',['$event']]]]]}}" class="indexBar-box" bindtouchstart="__e" bindtouchend="__e" catchtouchmove="__e"><block wx:for="{{listData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="indexBar-item" id="{{index}}" data-event-opts="{{[['touchstart',[['handleGetCurrent',['$event']]]],['touchend',[['handleSetCurrent',['$event']]]]]}}" bindtouchstart="__e" bindtouchend="__e">{{''+index+''}}</view></block></view></view><block wx:if="{{!isHidden}}"><view class="indexToast">{{''+listCur+''}}</view></block></view></block></view>