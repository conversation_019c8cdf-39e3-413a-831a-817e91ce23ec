<template>
	<view v-if="!checkAppIsAudit">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" :title="title"></back>
		
		<!-- 成功图标和提示 -->
		<view class="success-container bg-white padding">
			<view class="success-icon-box">
				<text class="cuIcon-roundcheckfill text-green success-icon"></text>
			</view>
			<view class="text-center padding-tb">
				<text class="text-xxl text-black text-bold">支付成功</text>
			</view>
			<view class="text-center text-df text-grey">
				<text class="text-black">您的订单已支付成功，感谢您的购买！</text>
			</view>
		</view>
		
		<!-- 订单信息卡片 -->
		<view class="order-card bg-white margin-top-sm radius shadow-warp">
			<view class="cu-bar solid-bottom">
				<view class="action">
					<text class="cuIcon-title text-blue"></text>
					<text class="text-bold">订单信息</text>
				</view>
			</view>
			<view class="padding-sm">
				<view class="cu-list menu sm-border">
					<view class="cu-item">
						<view class="content">
							<text class="cuIcon-time text-blue"></text>
							<text>支付时间</text>
						</view>
						<view class="action">
							<text>{{ new Date().toLocaleString() }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 客服提示 -->
		<view class="cu-bar bg-white margin-top-sm radius shadow-warp margin-top-sm" style="margin-left: 30rpx; margin-right: 30rpx;">
			<view class="action">
				<text class="cuIcon-service text-blue margin-right-xs"></text>
				如未实际到账请联系在线客服
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="padding-xl flex flex-direction">
			<button @tap="onLinkClick" class="cu-btn round bg-gradual-blue lg shadow" data-id="1">
				<text class="cuIcon-home margin-right-xs"></text>题库首页
			</button>
			<button @tap="onLinkClick" class="cu-btn round bg-blue light margin-top-sm lg shadow" data-id="2">
				<text class="cuIcon-my margin-right-xs"></text>个人中心
			</button>
		</view>
	</view>
</template>
<style src="./payRes.css"></style>
<script src="./payRes.js"></script>