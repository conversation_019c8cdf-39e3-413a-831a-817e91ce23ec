{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/responsive-test/responsive-test.vue?5af2", "webpack:///D:/桌面/thinker/app/pages/responsive-test/responsive-test.vue?22eb", "webpack:///D:/桌面/thinker/app/pages/responsive-test/responsive-test.vue?85ec", "webpack:///D:/桌面/thinker/app/pages/responsive-test/responsive-test.vue?cba2", "uni-app:///pages/responsive-test/responsive-test.vue", "webpack:///D:/桌面/thinker/app/pages/responsive-test/responsive-test.vue?7e76", "webpack:///D:/桌面/thinker/app/pages/responsive-test/responsive-test.vue?5115"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "currentBreakpoint", "screenWidth", "isLargeScreen", "onLoad", "onResize", "methods", "updateScreenInfo", "uni", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACqC;;;AAGnG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4EvrB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACAC;QACAC;UACA;UAEA;YACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;UAEA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAAo+B,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACAx/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/responsive-test/responsive-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/responsive-test/responsive-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./responsive-test.vue?vue&type=template&id=f1ccd1c4&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-test.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-test.vue?vue&type=style&index=0&id=f1ccd1c4&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f1ccd1c4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/responsive-test/responsive-test.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-test.vue?vue&type=template&id=f1ccd1c4&scoped=true&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-test.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"responsive-test-page\">\n    <back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"响应式测试\"></back>\n    \n    <!-- 使用CSS类的响应式布局 -->\n    <view class=\"container\">\n      <view class=\"section\">\n        <view class=\"section-title\">屏幕信息</view>\n        <view class=\"info-card\">\n          <view class=\"info-item\">\n            <text class=\"label\">当前断点:</text>\n            <text class=\"value\">{{ currentBreakpoint }}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"label\">屏幕宽度:</text>\n            <text class=\"value\">{{ screenWidth }}px</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"label\">是否大屏:</text>\n            <text class=\"value\">{{ isLargeScreen ? '是' : '否' }}</text>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"section\">\n        <view class=\"section-title\">响应式网格</view>\n        <view class=\"test-grid grid grid-xs-1 grid-sm-2 grid-md-3 grid-lg-4 grid-xl-5\">\n          <view class=\"grid-item\" v-for=\"n in 10\" :key=\"n\">\n            <view class=\"item-content\">{{ n }}</view>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"section\">\n        <view class=\"section-title\">响应式显示/隐藏</view>\n        <view class=\"display-test\">\n          <view class=\"test-item d-xs-block d-sm-none\">\n            <text class=\"test-text\">只在超小屏显示 (xs)</text>\n          </view>\n          <view class=\"test-item d-xs-none d-sm-block d-md-none\">\n            <text class=\"test-text\">只在小屏显示 (sm)</text>\n          </view>\n          <view class=\"test-item d-xs-none d-sm-none d-md-block d-lg-none\">\n            <text class=\"test-text\">只在中屏显示 (md)</text>\n          </view>\n          <view class=\"test-item d-xs-none d-sm-none d-md-none d-lg-block d-xl-none\">\n            <text class=\"test-text\">只在大屏显示 (lg)</text>\n          </view>\n          <view class=\"test-item d-xs-none d-sm-none d-md-none d-lg-none d-xl-block\">\n            <text class=\"test-text\">只在超大屏显示 (xl)</text>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"section\">\n        <view class=\"section-title\">响应式文本对齐</view>\n        <view class=\"text-align-test\">\n          <text class=\"test-text text-xs-center text-sm-left text-md-center text-lg-right text-xl-center\">\n            这段文字在不同屏幕尺寸下有不同的对齐方式\n          </text>\n        </view>\n      </view>\n\n      <view class=\"section\">\n        <view class=\"section-title\">响应式间距</view>\n        <view class=\"spacing-test\">\n          <view class=\"spacing-item p-xs-1 p-sm-2 p-md-3 p-lg-3 p-xl-3\">\n            <text class=\"test-text\">响应式内边距</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'responsive-test',\n  data() {\n    return {\n      currentBreakpoint: 'xs',\n      screenWidth: 375,\n      isLargeScreen: false\n    }\n  },\n  onLoad() {\n    this.updateScreenInfo();\n  },\n  onResize() {\n    this.updateScreenInfo();\n  },\n  methods: {\n    updateScreenInfo() {\n      uni.getSystemInfo({\n        success: (res) => {\n          this.screenWidth = res.windowWidth;\n          \n          if (res.windowWidth >= 1441) {\n            this.currentBreakpoint = 'xl';\n          } else if (res.windowWidth >= 1025) {\n            this.currentBreakpoint = 'lg';\n          } else if (res.windowWidth >= 769) {\n            this.currentBreakpoint = 'md';\n          } else if (res.windowWidth >= 577) {\n            this.currentBreakpoint = 'sm';\n          } else {\n            this.currentBreakpoint = 'xs';\n          }\n          \n          this.isLargeScreen = res.windowWidth >= 769;\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.responsive-test-page {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20rpx;\n}\n\n.section {\n  margin-bottom: 40rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  padding: 20rpx 0;\n  border-bottom: 2rpx solid #e0e0e0;\n}\n\n.info-card {\n  background: white;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.info-item:last-child {\n  border-bottom: none;\n}\n\n.label {\n  font-weight: bold;\n  color: #666;\n}\n\n.value {\n  color: #007aff;\n  font-weight: bold;\n}\n\n.test-grid {\n  gap: 20rpx;\n}\n\n.grid-item {\n  background: white;\n  border-radius: 8rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.item-content {\n  padding: 40rpx;\n  text-align: center;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: white;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.display-test {\n  background: white;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.test-item {\n  margin: 20rpx 0;\n  padding: 20rpx;\n  border-radius: 8rpx;\n  background: #f8f9fa;\n  border: 2rpx solid #e9ecef;\n}\n\n.test-text {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.text-align-test {\n  background: white;\n  border-radius: 12rpx;\n  padding: 40rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.spacing-test {\n  background: white;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.spacing-item {\n  background: #f8f9fa;\n  border-radius: 8rpx;\n  border: 2rpx solid #e9ecef;\n}\n\n/* 大屏优化 */\n@media screen and (min-width: 1024rpx) {\n  .container {\n    padding: 40rpx;\n  }\n  \n  .section-title {\n    font-size: 36rpx;\n  }\n  \n  .info-card,\n  .display-test,\n  .text-align-test,\n  .spacing-test {\n    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);\n  }\n  \n  .grid-item:hover .item-content {\n    transform: scale(1.05);\n    transition: transform 0.3s ease;\n  }\n  \n  .test-item:hover {\n    transform: translateY(-2rpx);\n    box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.1);\n    transition: all 0.3s ease;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-test.vue?vue&type=style&index=0&id=f1ccd1c4&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-test.vue?vue&type=style&index=0&id=f1ccd1c4&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753559962786\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}