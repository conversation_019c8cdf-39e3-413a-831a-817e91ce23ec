<view><back vue-id="2e57f71c-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="我的收藏" bind:__l="__l"></back><view class="flex flex-direction" style="min-height:100vh;"><view class="flex-sub" style="margin-bottom:270rpx;"><scroll-view class="bg-white nav text-center top" scroll-x="{{true}}"><block wx:for="{{selectModel}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['cu-item',index==selectModelIndex?'text-blue cur':'']}}" data-id="{{index}}" data-event-opts="{{[['tap',[['selectModelTap',['$event']]]]]}}" bindtap="__e">{{''+item+''}}</view></block></scroll-view><block wx:if="{{isLoad==true}}"><view class="margin-top-xs"><block wx:for="{{listData}}" wx:for-item="course" wx:for-index="idx" wx:key="idx"><view class="course-layout margin-bottom-xs" data-item="{{course}}" data-event-opts="{{[['tap',[['courseTap',['$event']]]]]}}" bindtap="__e"><view class="name-layout"><text>{{course.name}}</text><courselabel vue-id="{{'2e57f71c-2-'+idx}}" code="{{course.code}}" topicCount="{{course.count?'共'+course.count+'题':''}}" bind:__l="__l"></courselabel></view><text class="cuIcon-right"></text></view></block></view></block><block wx:if="{{$root.g0}}"><empty vue-id="2e57f71c-3" bind:__l="__l"></empty></block></view><adfootbanner vue-id="2e57f71c-4" bind:__l="__l"></adfootbanner></view></view>