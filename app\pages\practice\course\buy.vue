<template>
	<view v-if="isLoad && !appIsAudit" class="course-buy-container">
		<!-- 保留原有的back组件 -->
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="开通题库"></back>
		
		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 课程信息卡片 -->
			<view class="course-card">
				<view class="card-header">
					<text class="card-title">题库详情</text>
				</view>
				<view class="card-body">
					<view class="info-item">
						<view class="info-label">
							<text class="cuIcon-infofill icon-custom"></text>
							<text>题库名称</text>
						</view>
						<view class="info-value">{{info.courseInfo.name}}{{info.courseInfo.code}}</view>
					</view>
					
					<view class="info-item" v-if="info.courseInfo.price">
						<view class="info-label">
							<text class="cuIcon-rechargefill icon-custom"></text>
							<text>题库价格</text>
						</view>
						<view class="info-value price">¥ {{info.courseInfo.price}}</view>
					</view>
					
					<view class="info-item">
						<view class="info-label">
							<text class="cuIcon-babyfill icon-custom"></text>
							<text>题库特权</text>
						</view>
						<view class="info-value">
							<view class="tag-container">
								<text class="content-tag">尊享VIP题库</text>
							</view>
						</view>
					</view>
					
					<view class="info-item">
						<view class="info-label">
							<text class="cuIcon-timefill icon-custom"></text>
							<text>有效时长</text>
						</view>
						<view class="info-value">{{info.courseInfo.expire}}{{info.courseInfo.expire_type_name}}</view>
					</view>
				</view>
			</view>
			
			<!-- 服务协议卡片 -->
			<view class="agreement-card">
				<view class="card-header">
					<text class="cuIcon-titles icon-title"></text>
					<text class="card-title">服务协议</text>
				</view>
				<view class="card-body">
					<view class="agreement-list">
						<view class="agreement-item" v-for="(notice, index) in info.noticeList" :key="index">
							<text class="agreement-number">{{index+1}}</text>
							<text class="agreement-text">{{ notice }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部确认按钮 -->
		<view class="footer-action">
			<button @tap="onBuyCommit" class="confirm-btn">确认开通</button>
		</view>
	</view>
</template>

<style src="./buy.css"></style>
<script src="./buy.js"></script>
