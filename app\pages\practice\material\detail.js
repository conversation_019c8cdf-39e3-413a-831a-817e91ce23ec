let app = getApp();
let that = null;
let config = app.globalData.config;
export default {
	data() {
		return {
			id: 0,
			info: [],
			isLoad: false,
			isLock: false,
			processRate: '',
			showProcessModal: false,
			showContent: false,
			checkAppIsAudit: true
		};
	},
	onLoad(options) {
		that = this;
		that.id = options.id;
		that.checkAppIsAudit = app.globalData.checkAppIsAudit();
	},
	onShow(options) {
		that.getInfo();
		that.initProcess();
		app.globalData.showShareMenu();
	},
	onShareAppMessage() {
		let info = that.info;
		let config = app.globalData.getShareConfig();
		config.title = info.name;
		config.path = '/pages/practice/material/detail?id=' + info.id;
		return config;
	},
	methods: {
		initProcess() {
			that.processRate = '1%';
		},
		openTap() {
			console.log('openTap clicked');
			let info = that.info;
			console.log('Material info:', info);

			if (!info.has_material) {
				console.log('User has not purchased this material');
				app.showToast('您未购买此资料');
				return;
			}

			console.log('Setting showContent to true');
			that.showContent = true;

			console.log('Copying content to clipboard:', info.content);
			uni.setClipboardData({
				data: info.content ? info.content.toString() : '',
				success: function() {
					console.log('Content copied successfully');
				},
				fail: function(err) {
					console.error('Failed to copy content:', err);
				}
			});
		},
		async getInfo() {
			let info = await app.globalData.service.getMaterialInfo(that.id);
			that.info = info.data;
			that.isLoad = true;
		},
		async readTap() {
			that.initProcess();
			let info = that.info;
			let remote_url = info.remote_url;
			if (!info.has_material) {
				app.showToast('您未购买此资料');
				return;
			}
			if (app.globalData.appPlatform == 10) {
				window.location.href = remote_url;
				return;
			}
			let filePath = '';
			let cacheMaterialFile = await that.getCacheMaterialFile(remote_url);
			if (cacheMaterialFile.code == 1) {
				filePath = cacheMaterialFile.filePath;
			} else {
				let remoteMaterialFile = await that.downloadFileToTempPath(remote_url);
				if (remoteMaterialFile.code == 1) {
					filePath = remoteMaterialFile.filePath;
				}
			}
			if (filePath != '') {
				uni.openDocument({
					filePath: filePath,
					showMenu: true,
					fail: function(res) {
						app.showToast(`读取资料失败:${res.errMsg}`);
					}
				});
			}
		},
		async exchangeTap() {
			let info = that.info;
			app.showConfirm(`确认使用 ${info.price} 积分兑换？`, async () => {
				let res = await app.globalData.service.exchangeMaterial(that.id);
				if (res.code == 1) {
					that.getInfo();
					setTimeout(() => {
						app.showToast('恭喜您，购买成功')
					}, 1000)
				}
				console.log(res);
			}, () => {
				app.showToast('您已取消兑换')
			});
		},
		async buyTap() {
			app.showConfirm('确认购买此资料，虚拟商品不支持退费？', async () => {
				let res = await app.globalData.service.createOrder({
					type: 3,
					material_id: that.id
				});
				uni.navigateTo({
					url: '/pages/pay/pay?id=' + res.data.order_id
				});
			}, () => {
				app.showToast('您已取消支付')
			});
		},
		async helpTaskTap() {
			let helpTaskInfo = await app.globalData.service.createHelpTask(that.id, 20);
			let helpTaskId = helpTaskInfo.data.id;
			let url = `/pages/practice/group/group?id=${helpTaskId}`;
			uni.navigateTo({
				url: url
			});
		},
		downloadTap() {
			let info = that.info;
			let remote_url = info.remote_url;
			if (app.globalData.appPlatform == 10) {
				window.location.href = remote_url;
				return;
			}
			uni.showModal({
				content: '点击右上角菜单，选择保存到手机，即可成功下载文档',
				showCancel: false,
				confirmText: "我知道了"
			});
		},
		async getCacheMaterialFile(remote_url) {
			return new Promise((resolve, reject) => {
				let cacheFilePath = app.globalData.config.storage.getMaterialData(remote_url);
				if (cacheFilePath) {
					let appPlatform = app.globalData.appPlatform;
					let fileSystemManager = appPlatform == 30 ? uni : uni.getFileSystemManager();
					let cacheFilePathExist = false;
					fileSystemManager.getFileInfo({
						'filePath': cacheFilePath,
						'success': function(res) {
							resolve({
								'code': 1,
								'filePath': cacheFilePath,
							});
						},
						'fail': function(res) {
							resolve({
								'code': 0,
								'filePath': '',
							});
						},
					});
				} else {
					resolve({
						'code': 0,
						'filePath': '',
					});
				}
			});
		},
		async downloadFileToTempPath(remote_url) {
			if (that.isLock == true) {
				return;
			}
			that.isLock = true
			return new Promise((resolve, reject) => {
				that.showProcessModal = true;
				const downloadTask = uni.downloadFile({
					url: remote_url,
					success: (res) => {
						if (res.statusCode === 200) {
							let filePath = res.tempFilePath;
							app.globalData.config.storage.setMaterialData(remote_url, filePath)
							resolve({
								'code': 1,
								'filePath': filePath,
							});
						} else {
							app.showToast('下载完成时出现错误:' + res.errMsg);
							resolve({
								'code': 0,
								'filePath': '',
							});
						}
					},
					fail: (res) => {
						app.showToast('下载资料失败请重试');
						resolve({
							'code': 0,
							'filePath': '',
						});
					},
					complete: () => {
						setTimeout(() => {
							that.isLock = false;
						}, 1500)
						that.showProcessModal = false;
						that.initProcess();
					}
				});
				downloadTask.onProgressUpdate((res) => {
					that.processRate = `${res.progress}%`;
				});
			})
		}
	}
};