/**
 * 响应式配置文件
 * 统一管理小程序大屏适配的配置
 */

// 断点配置（单位：px）
export const BREAKPOINTS = {
  xs: 576,    // 超小屏幕
  sm: 768,    // 小屏幕
  md: 1024,   // 中等屏幕
  lg: 1440,   // 大屏幕
  xl: 1920    // 超大屏幕
};

// 容器最大宽度配置
export const CONTAINER_MAX_WIDTH = {
  xs: '100%',
  sm: '720rpx',
  md: '960rpx',
  lg: '1200rpx',
  xl: '1400rpx'
};

// 网格列数配置
export const GRID_COLUMNS = {
  xs: 1,
  sm: 2,
  md: 3,
  lg: 4,
  xl: 5
};

// 间距配置
export const SPACING = {
  xs: {
    small: '10rpx',
    medium: '15rpx',
    large: '20rpx'
  },
  sm: {
    small: '15rpx',
    medium: '20rpx',
    large: '25rpx'
  },
  md: {
    small: '20rpx',
    medium: '30rpx',
    large: '40rpx'
  },
  lg: {
    small: '25rpx',
    medium: '35rpx',
    large: '45rpx'
  },
  xl: {
    small: '30rpx',
    medium: '40rpx',
    large: '50rpx'
  }
};

// 字体大小配置
export const FONT_SIZE = {
  xs: {
    small: '24rpx',
    medium: '28rpx',
    large: '32rpx',
    xlarge: '36rpx'
  },
  sm: {
    small: '26rpx',
    medium: '30rpx',
    large: '34rpx',
    xlarge: '38rpx'
  },
  md: {
    small: '28rpx',
    medium: '32rpx',
    large: '36rpx',
    xlarge: '40rpx'
  },
  lg: {
    small: '30rpx',
    medium: '34rpx',
    large: '38rpx',
    xlarge: '42rpx'
  },
  xl: {
    small: '32rpx',
    medium: '36rpx',
    large: '40rpx',
    xlarge: '44rpx'
  }
};

// 圆角配置
export const BORDER_RADIUS = {
  xs: {
    small: '6rpx',
    medium: '8rpx',
    large: '10rpx'
  },
  sm: {
    small: '8rpx',
    medium: '10rpx',
    large: '12rpx'
  },
  md: {
    small: '10rpx',
    medium: '12rpx',
    large: '16rpx'
  },
  lg: {
    small: '12rpx',
    medium: '16rpx',
    large: '20rpx'
  },
  xl: {
    small: '16rpx',
    medium: '20rpx',
    large: '24rpx'
  }
};

// 阴影配置
export const BOX_SHADOW = {
  xs: '0 2rpx 8rpx rgba(0,0,0,0.1)',
  sm: '0 2rpx 10rpx rgba(0,0,0,0.1)',
  md: '0 4rpx 15rpx rgba(0,0,0,0.1)',
  lg: '0 6rpx 20rpx rgba(0,0,0,0.12)',
  xl: '0 8rpx 25rpx rgba(0,0,0,0.15)'
};

// 页面布局配置
export const LAYOUT_CONFIG = {
  // 侧边栏配置
  sidebar: {
    width: {
      md: '250rpx',
      lg: '300rpx',
      xl: '350rpx'
    },
    showBreakpoint: 'md' // 从md断点开始显示侧边栏
  },
  
  // 导航栏配置
  navbar: {
    height: {
      xs: '88rpx',
      sm: '96rpx',
      md: '104rpx',
      lg: '112rpx',
      xl: '120rpx'
    }
  },
  
  // 卡片配置
  card: {
    padding: SPACING,
    borderRadius: BORDER_RADIUS,
    shadow: BOX_SHADOW
  }
};

// 组件默认配置
export const COMPONENT_DEFAULTS = {
  // 响应式容器
  responsiveContainer: {
    maxWidth: CONTAINER_MAX_WIDTH,
    padding: SPACING,
    center: true
  },
  
  // 响应式网格
  responsiveGrid: {
    cols: GRID_COLUMNS,
    gap: SPACING
  },
  
  // 响应式按钮
  responsiveButton: {
    height: {
      xs: '80rpx',
      sm: '88rpx',
      md: '96rpx',
      lg: '104rpx',
      xl: '112rpx'
    },
    fontSize: FONT_SIZE,
    borderRadius: BORDER_RADIUS
  }
};

// 媒体查询字符串生成器
export function generateMediaQuery(breakpoint, type = 'min') {
  const width = BREAKPOINTS[breakpoint];
  if (!width) return '';
  
  if (type === 'min') {
    return `(min-width: ${width}px)`;
  } else if (type === 'max') {
    return `(max-width: ${width - 1}px)`;
  } else if (type === 'between') {
    const nextBreakpoint = getNextBreakpoint(breakpoint);
    if (nextBreakpoint) {
      return `(min-width: ${width}px) and (max-width: ${BREAKPOINTS[nextBreakpoint] - 1}px)`;
    }
    return `(min-width: ${width}px)`;
  }
  
  return '';
}

// 获取下一个断点
function getNextBreakpoint(current) {
  const breakpointKeys = Object.keys(BREAKPOINTS);
  const currentIndex = breakpointKeys.indexOf(current);
  return breakpointKeys[currentIndex + 1];
}

// 根据断点获取配置值
export function getBreakpointValue(config, breakpoint) {
  if (typeof config === 'object' && config !== null) {
    return config[breakpoint] || config.xs || config.default;
  }
  return config;
}

// 检查是否为大屏设备
export function isLargeScreen(width) {
  return width >= BREAKPOINTS.md;
}

// 检查是否为移动设备
export function isMobileScreen(width) {
  return width < BREAKPOINTS.md;
}

// 获取当前断点
export function getCurrentBreakpointName(width) {
  if (width >= BREAKPOINTS.xl) return 'xl';
  if (width >= BREAKPOINTS.lg) return 'lg';
  if (width >= BREAKPOINTS.md) return 'md';
  if (width >= BREAKPOINTS.sm) return 'sm';
  return 'xs';
}

export default {
  BREAKPOINTS,
  CONTAINER_MAX_WIDTH,
  GRID_COLUMNS,
  SPACING,
  FONT_SIZE,
  BORDER_RADIUS,
  BOX_SHADOW,
  LAYOUT_CONFIG,
  COMPONENT_DEFAULTS,
  generateMediaQuery,
  getBreakpointValue,
  isLargeScreen,
  isMobileScreen,
  getCurrentBreakpointName
};
