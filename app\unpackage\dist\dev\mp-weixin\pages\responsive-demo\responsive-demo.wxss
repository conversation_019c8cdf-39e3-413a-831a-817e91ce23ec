
.responsive-demo.data-v-4f69b280 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.demo-section.data-v-4f69b280 {
  margin-bottom: 40rpx;
}
.section-title.data-v-4f69b280 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #e0e0e0;
}
.info-card.data-v-4f69b280 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.info-item.data-v-4f69b280 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-item.data-v-4f69b280:last-child {
  border-bottom: none;
}
.label.data-v-4f69b280 {
  font-weight: bold;
  color: #666;
}
.value.data-v-4f69b280 {
  color: #007aff;
  font-weight: bold;
}
.demo-grid.data-v-4f69b280 {
  gap: 20rpx;
}
.grid-item.data-v-4f69b280 {
  background: white;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.item-content.data-v-4f69b280 {
  padding: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
.media-demo.data-v-4f69b280 {
  padding: 30rpx;
  margin: 20rpx 0;
  border-radius: 8rpx;
  text-align: center;
  font-weight: bold;
  color: white;
}
.media-demo.xs.data-v-4f69b280 {
  background: #ff6b6b;
}
.media-demo.sm.data-v-4f69b280 {
  background: #4ecdc4;
}
.media-demo.md.data-v-4f69b280 {
  background: #45b7d1;
}
.media-demo.lg.data-v-4f69b280 {
  background: #96ceb4;
}
.media-demo.xl.data-v-4f69b280 {
  background: #feca57;
}
.text-demo.data-v-4f69b280 {
  background: white;
  padding: 40rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.layout-demo.data-v-4f69b280 {
  display: flex;
  gap: 20rpx;
  min-height: 400rpx;
}
.sidebar.data-v-4f69b280 {
  flex: 0 0 300rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.sidebar-content.data-v-4f69b280 {
  padding: 30rpx;
  text-align: center;
  font-weight: bold;
  color: #666;
}
.main-content.data-v-4f69b280 {
  flex: 1;
}
.content-card.data-v-4f69b280 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  height: 100%;
}
.card-title.data-v-4f69b280 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.card-body.data-v-4f69b280 {
  color: #666;
  line-height: 1.6;
}

/* 大屏优化 */
@media screen and (min-width: 1024rpx) {
.section-title.data-v-4f69b280 {
    font-size: 36rpx;
}
.info-card.data-v-4f69b280,
  .content-card.data-v-4f69b280 {
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}
.grid-item:hover .item-content.data-v-4f69b280 {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
    transition: -webkit-transform 0.3s ease;
    transition: transform 0.3s ease;
    transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
}

