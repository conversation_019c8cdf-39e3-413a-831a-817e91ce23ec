let app = getApp();
let that = null;
export default {
	data() {
		return {
			info: {},
			isLoad: false,
		};
	},
	// 格式化时间戳为年月日格式
	filters: {
		formatDate(timestamp) {
			if (!timestamp) return '刚刚发布';
			
			const date = new Date(timestamp * 1000);
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			
			return `${year}-${month}-${day}`;
		}
	},
	onLoad(options) {
		app.globalData.showShareMenu();
		let id = options.id;
		(that = this).getInfo(id);
	},
	onShareAppMessage() {
		return {
			title: that.info.title,
			desc: that.info.summary || that.info.title,
			path: '/pages/index/article/detail?id=' + that.info.id
		}
	},
	methods: {
		async getInfo(id) {
			try {
				let info = await app.globalData.service.getArticleInfo(id);
				that.info = info.data;
				
				// 处理可能缺失的数据
				if (!that.info.publish_time) {
					that.info.publish_time = '最近发布';
				}
				that.isLoad = true;
			} catch (error) {
				console.error('获取文章详情失败', error);
				app.showToast('获取文章详情失败');
			}
		}
	}
};