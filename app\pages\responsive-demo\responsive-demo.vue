<template>
  <view class="responsive-demo" :class="responsiveClass">
    <back :showBackText="false" customClass="bg-gradual-blue text-white" title="响应式演示"></back>
    
    <!-- 响应式容器演示 -->
    <responsive-container>
      <view class="demo-section">
        <view class="section-title">屏幕信息</view>
        <view class="info-card">
          <view class="info-item">
            <text class="label">断点:</text>
            <text class="value">{{ screenInfo.breakpoint }}</text>
          </view>
          <view class="info-item">
            <text class="label">宽度:</text>
            <text class="value">{{ screenInfo.width }}px</text>
          </view>
          <view class="info-item">
            <text class="label">高度:</text>
            <text class="value">{{ screenInfo.height }}px</text>
          </view>
          <view class="info-item">
            <text class="label">大屏设备:</text>
            <text class="value">{{ isLargeDevice ? '是' : '否' }}</text>
          </view>
        </view>
      </view>
    </responsive-container>

    <!-- 响应式网格演示 -->
    <responsive-container>
      <view class="demo-section">
        <view class="section-title">响应式网格</view>
        <responsive-grid class="demo-grid">
          <view class="grid-item" v-for="n in 12" :key="n">
            <view class="item-content">{{ n }}</view>
          </view>
        </responsive-grid>
      </view>
    </responsive-container>

    <!-- 媒体查询演示 -->
    <responsive-container>
      <view class="demo-section">
        <view class="section-title">媒体查询演示</view>
        
        <match-media breakpoint="xs">
          <view class="media-demo xs">超小屏幕 (xs) 显示</view>
        </match-media>
        
        <match-media breakpoint="sm">
          <view class="media-demo sm">小屏幕 (sm) 显示</view>
        </match-media>
        
        <match-media breakpoint="md">
          <view class="media-demo md">中等屏幕 (md) 显示</view>
        </match-media>
        
        <match-media breakpoint="lg">
          <view class="media-demo lg">大屏幕 (lg) 显示</view>
        </match-media>
        
        <match-media breakpoint="xl">
          <view class="media-demo xl">超大屏幕 (xl) 显示</view>
        </match-media>
      </view>
    </responsive-container>

    <!-- 响应式文本演示 -->
    <responsive-container>
      <view class="demo-section">
        <view class="section-title">响应式文本</view>
        <view class="text-demo">
          <text class="text-xs-sm text-sm-md text-md-lg text-lg-lg text-xl-lg">
            这段文字在不同屏幕尺寸下显示不同大小
          </text>
        </view>
      </view>
    </responsive-container>

    <!-- 响应式布局演示 -->
    <responsive-container>
      <view class="demo-section">
        <view class="section-title">响应式布局</view>
        <view class="layout-demo">
          <view class="sidebar d-xs-none d-sm-none d-md-block">
            <view class="sidebar-content">侧边栏 (中屏及以上显示)</view>
          </view>
          <view class="main-content">
            <view class="content-card">
              <view class="card-title">主要内容</view>
              <view class="card-body">
                这是主要内容区域，在小屏幕上占满宽度，在大屏幕上与侧边栏并排显示。
              </view>
            </view>
          </view>
        </view>
      </view>
    </responsive-container>
  </view>
</template>

<script>
import { responsiveMixin } from '@/common/js/responsive.js'

export default {
  name: 'responsive-demo',
  mixins: [responsiveMixin],
  data() {
    return {
      
    }
  },
  onLoad() {
    console.log('响应式演示页面加载');
  }
}
</script>

<style scoped>
.responsive-demo {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.demo-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #e0e0e0;
}

.info-card {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: bold;
  color: #666;
}

.value {
  color: #007aff;
  font-weight: bold;
}

.demo-grid {
  gap: 20rpx;
}

.grid-item {
  background: white;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.item-content {
  padding: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.media-demo {
  padding: 30rpx;
  margin: 20rpx 0;
  border-radius: 8rpx;
  text-align: center;
  font-weight: bold;
  color: white;
}

.media-demo.xs {
  background: #ff6b6b;
}

.media-demo.sm {
  background: #4ecdc4;
}

.media-demo.md {
  background: #45b7d1;
}

.media-demo.lg {
  background: #96ceb4;
}

.media-demo.xl {
  background: #feca57;
}

.text-demo {
  background: white;
  padding: 40rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.layout-demo {
  display: flex;
  gap: 20rpx;
  min-height: 400rpx;
}

.sidebar {
  flex: 0 0 300rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.sidebar-content {
  padding: 30rpx;
  text-align: center;
  font-weight: bold;
  color: #666;
}

.main-content {
  flex: 1;
}

.content-card {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  height: 100%;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.card-body {
  color: #666;
  line-height: 1.6;
}

/* 大屏优化 */
@media screen and (min-width: 1024rpx) {
  .section-title {
    font-size: 36rpx;
  }
  
  .info-card,
  .content-card {
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  }
  
  .grid-item:hover .item-content {
    transform: scale(1.05);
    transition: transform 0.3s ease;
  }
}
</style>
