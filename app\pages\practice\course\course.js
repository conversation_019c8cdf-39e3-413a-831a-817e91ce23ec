import {
	get
} from "@/common/js/http.js";
let app = getApp();
let that = null;

export default {
	data() {
		return {
			isAllScreen: app.globalData.isAllScreen,
			listData: [],
			isLoad: false,
		};
	},
	onLoad(o) {
		that = this;
		that.getCourse();
	},
	methods: {
		getCourse() {
			let currenProfession = app.globalData.config.storage.getCurrentProfessionData();
			app.globalData.server
				.getRequest('v2/getCourses', {
					professions_id: currenProfession.id
				})
				.then((res) => {
					that.listData = res.data;
					that.isLoad = true;
				})
				.catch((e) => {
					app.showToast('获取课程列表失败');
				});
		},
		editTap(e) {
			let o = e.currentTarget.dataset.item;
			console.log(e);
			app.globalData.server
				.postRequest('v2/joinCourses', {
					course_id: o.id,
					join: o.joined
				})
				.then((e) => {
					that.getCourse();
				})
				.catch((e) => {
					console.log(e);
					app.showToast('编辑失败');
				});
		},
		async courseTap(options) {
			const { index, child } = options.currentTarget.dataset;
			const course = that.listData[index].courses[child];

			uni.navigateTo({
				url: `../course/detail?title=${course.name}&id=${course.id}&profession_course_id=${course.profession_course_id}`
			});
		},
	}
}; 