<block wx:if="{{load}}"><view><back vue-id="532412b9-1" showBackText="{{false}}" customClass="bg-gradual-red text-white" title="成交订单" bind:__l="__l"></back><block wx:if="{{$root.g0>0}}"><view class="cu-list sm-border menu"><block wx:for="{{listData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cu-item arrow" style="margin-top:10rpx;" data-id="{{item.id}}" data-event-opts="{{[['tap',[['itemTap',['$event']]]]]}}" bindtap="__e"><view class="content" style="padding:3rpx 5rpx 3rpx 5rpx;margin:20rpx 0 20rpx 0;"><view class="text-cut"><text class="cuIcon-infofill text-blue margin-right-xs"></text><text class="text-df">{{"订单编号："+item.order_sn+''}}</text><text class="text-sm text-gray">{{"("+item.relation_level_name+")"}}</text></view><view class="flex text-df"><view class="basis-df"><text class="cuIcon-rechargefill text-red margin-right-xs"></text>金额：<text class="text-red">{{item.order_amount}}</text></view><view class="basis-df"><text class="cuIcon-sponsorfill text-red margin-right-xs"></text>分红：<text class="text-red">{{item.bonus_amount}}</text><text class="text-sm text-gray">{{"("+item.bonus_rate_name+")"}}</text></view></view><view class="flex text-df"><view class="basis-df"><text class="cuIcon-tagfill text-blue margin-right-xs"></text>状态：<text>{{item.is_settlement==1?'已结算':'未结算'}}</text></view><view class="basis-df"><text class="cuIcon-timefill text-blue margin-right-xs"></text>时间：<text>{{item.add_date}}</text></view></view><view class="flex text-df"><view><text class="cuIcon-tagfill text-blue margin-right-xs"></text>结算单：<text>{{item.is_build_settlement_order==1?'已生成':'未生成'}}</text></view></view></view></view></block></view></block><block wx:if="{{$root.g1==0}}"><empty vue-id="532412b9-2" info="暂时没有订单" bind:__l="__l"></empty></block></view></block>