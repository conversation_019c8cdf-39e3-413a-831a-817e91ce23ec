
.responsive-container.data-v-65a324f8 {
  width: 100%;
  box-sizing: border-box;
}
.responsive-container--center.data-v-65a324f8 {
  margin: 0 auto;
}

/* 小屏幕 */
@media screen and (max-width: 768rpx) {
.responsive-container.data-v-65a324f8 {
    padding: 20rpx !important;
}
}

/* 中等屏幕 */
@media screen and (min-width: 769rpx) and (max-width: 1024rpx) {
.responsive-container.data-v-65a324f8 {
    padding: 40rpx !important;
}
}

/* 大屏幕 */
@media screen and (min-width: 1025rpx) {
.responsive-container.data-v-65a324f8 {
    padding: 60rpx !important;
    max-width: 1200px;
    margin: 0 auto;
}
}

/* 超大屏幕 */
@media screen and (min-width: 1440rpx) {
.responsive-container.data-v-65a324f8 {
    max-width: 1400px;
    padding: 80rpx !important;
}
}

