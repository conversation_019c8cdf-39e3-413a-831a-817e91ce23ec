<view><back vue-id="517f8be4-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="会员登录" bind:__l="__l"></back><view><scroll-view class="bg-white nav text-center" scroll-x="{{true}}"><block wx:for="{{selectModel}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.show}}"><view class="{{['cu-item',index==selectModelIndex?'text-blue cur':'']}}" data-id="{{index}}" data-event-opts="{{[['tap',[['selectModelTap',['$event']]]]]}}" bindtap="__e"><text class="{{[item.icon]}}"></text>{{''+item.name+''}}</view></block></block></scroll-view><block wx:if="{{selectModelIndex==1}}"><view><view class="cu-form-group margin-top"><view class="title">账户</view><input maxlength="32" placeholder="手机号码|邮箱地址" placeholderStyle="color:#999;" type="text" data-event-opts="{{[['input',[['usernameInputTap',['$event']]]]]}}" bindinput="__e"/></view><view class="cu-form-group margin-top"><view class="title">密码</view><input maxlength="20" password="{{true}}" placeholder="账号登录密码" placeholderStyle="color:#999;" type="text" data-event-opts="{{[['input',[['passwordInputTap',['$event']]]]]}}" bindinput="__e"/></view><block wx:if="{{appPlatform==30}}"><view class="cu-form-group margin-top" style="justify-content:space-evenly;"><switch class="{{['red','text-df',isAgreement?'checked':'']}}" checked="{{isAgreement?true:false}}" color="#e54d42" data-event-opts="{{[['change',[['agreementTap',['$event']]]]]}}" bindchange="__e"></switch><view style="margin-left:0;"><text>我已阅读并同意</text><text class="text-bold"><navigator href="https://admin.5b1.cn/index/agreement/index" target="_blank" class="_a">用户协议</navigator></text>、<text class="text-bold"><navigator href="https://admin.5b1.cn/index/agreement/privacy" target="_blank" class="_a">隐私政策</navigator></text></view></view></block><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['loginTap',['$event']]]]]}}" class="cu-btn bg-blue lg" bindtap="__e">登录</button></view><block wx:if="{{!checkAppIsAudit}}"><view class="padding flex flex-direction" style="padding-top:0rpx;"><view class="flex" style="text-align:center;"><view class="flex-sub bg-blue padding-sm margin-xs radius" style="margin-left:-3rpx;"><navigator hoverClass="none" url="../register/register">注册账号</navigator></view><view class="flex-sub bg-blue padding-sm margin-xs radius" style="margin-right:-3rpx;"><navigator hoverClass="none" url="../register/forget">找回密码</navigator></view></view></view></block></view></block><block wx:if="{{selectModelIndex==0&&login_page_set!=null}}"><view class="margin-top"><view class="padding-xs flex align-center"><view class="flex-sub text-center"><view class="text-lg padding"><text class="text-black">申请获取以下权限</text></view><view class="padding"><text class="text-grey">获取您的用户身份</text></view></view></view><block wx:if="{{login_page_set.open_mcp_mobile_login!=1}}"><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['wxloginTap',['$event']]]]]}}" class="cu-btn bg-blue lg" bindtap="__e">授权登录</button></view></block><block wx:if="{{login_page_set.open_mcp_mobile_login==1}}"><view class="padding flex flex-direction"><button class="cu-btn bg-blue lg" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['wxPhoneloginTap',['$event']]]]]}}" bindgetphonenumber="__e">手机号快捷登录</button></view></block></view></block></view></view>