import {
	post
} from "@/common/js/http.js";
let app = getApp();
let that = null;
export default {
	data() {
		return {
			reqId: null
		};
	},
	onLoad(options) {
		that = this;
		that.reqId = decodeURIComponent(options.scene);
	},
	methods: {
		confirmTap() {
			post('sso/confirmLoginQr', {
				reqId: that.reqId
			}).then(() => {
				app.showToast('登录成功');
				setTimeout(() => {
					uni.reLaunch({
						url: '../../user/user'
					});
				}, 1500);
			});
		}
	}
};