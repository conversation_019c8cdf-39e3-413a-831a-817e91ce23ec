let app = getApp();
let that = null;
export default {
	data() {
		return {
			title: '支付成功',
			checkAppIsAudit: false,
			id: 0
		};
	}
	/**
	 * 生命周期函数--监听页面加载
	 */
	,
	onLoad(options) {
		that = this;
		app.globalData.checkAppIsAuditAndRedirect();
		that.checkAppIsAudit = app.globalData.checkAppIsAudit();
		
		// Get order ID from options
		if (options && options.id) {
			that.id = options.id;
		}
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {},
	methods: {
		/**
		 * 按钮点击事件
		 * @param {*} options
		 */
		onLinkClick(options) {
			console.log(options);
			let id = options.currentTarget.dataset.id;
			if (id == 1) {
				uni.reLaunch({
					url: '/pages/index/index'
				});
			} else {
				uni.reLaunch({
					url: '/pages/user/user'
				});
			}
		}
	}
};