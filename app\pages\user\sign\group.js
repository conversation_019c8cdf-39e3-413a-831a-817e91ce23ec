let that = null,
	app = getApp(),
	cache = app.globalData.config.storage,
	service = app.globalData.service;
export default {
	data() {
		return {
			isLoad: false,
			checkAppIsAudit: false,
			wxFromData: {},
		};
	},
	onLoad() {
		that = this;
		app.globalData.showShareMenu();
		that.init();
	},
	onShow() {

	},
	methods: {
		init() {
			that.checkAppIsAudit = app.globalData.checkAppIsAudit();
			uni.getGroupEnterInfo({
				success(res) {
					that.wxFromData = res;
					that.isLoad = true;
				},
				fail() {
					app.showToast('获取微信群信息失败');
				}
			})
		},
		async submitTap() {
			uni.login({
				success(res) {
					let data = that.wxFromData;
					app.globalData.server
						.postRequest('user/sign/wxGroupWelfare', {
							code: res.code,
							iv: that.wxFromData.iv,
							encryptedData: that.wxFromData.encryptedData
						})
						.then((res) => {
							that.wxFromData.iv = res.data.wxGroupId;
							app.showToast("领取成功");
						})
						.catch((t) => {
							app.showToast('微信登录异常');
						});
				}
			});
		}
	}
};