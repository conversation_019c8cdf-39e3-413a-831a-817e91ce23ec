import {
	post
} from "@/common/js/http.js";
let that = null;
let app = getApp();
export default {
	data() {
		return {
			username: '',
			password: '',
			confirmPassword: '',
			verifyCode: '',
			checkAppIsAudit: true,
			showConfirm: false,
			appPlatform: null,
			isAgreement: false,
			countDown: '发送验证码',
		};
	},
	onLoad() {
		that = this;
		that.appPlatform = app.globalData.appPlatform;
		that.checkAppIsAudit = app.globalData.checkAppIsAudit();
		app.globalData.checkAppIsAuditAndRedirect();
	},
	methods: {
		usernameInputTap(options) {
			that.username = options.detail.value;
		},
		passwordInputTap(options) {
			that.password = options.detail.value;
		},
		confirmPasswordInputTap(options) {
			that.confirmPassword = options.detail.value;
		},
		verifyCodeInputTap(options) {
			that.verifyCode = options.detail.value;
		},
		verifyTap() {
			try {
				if (that.isCountDowning === true) {
					app.showToast('请不要频繁操作');
					return;
				}
				that.verifyForm();
				that.showConfirm = true;
			} catch (exception) {
				app.showToast(exception.message);
			}
		},
		async safeCheckCompleteTap(options) {
			// 检查
			if (options.action != 1) {
				return;
			}
			if (that.isCountDowning === true) {
				that.showToast('请不要频繁操作');
				return;
			}

			// 倒计时
			let time = 60;
			let func = function() {
				that.isCountDowning = true;
				app.startInterval(() => {
					time--;
					that.countDown = `${time}s`;
					if (time <= 0) {
						app.stopInterval();
						that.countDown = '重新发送';
						that.isCountDowning = false;
					}
				}, 1000);
			};
			func();
		},
		verifyForm() {
			if (that.username == '') {
				throw new Error('请输入手机号码');
			}
			if (that.username.length < 5) {
				throw new Error('账号长度不足5位数');
			}
			if (that.password == '') {
				throw new Error('请输入登录密码');
			}
			if (that.password.length < 5) {
				throw new Error('密码长度不足5位数');
			}
			if (that.confirmPassword == '') {
				throw new Error('请输入确认密码');
			}
			if (that.confirmPassword.length < 5) {
				throw new Error('确认密码长度不足5位数');
			}
			if (that.password != that.confirmPassword) {
				throw new Error('登录密码和确认密码不一致');
			}
			if (that.appPlatform == 30 && !that.isAgreement) {
				throw new Error('请先阅读协议并勾选');
			}
		},
		async registerTap() {
			try {
				if (!that.verifyCode) {
					throw new Error('验证码不能为空');
				}
				that.verifyForm();
				let res = await post('user/register', {
					code: that.verifyCode,
					username: that.username,
					password: that.password
				});
				app.globalData.config.storage.setUserInfoData(res.data);
				app.globalData.config.storage.setUserTokenData(res.data.token);
				app.showToast('注册成功');
				app.globalData.checkLogin();
				uni.reLaunch({
					url: '../../index/city/city'
				});
			} catch (exception) {
				app.showToast(exception.message);
			}
		},
		agreementTap(e) {
			that.isAgreement = e.detail.value
		}
	},
};