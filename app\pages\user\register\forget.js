let app = getApp();
let that = null;
export default {
	data() {
		return {
			username: '',
			password: '',
			verifyCode: '',
			showConfirm: false,
			checkAppIsAudit: true,
			isCountDowning: false,
			countDown: '发送验证码'
		};
	},
	/**
	 * 初始化
	 */
	onLoad() {
		that = this;
		that.checkAppIsAudit = app.globalData.checkAppIsAudit();
	},
	methods: {
		/**
		 * 账号输入事件
		 */
		usernameInputTap(options) {
			that.setData({
				username: options.detail.value
			});
		},

		/**
		 * 密码输入事件
		 */
		passwordInputTap(options) {
			that.setData({
				password: options.detail.value
			});
		},

		/**
		 * 验证码输入事件
		 */
		verifyCodeInputTap(options) {
			that.setData({
				verifyCode: options.detail.value
			});
		},

		/**
		 * 发送验证码
		 */
		sendVerifyTap() {
			if (!that.username) {
				app.showToast('请输入手机号码');
				return;
			}
			if (!that.password) {
				app.showToast('请输入新的密码');
				return;
			}
			that.showConfirm = true;
		},

		async safeCheckCompleteTap(options) {
			// 检查
			if (options.action != 1) {
				return;
			}
			if (that.isCountDowning === true) {
				that.showToast('请不要频繁操作');
				return;
			}

			// 倒计时
			let time = 60;
			let func = function() {
				that.isCountDowning = true;
				app.startInterval(function() {
					time--;
					that.countDown = `${time}s`;
					if (time <= 0) {
						app.stopInterval();
						that.countDown = '重新发送';
						that.isCountDowning = false;
					}
				}, 1000);
			};
			func();
		},

		/**
		 * 确定按钮事件
		 */
		submitTap(options) {
			try {
				that.resetPassword();
			} catch (err) {
				app.showToast(err.message);
			}
		},

		/**
		 * 确定重置密码
		 */
		async resetPassword() {
			let data = {
				username: that.username,
				password: that.password,
				verifyCode: that.verifyCode
			};
			if (!data.username) {
				throw new Error('请输入手机号码');
			}
			if (!data.password) {
				throw new Error('请输入新的密码');
			}
			if (!data.verifyCode) {
				throw new Error('请输入短信验证码');
			}
			let res = await app.globalData.service.resetPassword(data);
			app.showToast(res.message);
			if (res.code == 1) {
				setTimeout(function() {
					uni.navigateBack({
						delta: 1
					});
				}, 1500);
			}
		}
	}
};