/* 响应式设计基础样式 */

/* 断点定义 */
:root {
  --breakpoint-xs: 576rpx;
  --breakpoint-sm: 768rpx;
  --breakpoint-md: 1024rpx;
  --breakpoint-lg: 1440rpx;
  --breakpoint-xl: 1920rpx;
}

/* 基础容器 */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 20rpx;
  box-sizing: border-box;
}

/* 响应式容器 */
.container-fluid {
  width: 100%;
  padding: 0 20rpx;
  box-sizing: border-box;
}

/* 行和列 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.col {
  flex: 1;
  padding: 0 10rpx;
  box-sizing: border-box;
}

/* 响应式显示/隐藏 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

/* 超小屏幕 (xs) < 576rpx */
@media screen and (max-width: 576rpx) {
  .container {
    max-width: 100%;
    padding: 0 15rpx;
  }
  
  .d-xs-none { display: none !important; }
  .d-xs-block { display: block !important; }
  .d-xs-flex { display: flex !important; }
  .d-xs-grid { display: grid !important; }
  
  /* 字体大小 */
  .text-xs-sm { font-size: 24rpx !important; }
  .text-xs-md { font-size: 28rpx !important; }
  .text-xs-lg { font-size: 32rpx !important; }
  
  /* 间距 */
  .p-xs-1 { padding: 10rpx !important; }
  .p-xs-2 { padding: 20rpx !important; }
  .p-xs-3 { padding: 30rpx !important; }
  .m-xs-1 { margin: 10rpx !important; }
  .m-xs-2 { margin: 20rpx !important; }
  .m-xs-3 { margin: 30rpx !important; }
}

/* 小屏幕 (sm) 576rpx - 768rpx */
@media screen and (min-width: 577rpx) and (max-width: 768rpx) {
  .container {
    max-width: 720rpx;
    padding: 0 20rpx;
  }
  
  .d-sm-none { display: none !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
  .d-sm-grid { display: grid !important; }
  
  /* 字体大小 */
  .text-sm-sm { font-size: 26rpx !important; }
  .text-sm-md { font-size: 30rpx !important; }
  .text-sm-lg { font-size: 34rpx !important; }
  
  /* 间距 */
  .p-sm-1 { padding: 15rpx !important; }
  .p-sm-2 { padding: 25rpx !important; }
  .p-sm-3 { padding: 35rpx !important; }
  .m-sm-1 { margin: 15rpx !important; }
  .m-sm-2 { margin: 25rpx !important; }
  .m-sm-3 { margin: 35rpx !important; }
}

/* 中等屏幕 (md) 768rpx - 1024rpx */
@media screen and (min-width: 769rpx) and (max-width: 1024rpx) {
  .container {
    max-width: 960rpx;
    padding: 0 30rpx;
  }
  
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
  .d-md-grid { display: grid !important; }
  
  /* 字体大小 */
  .text-md-sm { font-size: 28rpx !important; }
  .text-md-md { font-size: 32rpx !important; }
  .text-md-lg { font-size: 36rpx !important; }
  
  /* 间距 */
  .p-md-1 { padding: 20rpx !important; }
  .p-md-2 { padding: 30rpx !important; }
  .p-md-3 { padding: 40rpx !important; }
  .m-md-1 { margin: 20rpx !important; }
  .m-md-2 { margin: 30rpx !important; }
  .m-md-3 { margin: 40rpx !important; }
}

/* 大屏幕 (lg) 1024rpx - 1440rpx */
@media screen and (min-width: 1025rpx) and (max-width: 1440rpx) {
  .container {
    max-width: 1200rpx;
    padding: 0 40rpx;
  }
  
  .d-lg-none { display: none !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
  .d-lg-grid { display: grid !important; }
  
  /* 字体大小 */
  .text-lg-sm { font-size: 30rpx !important; }
  .text-lg-md { font-size: 34rpx !important; }
  .text-lg-lg { font-size: 38rpx !important; }
  
  /* 间距 */
  .p-lg-1 { padding: 25rpx !important; }
  .p-lg-2 { padding: 35rpx !important; }
  .p-lg-3 { padding: 45rpx !important; }
  .m-lg-1 { margin: 25rpx !important; }
  .m-lg-2 { margin: 35rpx !important; }
  .m-lg-3 { margin: 45rpx !important; }
}

/* 超大屏幕 (xl) > 1440rpx */
@media screen and (min-width: 1441rpx) {
  .container {
    max-width: 1400rpx;
    padding: 0 50rpx;
  }
  
  .d-xl-none { display: none !important; }
  .d-xl-block { display: block !important; }
  .d-xl-flex { display: flex !important; }
  .d-xl-grid { display: grid !important; }
  
  /* 字体大小 */
  .text-xl-sm { font-size: 32rpx !important; }
  .text-xl-md { font-size: 36rpx !important; }
  .text-xl-lg { font-size: 40rpx !important; }
  
  /* 间距 */
  .p-xl-1 { padding: 30rpx !important; }
  .p-xl-2 { padding: 40rpx !important; }
  .p-xl-3 { padding: 50rpx !important; }
  .m-xl-1 { margin: 30rpx !important; }
  .m-xl-2 { margin: 40rpx !important; }
  .m-xl-3 { margin: 50rpx !important; }
}

/* 网格系统 */
.grid {
  display: grid;
  gap: 20rpx;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* 响应式网格 */
@media screen and (max-width: 576rpx) {
  .grid-xs-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-xs-2 { grid-template-columns: repeat(2, 1fr); }
}

@media screen and (min-width: 577rpx) and (max-width: 768rpx) {
  .grid-sm-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-sm-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-sm-3 { grid-template-columns: repeat(3, 1fr); }
}

@media screen and (min-width: 769rpx) and (max-width: 1024rpx) {
  .grid-md-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-md-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-md-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-md-4 { grid-template-columns: repeat(4, 1fr); }
}

@media screen and (min-width: 1025rpx) and (max-width: 1440rpx) {
  .grid-lg-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-lg-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-lg-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-lg-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-lg-5 { grid-template-columns: repeat(5, 1fr); }
}

@media screen and (min-width: 1441rpx) {
  .grid-xl-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-xl-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-xl-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-xl-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-xl-5 { grid-template-columns: repeat(5, 1fr); }
  .grid-xl-6 { grid-template-columns: repeat(6, 1fr); }
}

/* 弹性布局 */
.flex-responsive {
  display: flex;
  flex-wrap: wrap;
}

.flex-responsive > * {
  flex: 1;
  min-width: 200rpx;
}

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* 响应式文本对齐 */
@media screen and (max-width: 576rpx) {
  .text-xs-left { text-align: left !important; }
  .text-xs-center { text-align: center !important; }
  .text-xs-right { text-align: right !important; }
}

@media screen and (min-width: 577rpx) {
  .text-sm-left { text-align: left !important; }
  .text-sm-center { text-align: center !important; }
  .text-sm-right { text-align: right !important; }
}

@media screen and (min-width: 769rpx) {
  .text-md-left { text-align: left !important; }
  .text-md-center { text-align: center !important; }
  .text-md-right { text-align: right !important; }
}

@media screen and (min-width: 1025rpx) {
  .text-lg-left { text-align: left !important; }
  .text-lg-center { text-align: center !important; }
  .text-lg-right { text-align: right !important; }
}

@media screen and (min-width: 1441rpx) {
  .text-xl-left { text-align: left !important; }
  .text-xl-center { text-align: center !important; }
  .text-xl-right { text-align: right !important; }
}
