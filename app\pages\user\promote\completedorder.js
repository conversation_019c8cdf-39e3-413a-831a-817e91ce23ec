let app = getApp(),
that = null,
helper = app.globalData.helper;
export default {
	data() {
		return {
			page: 1,
			settlement_order_id: 0,
			isFinish: false,
			load: false,
			listData: [],
		};
	},
	onLoad: function(options) {
		that = this;
		that.settlement_order_id = helper.variableDefalut(options.settlement_order_id, 0),
		that.getList();
	},
	onShow: function() {

	},
	onReachBottom: function() {
		that.page++;
		if (!that.isFinish) {
			that.getList();
		}
	},
	onShareAppMessage: function() {},
	methods: {
		getList() {
			app.globalData.server
				.getRequest('promote/completedOrder', {
					page: that.page,
					settlement_order_id:that.settlement_order_id
				})
				.then(function(res) {
					uni.stopPullDownRefresh();
					that.load = true;
					if (res.data.length > 0) {
						that.listData = that.listData.concat(res.data);
					} else {
						that.isFinish = true;
					}
				})
				.catch(function(a) {
					app.showToast('加载数据失败');
				});
		},
		itemTap(options){
			let id = options.currentTarget.dataset.id;
			let url = '/pages/user/promote/settlement?completed_order_id='+id;
			uni.navigateTo({
				url: url
			});
		}
	}
};