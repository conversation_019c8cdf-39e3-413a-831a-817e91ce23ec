module.exports = {
  // 基础设计宽度（以750rpx为标准）
  baseDesignWidth: 750,
  // 获取当前视口宽度
  getViewportWidth: function() {
    return uni.getSystemInfoSync().windowWidth;
  },
  // 计算响应式比例
  getResponsiveRatio: function() {
    var viewportWidth = this.getViewportWidth();
    // 当宽度大于基础设计宽度时开始缩小比例
    if (viewportWidth > this.baseDesignWidth) {
      return this.baseDesignWidth / viewportWidth;
    }
    return 1;
  },
  // 计算响应式尺寸
  calculateResponsiveSize: function(originalSize) {
    var ratio = this.getResponsiveRatio();
    return Math.round(originalSize * ratio);
  },
  // 获取响应式样式对象
  getResponsiveStyles: function(baseStyles) {
    var responsiveStyles = {};
    
    // 处理基础样式中的字体大小
    if (baseStyles.fontSize) {
      responsiveStyles.fontSize = this.calculateResponsiveSize(baseStyles.fontSize) + 'rpx';
    }
    
    // 处理基础样式中的高度
    if (baseStyles.height) {
      responsiveStyles.height = this.calculateResponsiveSize(baseStyles.height) + 'rpx';
    }
    
    // 处理基础样式中的内边距
    if (baseStyles.padding) {
      var paddingValues = baseStyles.padding.split(' ').map(function(value) {
        return this.calculateResponsiveSize(parseInt(value)) + 'rpx';
      }.bind(this));
      responsiveStyles.padding = paddingValues.join(' ');
    }
    
    // 处理基础样式中的外边距
    if (baseStyles.margin) {
      var marginValues = baseStyles.margin.split(' ').map(function(value) {
        return this.calculateResponsiveSize(parseInt(value)) + 'rpx';
      }.bind(this));
      responsiveStyles.margin = marginValues.join(' ');
    }
    
    return responsiveStyles;
  }
};