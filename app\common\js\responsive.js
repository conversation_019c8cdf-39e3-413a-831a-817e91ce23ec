/**
 * 响应式工具类
 */

// 断点配置
export const breakpoints = {
  xs: 576,
  sm: 768,
  md: 1024,
  lg: 1440,
  xl: 1920
};

// 获取当前屏幕尺寸类型
export function getCurrentBreakpoint() {
  return new Promise((resolve) => {
    uni.getSystemInfo({
      success: (res) => {
        const width = res.windowWidth;
        let breakpoint = 'xs';
        
        if (width >= breakpoints.xl) {
          breakpoint = 'xl';
        } else if (width >= breakpoints.lg) {
          breakpoint = 'lg';
        } else if (width >= breakpoints.md) {
          breakpoint = 'md';
        } else if (width >= breakpoints.sm) {
          breakpoint = 'sm';
        }
        
        resolve({
          breakpoint,
          width,
          height: res.windowHeight,
          isLargeScreen: width >= breakpoints.md
        });
      }
    });
  });
}

// 判断是否为大屏设备
export function isLargeScreen() {
  return new Promise((resolve) => {
    getCurrentBreakpoint().then(info => {
      resolve(info.isLargeScreen);
    });
  });
}

// 响应式混入
export const responsiveMixin = {
  data() {
    return {
      screenInfo: {
        breakpoint: 'xs',
        width: 375,
        height: 667,
        isLargeScreen: false
      }
    };
  },
  
  computed: {
    // 是否为小屏
    isXs() {
      return this.screenInfo.breakpoint === 'xs';
    },
    // 是否为小屏
    isSm() {
      return this.screenInfo.breakpoint === 'sm';
    },
    // 是否为中屏
    isMd() {
      return this.screenInfo.breakpoint === 'md';
    },
    // 是否为大屏
    isLg() {
      return this.screenInfo.breakpoint === 'lg';
    },
    // 是否为超大屏
    isXl() {
      return this.screenInfo.breakpoint === 'xl';
    },
    // 是否为大屏设备（md及以上）
    isLargeDevice() {
      return this.screenInfo.isLargeScreen;
    },
    // 响应式CSS类
    responsiveClass() {
      return {
        'screen-xs': this.isXs,
        'screen-sm': this.isSm,
        'screen-md': this.isMd,
        'screen-lg': this.isLg,
        'screen-xl': this.isXl,
        'large-screen': this.isLargeDevice
      };
    }
  },
  
  mounted() {
    this.updateScreenInfo();
    // 监听屏幕变化（主要用于H5）
    // #ifdef H5
    window.addEventListener('resize', this.handleResize);
    // #endif
  },
  
  beforeDestroy() {
    // #ifdef H5
    window.removeEventListener('resize', this.handleResize);
    // #endif
  },
  
  methods: {
    // 更新屏幕信息
    async updateScreenInfo() {
      const info = await getCurrentBreakpoint();
      this.screenInfo = info;
    },
    
    // 处理屏幕尺寸变化
    handleResize() {
      clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        this.updateScreenInfo();
      }, 100);
    },
    
    // 根据屏幕尺寸获取不同的值
    getResponsiveValue(values) {
      const { breakpoint } = this.screenInfo;
      
      if (typeof values === 'object' && values !== null) {
        return values[breakpoint] || values.xs || values.default;
      }
      
      return values;
    },
    
    // 根据屏幕尺寸执行不同的函数
    executeResponsive(functions) {
      const { breakpoint } = this.screenInfo;
      const fn = functions[breakpoint] || functions.default;
      
      if (typeof fn === 'function') {
        return fn();
      }
    }
  }
};

// 响应式布局配置
export const layoutConfig = {
  // 容器最大宽度
  containerMaxWidth: {
    xs: '100%',
    sm: '720rpx',
    md: '960rpx', 
    lg: '1200rpx',
    xl: '1400rpx'
  },
  
  // 网格列数
  gridCols: {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5
  },
  
  // 间距
  spacing: {
    xs: '15rpx',
    sm: '20rpx',
    md: '30rpx',
    lg: '40rpx',
    xl: '50rpx'
  },
  
  // 字体大小
  fontSize: {
    xs: {
      small: '24rpx',
      medium: '28rpx',
      large: '32rpx'
    },
    sm: {
      small: '26rpx',
      medium: '30rpx',
      large: '34rpx'
    },
    md: {
      small: '28rpx',
      medium: '32rpx',
      large: '36rpx'
    },
    lg: {
      small: '30rpx',
      medium: '34rpx',
      large: '38rpx'
    },
    xl: {
      small: '32rpx',
      medium: '36rpx',
      large: '40rpx'
    }
  }
};

// 创建媒体查询观察器（微信小程序）
export function createMediaQueryObserver(component, options = {}) {
  // #ifdef MP-WEIXIN
  if (uni.createMediaQueryObserver) {
    const observer = uni.createMediaQueryObserver(component);
    
    const defaultOptions = {
      minWidth: 0,
      maxWidth: 9999
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    return observer.observe(finalOptions, (matches) => {
      if (component.onMediaQueryChange) {
        component.onMediaQueryChange(matches);
      }
    });
  }
  // #endif
  
  return null;
}

// 工具函数：转换rpx到px
export function rpxToPx(rpx) {
  return new Promise((resolve) => {
    uni.getSystemInfo({
      success: (res) => {
        const screenWidth = res.screenWidth;
        const px = (rpx / 750) * screenWidth;
        resolve(px);
      }
    });
  });
}

// 工具函数：转换px到rpx
export function pxToRpx(px) {
  return new Promise((resolve) => {
    uni.getSystemInfo({
      success: (res) => {
        const screenWidth = res.screenWidth;
        const rpx = (px / screenWidth) * 750;
        resolve(rpx);
      }
    });
  });
}

export default {
  breakpoints,
  getCurrentBreakpoint,
  isLargeScreen,
  responsiveMixin,
  layoutConfig,
  createMediaQueryObserver,
  rpxToPx,
  pxToRpx
};
