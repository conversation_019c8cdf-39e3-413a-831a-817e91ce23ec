let app = getApp();
let that = null;
let config = app.globalData.config;
import {
	post
} from "@/common/js/http.js";
export default {
	data() {
		return {
			id: 0,
			title: '',
			course_id: 0,
			isLoad: false,
			cateList: []
		};
	},
	onLoad(options) {
		that = this;
		const {
			title = '',
				course_id = 0,
				category_id = 0
		} = options || {};
		that.title = title;
		that.course_id = course_id;
		that.getCateList(course_id, category_id);
	},
	methods: {
		async getCateList(course_id, category_id) {
			const res = await post('paper/category/list', {
				course_id: course_id,
				pid: category_id
			});
			that.cateList = res.data;
			that.isLoad = true;
		},
		async chapterTap(key) {
			let cate = that.cateList[key];
			if (!Object.prototype.hasOwnProperty.call(cate, 'childs')) {
				const res = await post('paper/category/list', {
					course_id: that.course_id,
					pid: cate.id
				});
				cate.isOpen = true;
				cate.childs = res.data;
				this.$set(that.cateList, key, cate);
			} else {
				cate.isOpen = !cate.isOpen;
				this.$set(that.cateList, key, cate);
			}
		},
		sectionTap(item) {
			if (item.paper_num === 0) {
				uni.showToast({
					title: '该分类未上传试卷',
					icon: 'none'
				});
				return;
			}
			
			const url = '/pages/practice/paper/paper?title=' + item.name + '&category_id=' + item.id + '&course_id=' + (item.course_id || that.course_id);
			uni.navigateTo({
				url: url
			});
		}
	}
};