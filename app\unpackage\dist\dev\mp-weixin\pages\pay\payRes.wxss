/* 支付成功页面样式 */
.success-container {
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  margin: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.success-icon-box {
  text-align: center;
  margin-bottom: 20rpx;
}
.success-icon {
  font-size: 120rpx;
  -webkit-animation: pulse 1.5s ease-in-out;
          animation: pulse 1.5s ease-in-out;
}
.order-card {
  margin: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
}
/* 动画效果 */
@-webkit-keyframes pulse {
0% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0;
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
@keyframes pulse {
0% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0;
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
