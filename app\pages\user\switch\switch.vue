<template>
	<view v-if="load">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="切换版本"></back>
		<view class="cu-bar bg-white solid-bottom">
			<view class="action">
				<text class="cuIcon-titles text-orange "></text> 版本列表
			</view>
		</view>
		<view class="cu-list menu">
			<view class="cu-item arrow">
				<view class="content" @tap="copyTap(1)" >
					<text class="cuIcon-discoverfill text-blue"></text>
					<text class="text-blue">网页版刷题（复制网址）</text>
				</view>
			</view>
			<view class="cu-item arrow">
				<view class="content" @tap="copyTap(2)">
					<text class="cuIcon-discoverfill text-blue"></text>
					<text class="text-blue">安卓APP版（下载地址）</text>
				</view>
			</view>
			<view class="cu-item arrow">
				<view class="content">
					<text class="cuIcon-discoverfill text-gray"></text>
					<text class="text-gray">苹果APP版（即将上线）</text>
				</view>
			</view>
		</view>
		<view class="cu-modal" :class="showNotice ? 'show':''">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">提示</view>
					<view class="action" @tap="hideNoticeTap">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding-xl">
					{{showNoticeText}}
				</view>
				<view class="cu-bar bg-white">
					<button class="action  margin flex-sub text-green" @tap="hideNoticeTap">
						<text class="cuIcon-check"></text>知道啦
					</button>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	let that = null;
	let app = getApp();
	export default {
		data() {
			return {
				load: false,
				showNotice: false,
				showNoticeText:'',
				data: [],
				appIsAudit: false
			};
		},
		onLoad: function() {
			that = this;
			app.globalData.checkAppIsAuditAndRedirect();
			that.getInfo();
		},
		onShow: function() {},
		methods: {
			getInfo: function() {
				app.globalData.server
					.getRequest('user/switch', {})
					.then(function(e) {
						that.data = e.data;
						that.load = true;
					})
					.catch(function(e) {
						app.showToast('获取信息失败');
						console.log(e);
					});
			},
			copyTap: function(type) {
				let text = type==1 ? that.data.h5:that.data.android;
				let showNoticeText = type==1 ? '网址复制成功，您可以粘贴到手机浏览器进行访问，再通过浏览器添加到桌面，即可享受APP版本纯净体验':'安卓版下载地址已经复制,您可以粘贴到手机浏览器进行下载'
				uni.setClipboardData({
					data: text.toString(),
					fail: function() {
						app.showToast('网址复制失败');
					},
					success: function() {
						that.showNoticeText = showNoticeText; 
						that.showNotice = true;
					}
				});
			},
			hideNoticeTap(){
				that.showNotice = false;
			}
		}
	};
</script>

<style src="./switch.css"></style>
