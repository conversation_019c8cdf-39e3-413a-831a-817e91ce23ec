<block wx:if="{{isLoad}}"><view><back vue-id="7fd67fc8-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="{{info.name}}" bind:__l="__l"></back><view class="flex flex-direction" style="min-height:100vh;"><view class="flex-sub" style="margin-bottom:270rpx;"><block><view class="cu-bar bg-white margin-top-xs solid-bottom" style="margin-buttom:3rpx;"><view class="action sub-title"><text class="text-xl text-bold text-blue text-shadow">学习概况</text><text class="text-ABC text-blue">OVERVIEW</text></view></view><view class="cu-card case bg-white margin-bottom-sm"><view class="cu-item shadow padding-sm"><view class="grid col-4"><view class="padding-xs"><view class="text-center"><view class="text-xl text-green text-bold">{{today_question_count}}</view><view class="text-sm text-gray">今日做题</view></view></view><view class="padding-xs"><view class="text-center"><view class="text-xl text-purple text-bold">{{total_question_count}}</view><view class="text-sm text-gray">总答题数</view></view></view><view class="padding-xs"><view class="text-center"><view class="text-xl text-blue text-bold">{{correct_rate+"%"}}</view><view class="text-sm text-gray">答题正确率</view></view></view><view class="padding-xs"><view class="text-center"><view class="text-xl text-red text-bold">{{exam_countdown}}</view><view class="text-sm text-gray">考试倒计时</view></view></view></view></view></view></block><view class="cu-bar bg-white margin-top-xs solid-bottom" style="margin-buttom:3rpx;"><view class="action sub-title"><text class="text-xl text-bold text-blue text-shadow">题库信息</text><text class="text-ABC text-blue">INFORMATION</text></view><view class="action"><button data-event-opts="{{[['tap',[['onCollect',['$event']]]]]}}" class="cu-btn round bg-blue shadow" bindtap="__e"><text class="cuIcon-favor margin-right-xs"></text>{{(info.collect==1?'已收藏':'收藏')+''}}</button></view></view><view class="cu-list menu"><view class="cu-item" style="font-size:32rpx;"><view class="content"><text class="cuIcon-infofill text-blue"></text><text class="text-black">题库名称</text></view><view class="action"><view class="cu-tag round bg-green light">{{info.name}}</view></view></view><block wx:if="{{info.id&&detail_page_set.show_course_code>0}}"><view class="cu-item" style="font-size:32rpx;"><view class="content"><text class="cuIcon-cuIcon text-blue"></text><text class="text-black">题库编码</text></view><view class="action"><view class="cu-tag round bg-green light">{{info.code?info.code:info.id}}</view></view></view></block><block wx:if="{{info.category_name}}"><view class="cu-item" style="font-size:32rpx;"><view class="content"><text class="cuIcon-group_fill text-blue"></text><text class="text-black">题库分类</text></view><view class="action"><view class="cu-tag round bg-green light">{{info.category_name}}</view></view></view></block><block wx:if="{{info.desc}}"><view class="cu-item" style="font-size:32rpx;"><view class="content"><text class="cuIcon-formfill text-blue"></text><text class="text-black">题库描述</text></view><view class="action"><view class="cu-tag round bg-green light">{{info.desc}}</view></view></view></block></view><view class="cu-bar bg-white margin-top-xs solid-bottom" style="margin-buttom:3rpx;"><view class="action sub-title"><text class="text-xl text-bold text-blue text-shadow">题库练习</text><text class="text-ABC text-blue">PRACTICE</text></view><view class="action"><button class="cu-btn round bg-green shadow" open-type="share"><text class="cuIcon-forwardfill margin-right-xs"></text>分享题库</button></view></view><view class="cu-list grid col-2"><view data-event-opts="{{[['tap',[['onOrderPractice',['$event']]]]]}}" class="cu-item menu-item" style="flex-direction:row-reverse;" bindtap="__e"><view class="text-blue fa-icon fa-sort-alpha-asc" style="width:100%;margin-top:28rpx;font-size:32rpx;"></view><text class="text-bold text-xl" style="width:100%;margin-top:28rpx;color:black;font-size:28rpx;margin-left:8rpx;">顺序练习</text></view><view data-event-opts="{{[['tap',[['onQuestionTypePractice',['$event']]]]]}}" class="cu-item menu-item" style="flex-direction:row-reverse;" bindtap="__e"><view class="text-blue cuIcon-filter"></view><text class="text-bold text-xl" style="width:100%;margin-top:28rpx;color:black;font-size:28rpx;margin-left:8rpx;">题型练习</text></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.g0}}"><view data-event-opts="{{[['tap',[['onCategory',['$0'],[[['categoryList','',index]]]]]]]}}" class="cu-item menu-item" style="flex-direction:row-reverse;" bindtap="__e"><view class="{{['text-blue',item.$orig.icon]}}"></view><text class="text-bold text-xl" style="width:100%;margin-top:28rpx;color:black;font-size:28rpx;margin-left:8rpx;">{{item.$orig.name}}</text></view></block></block><view data-event-opts="{{[['tap',[['onSearchQuestion',['$event']]]]]}}" class="cu-item menu-item" style="flex-direction:row-reverse;" bindtap="__e"><view class="text-blue cuIcon-searchlist"></view><text class="text-bold text-xl" style="width:100%;margin-top:28rpx;color:black;font-size:28rpx;margin-left:8rpx;">搜索试题</text></view><view data-event-opts="{{[['tap',[['onErrorQuestion',['$event']]]]]}}" class="cu-item menu-item" style="flex-direction:row-reverse;" bindtap="__e"><view class="text-blue cuIcon-roundclose"></view><text class="text-bold text-xl" style="width:100%;margin-top:28rpx;color:black;font-size:28rpx;margin-left:8rpx;">我的错题</text></view><view data-event-opts="{{[['tap',[['onColl',['$event']]]]]}}" class="cu-item menu-item" style="flex-direction:row-reverse;" bindtap="__e"><view class="text-blue cuIcon-favor"></view><text class="text-bold text-xl" style="width:100%;margin-top:28rpx;color:black;font-size:28rpx;margin-left:8rpx;">我的收藏</text></view></view></view><view class="bottom-layout cu-bar bg-white tabbar border shop"><block wx:if="{{!checkAppIsAudit}}"><view data-event-opts="{{[['tap',[['onNotSupport',['$event']]]]]}}" class="action text-gray" style="display:none;" bindtap="__e"><view class="cuIcon-lock"></view><text class="text-sm text-gray">考前押密</text></view></block><block wx:if="{{$root.g1}}"><view data-event-opts="{{[['tap',[['onOnlineCourse',['$event']]]]]}}" class="action text-blue" bindtap="__e"><view class="cuIcon-video"></view><text class="text-sm text-blue">在线网课</text></view></block><view class="action text-blue" data-type="5" data-event-opts="{{[['tap',[['onLearn',['$event']]]]]}}" bindtap="__e"><view class="cuIcon-down"></view><text class="text-sm text-blue">资料下载</text></view><block wx:if="{{!checkAppIsAudit&&!isIosVirtualPay&&!info.isCourseVip}}"><view data-event-opts="{{[['tap',[['onBuyCourse',['$event']]]]]}}" class="action text-blue" bindtap="__e"><view class="cuIcon-pay"></view><text class="text-sm">开通题库</text></view></block><block wx:if="{{!checkAppIsAudit&&!isIosVirtualPay&&!info.isCourseVip}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="action text-blue" bindtap="__e"><view class="cuIcon-unlock"></view><text class="text-sm text-blue">激活题库</text></view></block></view></view><activate-modal vue-id="7fd67fc8-2" show="{{showActivateModal}}" title="激活题库" business-id="{{info.id}}" business-type="2" data-event-opts="{{[['^updateShow',[['__set_sync',['$0','showActivateModal','$event'],['']]]],['^showService',[['showOnlineServiceTap']]],['^success',[['onActivateSuccess']]]]}}" bind:updateShow="__e" bind:showService="__e" bind:success="__e" bind:__l="__l"></activate-modal></view></block>