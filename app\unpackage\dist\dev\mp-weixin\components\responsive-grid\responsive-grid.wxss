
.responsive-grid.data-v-3f4970f8 {
  display: grid;
  width: 100%;
  box-sizing: border-box;
}

/* 超小屏幕 (xs) */
@media screen and (max-width: 576rpx) {
.responsive-grid.data-v-3f4970f8 {
    grid-template-columns: repeat(1, 1fr);
}
}

/* 小屏幕 (sm) */
@media screen and (min-width: 577rpx) and (max-width: 768rpx) {
.responsive-grid.data-v-3f4970f8 {
    grid-template-columns: repeat(2, 1fr);
}
}

/* 中等屏幕 (md) */
@media screen and (min-width: 769rpx) and (max-width: 1024rpx) {
.responsive-grid.data-v-3f4970f8 {
    grid-template-columns: repeat(3, 1fr);
}
}

/* 大屏幕 (lg) */
@media screen and (min-width: 1025rpx) and (max-width: 1440rpx) {
.responsive-grid.data-v-3f4970f8 {
    grid-template-columns: repeat(4, 1fr);
}
}

/* 超大屏幕 (xl) */
@media screen and (min-width: 1441rpx) {
.responsive-grid.data-v-3f4970f8 {
    grid-template-columns: repeat(5, 1fr);
}
}

/* 自定义列数类 */
.responsive-grid.cols-1.data-v-3f4970f8 { grid-template-columns: repeat(1, 1fr);
}
.responsive-grid.cols-2.data-v-3f4970f8 { grid-template-columns: repeat(2, 1fr);
}
.responsive-grid.cols-3.data-v-3f4970f8 { grid-template-columns: repeat(3, 1fr);
}
.responsive-grid.cols-4.data-v-3f4970f8 { grid-template-columns: repeat(4, 1fr);
}
.responsive-grid.cols-5.data-v-3f4970f8 { grid-template-columns: repeat(5, 1fr);
}
.responsive-grid.cols-6.data-v-3f4970f8 { grid-template-columns: repeat(6, 1fr);
}

