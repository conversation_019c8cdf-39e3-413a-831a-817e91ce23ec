
button:after {
	border: none;
}
.content-wrapper {
	padding: 30rpx;
}
.avatar-section {
	display: flex;
	justify-content: center;
	padding: 40rpx 0;
	background: linear-gradient(45deg, #0081ff, #1cbbb4);
	border-radius: 0 0 50rpx 50rpx;
	margin-bottom: 40rpx;
}
.avatar-button {
	background: transparent;
	padding: 0;
	width: auto;
	height: auto;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.cu-avatar.xl {
	width: 160rpx;
	height: 160rpx;
	border: 6rpx solid rgba(255, 255, 255, 0.7);
	box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
}
.avatar-hint {
	color: #ffffff;
	font-size: 24rpx;
	margin-top: 20rpx;
	opacity: 0.9;
}
.form-section {
	margin: 30rpx 0 60rpx;
}
.cu-form-group {
	background-color: #ffffff;
	padding: 30rpx;
	margin-bottom: 30rpx;
	border-radius: 12rpx;
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
}
.cu-form-group .title {
	font-weight: bold;
}
.button-section {
	padding: 20rpx 0 40rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	gap: 30rpx;
}
.cu-btn {
	margin-bottom: 0;
	height: 90rpx;
	font-size: 32rpx;
	font-weight: bold;
	letter-spacing: 2rpx;
	line-height: 90rpx;
	min-width: 220rpx;
	box-sizing: border-box;
}
.cu-btn.bg-gradual-blue,
.cu-btn.bg-grey {
	box-shadow: 0 10rpx 20rpx rgba(0, 129, 255, 0.2);
}

