<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="请选择学校"></back>
		<view v-if="load">
			<view class="cu-bar bg-white search fixed" :style="'top:' + CustomBar + 'px;'">
				<view class="search-form ">
					<text class="cuIcon-search"></text>
					<input type="text" placeholder="输入学校名称进行搜索" @input="inputTap" @confirm="searchTap"
						confirm-type="search" />
				</view>
				<view class="action" style="display: none;">
					<button @tap="searchCityTap" class="cu-btn bg-gradual-blue shadow-blur round">搜索</button>
				</view>
				<view class="action text-blue" @tap="cityTap">
					<text>{{currentCity.name}}</text>
					<text class="cuIcon-triangledownfill"></text>
				</view>
			</view>
			<scroll-view scroll-y class="indexes margin-top" :scroll-into-view="'indexes-' + listCurID"
				:style="'height:calc(100vh - ' + CustomBar + 'px - 50px)'" :scroll-with-animation="true"
				:enable-back-to-top="true">
				<block v-for="(item, index) in listData" :key="index">
					<view class="cu-list menu menu-avatar no-padding">
						<view class="cu-item arrow" @tap="schoolTap" :data-data="item">
							<view style="" class="cu-avatar  lg bg-blue">S</view>
							<view class="content">
								<view class="text-grey">
									<text class="text-abc">{{ item.name}}</text>
								</view>
							</view>
						</view>
					</view>
				</block>
				<adbanner></adbanner>
			</scroll-view>
			<!-- 选择显示 -->
			<view v-if="!hidden" class="indexToast">
				{{ listCur }}
			</view>
		</view>
	</view>
</template>
<style src="./school.css"></style>
<script src="./school.js"></script>