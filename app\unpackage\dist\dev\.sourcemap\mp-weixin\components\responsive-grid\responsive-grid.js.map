{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/responsive-grid/responsive-grid.vue?3599", "webpack:///D:/桌面/thinker/app/components/responsive-grid/responsive-grid.vue?0a9e", "webpack:///D:/桌面/thinker/app/components/responsive-grid/responsive-grid.vue?4652", "webpack:///D:/桌面/thinker/app/components/responsive-grid/responsive-grid.vue?6f75", "uni-app:///components/responsive-grid/responsive-grid.vue", "webpack:///D:/桌面/thinker/app/components/responsive-grid/responsive-grid.vue?6440", "webpack:///D:/桌面/thinker/app/components/responsive-grid/responsive-grid.vue?1c34"], "names": ["name", "props", "cols", "type", "default", "xs", "sm", "md", "lg", "xl", "gap", "customClass", "computed", "gridClass", "gridStyle", "display"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACqC;;;AAGnG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;gBCOvrB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QAAA;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IACA;IACAC;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;EACA;EACAQ;IACAC;MACA,QACA,mBACA,iBACA;IACA;IACAC;MACA;MACA;QACAJ;QACAK;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAo+B,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACAx/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/responsive-grid/responsive-grid.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./responsive-grid.vue?vue&type=template&id=3f4970f8&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-grid.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-grid.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-grid.vue?vue&type=style&index=0&id=3f4970f8&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3f4970f8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/responsive-grid/responsive-grid.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-grid.vue?vue&type=template&id=3f4970f8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-grid.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-grid.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"responsive-grid\" :class=\"gridClass\" :style=\"gridStyle\">\n    <slot></slot>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'responsive-grid',\n  props: {\n    // 列数配置 {xs: 1, sm: 2, md: 3, lg: 4, xl: 5}\n    cols: {\n      type: Object,\n      default: () => ({\n        xs: 1,\n        sm: 2, \n        md: 3,\n        lg: 4,\n        xl: 5\n      })\n    },\n    // 间距\n    gap: {\n      type: [String, Number],\n      default: '20rpx'\n    },\n    // 自定义类名\n    customClass: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    gridClass() {\n      return [\n        'responsive-grid',\n        this.customClass\n      ].filter(Boolean).join(' ');\n    },\n    gridStyle() {\n      const gap = typeof this.gap === 'number' ? `${this.gap}rpx` : this.gap;\n      return {\n        gap,\n        display: 'grid'\n      };\n    }\n  }\n}\n</script>\n\n<style scoped>\n.responsive-grid {\n  display: grid;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n/* 超小屏幕 (xs) */\n@media screen and (max-width: 576rpx) {\n  .responsive-grid {\n    grid-template-columns: repeat(1, 1fr);\n  }\n}\n\n/* 小屏幕 (sm) */\n@media screen and (min-width: 577rpx) and (max-width: 768rpx) {\n  .responsive-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n/* 中等屏幕 (md) */\n@media screen and (min-width: 769rpx) and (max-width: 1024rpx) {\n  .responsive-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n/* 大屏幕 (lg) */\n@media screen and (min-width: 1025rpx) and (max-width: 1440rpx) {\n  .responsive-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n/* 超大屏幕 (xl) */\n@media screen and (min-width: 1441rpx) {\n  .responsive-grid {\n    grid-template-columns: repeat(5, 1fr);\n  }\n}\n\n/* 自定义列数类 */\n.responsive-grid.cols-1 { grid-template-columns: repeat(1, 1fr); }\n.responsive-grid.cols-2 { grid-template-columns: repeat(2, 1fr); }\n.responsive-grid.cols-3 { grid-template-columns: repeat(3, 1fr); }\n.responsive-grid.cols-4 { grid-template-columns: repeat(4, 1fr); }\n.responsive-grid.cols-5 { grid-template-columns: repeat(5, 1fr); }\n.responsive-grid.cols-6 { grid-template-columns: repeat(6, 1fr); }\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-grid.vue?vue&type=style&index=0&id=3f4970f8&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-grid.vue?vue&type=style&index=0&id=3f4970f8&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753559962998\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}