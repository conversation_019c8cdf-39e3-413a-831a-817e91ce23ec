/* 页面容器 */
.course-buy-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	padding-bottom: 140rpx;
}
/* 主内容区域 */
.main-content {
	padding: 30rpx;
}
/* 卡片通用样式 */
.course-card, .agreement-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	margin-bottom: 30rpx;
	overflow: hidden;
}
/* 卡片头部 */
.card-header {
	padding: 24rpx 30rpx;
	border-bottom: 1rpx solid #eaeef5;
	display: flex;
	align-items: center;
}
.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.icon-title {
	color: #3b7ff3;
	margin-right: 16rpx;
	font-size: 36rpx;
}
/* 卡片内容 */
.card-body {
	padding: 20rpx 30rpx;
}
/* 信息项 */
.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f0f3f8;
}
.info-item:last-child {
	border-bottom: none;
}
.info-label {
	display: flex;
	align-items: center;
	color: #666;
	font-size: 28rpx;
}
.icon-custom {
	color: #3b7ff3;
	margin-right: 16rpx;
	font-size: 32rpx;
}
.info-value {
	color: #333;
	font-size: 28rpx;
	font-weight: 500;
}
.price {
	color: #ff6b6b;
	font-size: 36rpx;
	font-weight: bold;
}
/* 标签容器 */
.tag-container {
	display: flex;
	flex-wrap: wrap;
}
.content-tag {
	background: linear-gradient(135deg, #3b7ff3, #6aa1ff);
	color: #fff;
	font-size: 24rpx;
	padding: 8rpx 20rpx;
	border-radius: 30rpx;
	margin-right: 16rpx;
	margin-bottom: 10rpx;
}
/* 协议列表 */
.agreement-list {
	padding: 10rpx 0;
}
.agreement-item {
	display: flex;
	padding: 16rpx 0;
	align-items: flex-start;
}
.agreement-number {
	background-color: #3b7ff3;
	color: white;
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 24rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}
.agreement-text {
	color: #666;
	font-size: 26rpx;
	line-height: 1.6;
}
/* 底部操作区 */
.footer-action {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 30rpx;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
}
.confirm-btn {
	background: linear-gradient(135deg, #3b7ff3, #6aa1ff);
	color: #fff;
	border-radius: 50rpx;
	font-size: 32rpx;
	font-weight: bold;
	height: 90rpx;
	line-height: 90rpx;
	box-shadow: 0 10rpx 20rpx rgba(59, 127, 243, 0.3);
	transition: all 0.3s;
}
.confirm-btn:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
	box-shadow: 0 5rpx 10rpx rgba(59, 127, 243, 0.3);
}
