<view><back vue-id="e18ff65c-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="答题结果" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view class="result-container"><view class="result-card bg-white shadow"><view class="score-circle"><view class="circle-bg"><view class="circle-progress" style="{{('transform: rotate('+responseData.right_rate*3.6+'deg)')}}"></view></view><view class="circle-content"><text class="circle-percent">{{responseData.right_rate+"%"}}</text><text class="circle-label">正确率</text></view></view><view class="result-stats"><view class="stat-item"><text class="stat-value text-blue">{{responseData.right_count}}</text><text class="stat-label">做对题数</text></view><view class="stat-divider"></view><view class="stat-item"><text class="stat-value">{{responseData.answer_count+"/"+responseData.all_count}}</text><text class="stat-label">已完成</text></view></view><view class="result-comment"><block wx:if="{{responseData.right_rate>=80}}"><text class="comment-text">太棒了！继续保持！</text></block><block wx:else><block wx:if="{{responseData.right_rate>=60}}"><text class="comment-text">不错的表现，还可以更好！</text></block><block wx:else><text class="comment-text">再接再厉，继续努力！</text></block></block></view></view><view class="action-buttons"><button class="cu-btn round bg-gradual-blue shadow lg" data-action="1" data-event-opts="{{[['tap',[['redirectTap',['$event']]]]]}}" bindtap="__e"><text class="cuIcon-home"></text>题库首页</button><button class="cu-btn round bg-gradual-purple shadow lg margin-top" data-action="2" data-event-opts="{{[['tap',[['redirectTap',['$event']]]]]}}" bindtap="__e"><text class="cuIcon-my"></text>个人中心</button></view></view></block><adfootbanner vue-id="e18ff65c-2" unitId="adunit-9f7b1de89ce8659f" bind:__l="__l"></adfootbanner></view>