import {
	get,
	post,
	upload
} from "../../common/js/http.js";

module.exports = {

	/**
	 * 绑定公众号
	 * @param {Object} data
	 */
	bindMp: function(data) {
		return post('user/bindMp', data);
	},

	/**
	 * 找回密码
	 * @param {Object} data
	 */
	resetPassword: function(data) {
		return post('user/resetPassword', data);
	},

	/**
	 * 用户更新
	 * @param {Object} data
	 */
	userUpdate: function(data) {
		return post('user/update', data);
	},

	/**
	 * 文章详情
	 * @param Number id
	 */
	getArticleInfo(id) {
		return get('article/getInfo', {
			id: id
		});
	},

	/**
	 * 文章列表
	 * @param {Object} data
	 */
	getArticleList: function(data) {
		return get('article/getList', data);
	},
	
	/**
	 * 文章分类列表
	 * @param {Object} data
	 */
	getArticleCategoryList: function(data) {
		return get('article/category/getList', data);
	},

	/**
	 * 发送短信
	 * @param {Object} data
	 */
	sendSmsCode: function(data) {
		return post('verify/sendSmsCode', data);
	},

	/**
	 * 发送邮箱
	 * @param {Object} data
	 */
	sendMailCode: function(data) {
		return post('verify/sendMailCode', data);
	},

	/**
	 * 邮箱变更
	 * @param {Object} data
	 */
	changeMail: function(data) {
		return post('user/changeMail', data);
	},

	/**
	 * 绑定OpenId
	 * @param {Object} data
	 */
	bindOpenId: function(data) {
		return post('user/bind_openid', data);
	},


	/**
	 * 绑定手机
	 * @param {Object} data
	 */
	bindMobile: function(data) {
		return post('user/bindMobile', data);
	},

	/**
	 * 更新用户信息
	 * @param {Object} id
	 */
	updateUser: function(data) {
		return post('user/update', data);
	},

	/**
	 * 课程信息
	 * @param {Object} id
	 */
	courseInfo: function(id) {
		return get('course/getInfo', {
			id: id
		});
	},

	/**
	 * 兑换资料
	 * @param {Object} id
	 */
	exchangeMaterial: function(id) {
		return post('material/buy', {
			id: id
		});
	},


	/**
	 * 创建订单
	 * @param {Object} id
	 */
	createOrder: function(data) {
		return post('order/create', data);
	},


	/**
	 * 获取资料信息
	 * @param {Object} id
	 */
	getMaterialInfo: function(id) {
		return get('material/info', {
			id: id
		});
	},

	/**
	 * 获取资料分享规则
	 * @param {Object} id
	 */
	getMaterialShareRule: function() {
		return get('material/shareRule', {});
	},

	/**
	 * 获取资料列表
	 * @param {Object} course_id
	 */
	getMaterialList: function(course_id) {
		return get('material/list', {
			course_id: course_id
		});
	},

	/**
	 * 保存资料分享内容
	 * @param {Object} id
	 */
	saveMaterialShare: function(course_id, content) {
		return post('material/shareSave', {
			course_id: course_id,
			content: content
		});
	},

	/**
	 * 获取资料分享列表
	 */
	getMaterialShareList: function() {
		return get('material/shareList', {});
	},

	/**
	 * 创建互助任务
	 * @param {Object} data_id
	 * @param {Object} data_type
	 */
	createHelpTask: function(data_id, data_type) {
		return post('helpTask/create', {
			data_id: data_id,
			data_type: data_type
		});
	},

	/**
	 * 互助任务信息
	 * @param {Object} id
	 */
	helpTaskInfo: function(id) {
		return get('helpTask/info', {
			id: id
		});
	},

	/**
	 * 互助任务保存
	 * @param {Object} id
	 */
	helpTaskSave: function(data) {
		return post('helpTask/save', data);
	},

	/**
	 * 互助任务帮抢
	 * @param {Object} id
	 */
	helpTaskGrab: function(id) {
		return post('helpTask/grab', {
			id: id
		});
	},

	/**
	 * 互助明细编辑
	 * @param {Object} id
	 */
	helpTaskItemSave: function(data) {
		return post('helpTask/itemSave', data);
	},

	/**
	 * 上传文件到OSS
	 */
	uploadFile: function(name, file, formData) {
		return upload(name, file, formData,{});
	},

	/**
	 * 转让会员信息
	 */
	vipTransferInfo: function() {
		return get('user/vip/transferInfo', {});
	},

	/**
	 * 转让会员
	 */
	vipTransfer: function(data) {
		return post('user/vip/transfer', data);
	},

	/**
	 * 推广中心-首页信息
	 */
	promoteInfo: function() {
		return get('promote/info', {});
	},

	/**
	 * 推广中心-宣传素材
	 */
	shareImage: function(data) {
		return get('promote/shareImage', data);
	},
	
	/**
	 * 申请ai解析
	 */
	aiExplanation: function(data) {
		return post('question/aiExplanation', data);
	},
	
	/**
	 * 获取配置
	 * @param {Object} config_type
	 */
     getConfig: async function(config_type) {
		let res = await post('appConfig/get', {config_type:config_type});
		return res.data;
	},
};