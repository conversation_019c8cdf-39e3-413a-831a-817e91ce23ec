{"name": "贝壳刷题", "appid": "__UNI__0B72C04", "description": "贝壳刷题，免费考试刷题app", "versionName": "1.0.5", "versionCode": 103, "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Payment": {}, "Record": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"], "targetSdkVersion": 30}, "ios": {"dSYMs": false, "idfa": true, "privacyDescription": {"NSUserTrackingUsageDescription": "请放心，开启权限不会获取您在其他站点的隐私信息，该权限仅用于标识设备并保障服务安全与提示浏览体验"}}, "sdkConfigs": {"oauth": {"weixin": {"appid": "wxbeb4d4de2823d484", "UniversalLinks": ""}}, "payment": {"alipay": {"__platform__": ["android"]}}, "ad": {}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "splashscreen": {"useOriginalMsgbox": true}}}, "quickapp": {}, "mp-weixin": {"appid": "wx43897f46c2c0fc37", "setting": {"urlCheck": false, "es6": true, "minified": true}, "lazyCodeLoading": "requiredComponents", "usingComponents": true, "resizable": true, "permission": {}, "embeddedAppIdList": ["wxd9634afb01b983c0"], "plugins": {}}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true, "appid": "45768616", "setting": {"urlCheck": false}}, "mp-toutiao": {"usingComponents": true, "appid": "ttc919bfa45612a31401"}, "uniStatistics": {"enable": false}, "vueVersion": "2", "h5": {"title": "贝壳刷题", "optimization": {"treeShaking": {"enable": true}}, "router": {"mode": "hash"}, "template": "index.html"}, "fallbackLocale": "zh-Hans", "locale": "zh-Hans"}