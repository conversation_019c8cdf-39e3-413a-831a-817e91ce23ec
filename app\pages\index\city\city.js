import {
	get,
	post,
} from "@/common/js/http";
import {
	isSet
} from "@/common/js/helper";
let app = getApp();
let that = null;
export default {
	data() {
		return {
			CustomBar: this.CustomBar,
			isHidden: true,
			name: '',
			isLoad: true, // 修改这里，默认设置为true，确保组件可见
			firstVisit: true,
			listData: {},
			listInitial: {},
			listCur: '',
			listCurID: ''
		};
	},
	onLoad(options) {
		that = this;
		that.checkAgent(options);
		that.checkScene();
		if (that.checkFirst(options)) {
			that.getCityList();
		}
	},
	methods: {
		/**
		 * 处理场景值信息
		 * @description 当场景值不为0时，获取用户登录凭证并上报场景信息
		 */
		checkScene() {
			let scene = app.globalData.scene
			if (scene == 0) {
				return
			}

			//#ifdef MP-WEIXIN
			uni.login({
				success: (res) => {
					post('user/scene', {
							code: res.code,
							scene: scene
						})
						.then((res) => {
							console.log(res);
						}).catch((res) => {
							console.log(res);
						});
				}
			});
			//#endif
		},

		/**
		 * 处理代理信息记录
		 * @param {Object} options - 页面参数对象
		 * @param {string} options.agent_id - 代理ID
		 * @description 当存在代理ID时，获取用户登录凭证并记录代理关系
		 */
		checkAgent(options) {
			if (isSet(options.agent_id)) {
				let agent_id = options.agent_id;
				if (agent_id) {
					uni.login({
						success: (e) => {
							let code = e.code
							post('user/share', {
									code: code,
									agent_id: agent_id
								})
								.then(function(res) {
									console.log(res);
								});
						}
					});
				}
			}
		},

		/**
		 * 检查是否首次访问
		 * @param {Object} options - 页面参数对象
		 * @param {number} [options.first_visit] - 首次访问标记
		 * @returns {boolean} 是否为首次访问
		 * @description 根据缓存和页面参数判断是否首次访问，非首次访问时重定向到首页
		 */
		checkFirst(options) {
			let isNew = true; // 判断是否真正new

			let cacheFirstVisit = app.globalData.config.storage.getFristData();

			if (cacheFirstVisit == '') {
				cacheFirstVisit = 1;
			}

			let opt_first_visit = typeof options.first_visit != 'undefined' ? options.first_visit : -1;

			if (opt_first_visit == -1) {
				if (cacheFirstVisit > 1) {
					isNew = false;
				}
			} else {
				if (opt_first_visit > 1) {
					isNew = false;
				}
			}

			if (isNew == false) {
				uni.reLaunch({
					url: '../../index/index'
				});
				return false;
			}

			return isNew;
		},

		/**
		 * 获取城市列表数据
		 * @description 通过API请求获取城市分组数据，支持按名称搜索
		 * @returns {Promise} 包含城市列表数据的Promise对象
		 */
		getCityList() {
			get('region/get_group', {
				name: that.name
			}).then((res) => {
				console.log(res);
				that.isLoad = true;
				that.listData = res.data;
			}).catch((res) => {
				console.log(res);
			});
		},

		/**
		 * 处理搜索输入事件
		 * @param {Object} options - 输入事件对象
		 * @param {Object} options.detail - 输入详情
		 * @param {string} options.detail.value - 输入框值
		 * @description 更新搜索关键词并触发搜索
		 */
		handleSearchInput(options) {
			that.name = options.detail.value;
		},

		/**
		 * 处理搜索城市事件
		 * @param {Object} options - 事件对象
		 * @description 触发城市列表数据刷新
		 */
		handleSearchCity(options) {
			that.getCityList();
		},

		/**
		 * 处理城市选择事件
		 * @param {Object} t - 点击事件对象
		 * @param {Object} t.currentTarget.dataset.data - 选中的城市数据
		 * @description 保存选中城市信息并根据考试数据状态跳转页面
		 */
		handleCitySelect(t) {
			let currentCity = t.currentTarget.dataset.data;
			app.globalData.config.storage.setCurrentCityData(currentCity);
			let examData = app.globalData.config.storage.getCurrentExamData();
			if (typeof examData.id != 'undefined') {
				uni.reLaunch({
					url: '../../index/index'
				});
			} else {
				uni.navigateTo({
					url: '../exam/exam'
				});
			}
		},


		/**
		 * 处理获取当前选中项
		 * @param {Object} e - 触摸事件对象
		 * @param {Object} e.target - 触发事件的元素
		 * @param {string} e.target.id - 选中项ID
		 * @description 显示索引栏并设置当前选中的索引值
		 */
		handleGetCurrent(e) {
			that.hidden = false;
			that.listCur = e.target.id;
		},

		/**
		 * 处理设置当前选中项
		 * @param {Object} e - 触摸事件对象
		 * @description 隐藏索引栏并保持当前选中状态
		 */
		handleSetCurrent(e) {
			that.hidden = true;
			that.listCur = that.listCur
		},

		/**
		 * 处理滑动选择项目
		 * @param {Object} e - 触摸事件对象
		 * @param {Array} e.touches - 触摸点数组
		 * @param {Object} e.touches[0] - 第一个触摸点
		 * @param {number} e.touches[0].clientY - 触摸点Y坐标
		 * @description 根据滑动位置计算并更新当前选中的城市索引
		 */
		handleTouchMove(e) {
			let y = e.touches[0].clientY;
			let offsettop = this.boxTop;
			let that = this; // 判断选择区域,只有在选择区才会生效
			if (y > offsettop) {
				let num = parseInt((y - offsettop) / 20);
				that.listCur = that.listData[num];
			}
		},

		/**
		 * 处理触摸开始事件
		 * @description 开始触摸选择时显示索引栏
		 */
		handleTouchStart() {
			that.hidden = false;
		},

		/**
		 * 处理触摸结束事件
		 * @description 结束触摸选择时隐藏索引栏并确认选中项
		 */
		handleTouchEnd() {
			that.hidden = true;
			that.listCurID = this.listCur;
		}
	}
};