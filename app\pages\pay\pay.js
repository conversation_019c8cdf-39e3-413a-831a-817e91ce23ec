let app = getApp();
let that = null;
export default {
	data() {
		return {
			id: 0,
			info: [],
			isLoad: false,
			payStatus: true,
			useScanPay: false,
			scanPayUrl: '',
			useCheckPay: false,
		};
	},
	onLoad(options) {
		that = this;
		app.globalData.checkAppIsAuditAndRedirect();
		that.id = options.id;
		that.getInfo();
	},
	onShow() {
		let extraData = app.globalData.otherMcpExtraData;
		if (extraData != null) {
			if (extraData.code == 0) {
				that.paySuccessCheckTap();
			} else {
				app.showToast(extraData.msg);
				app.globalData.otherMcpExtraData = null;
				uni.navigateBack({
					delta: 1
				});
				return;
			}
		}
	},
	onHide() {
		app.stopInterval();
	},
	onUnload() {
		app.stopInterval();
	},
	onBackPress() {
		app.stopInterval();
	},
	methods: {
		getInfo() {
			app.globalData.server
				.getRequest('order/info', {
					id: that.id
				})
				.then(ret => {
					that.info = ret.data;
					that.isLoad = true;
				})
				.catch(err => {
					console.log(err);
				});
		},
		confirmPayTap() {
			app.globalData.server
				.getRequest('order/pay', {
					id: that.id,
				})
				.then((ret) => {
					that.openPay(ret);
				})
				.catch((err) => {
					console.log(err);
				});
		},
		openPay(res) {
			let payRes = res.data;
			let payData = payRes.payData;
			if (payRes.action == 1) {
				// 跳转支付	
				location.href = payData.payUrl
				return;
			} else if (payRes.action == 2) {
				// 扫码支付	
				that.useScanPay = true;
				that.scanPayUrl = payData.payUrl;
				that.useCheckPay = true;
				return;
			} else if (payRes.action == 3) {
				// 个人小程序跳转支付
				uni.openEmbeddedMiniProgram({
					appId: 'wxd9634afb01b983c0',
					path: '/pages/pay/pay',
					extraData: payData,
					success(res) {
						that.paySuccessCheckTap();
					},
					fail(res) {
						app.showToast("支付失败");
						app.stopInterval();
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							});
						}, 1500)
					}
				});
				return;
			} else if (payRes.action == 4) {

				// 原生小程序支付(支持个人/企业)
				uni.requestPayment({
					timeStamp: payData.timeStamp,
					nonceStr: payData.nonceStr,
					package: payData.package,
					signType: payData.signType,
					paySign: payData.paySign,
					success(res) {
						uni.navigateTo({
							url: '../pay/payRes?id=' + that.id
						});
					},
					fail(res) {

					},
					complete(res) {
						that.paySuccessCheckTap();
					}
				})
				return;
			} else if (payRes.action == 5) {
				// 原生APP支付
				that.useCheckPay = true;
				console.log(payData.payUrl);
				plus.runtime.openURL(payData.payUrl);
				return;
			}
		},
		onClickScanPay() {
			uni.previewImage({
				current: that.scanPayUrl,
				urls: [that.scanPayUrl],
				showmenu: true
			});
		},
		paySuccessCheckTap() {
			app.startInterval(() => {
				app.globalData.server
					.getRequest('order/info', {
						id: that.id
					})
					.then(ret => {
						if (ret.data.orderInfo.status != 1) {
							app.stopInterval();
							
							// 获取订单信息
							const orderType = ret.data.orderInfo.order_type;
							let extendData = JSON.parse(ret.data.orderInfo.extend);
							
							// 根据订单类型跳转到不同页面
							switch (orderType) {
								case 1: // 会员订单
									uni.switchTab({
										url: '../user/user'
									});
									break;
								case 2: // 题库订单
									uni.navigateTo({
										url: '../practice/course/detail?id=' + extendData.course_id
									});
									break;
								case 3: // 资料订单
									uni.navigateTo({
										url: '../practice/material/detail?id=' + extendData.material_id
									});
									break;
								case 4: // 网课订单
									uni.navigateTo({
										url: '../practice/course/detail?id=' + extendData.collection_id
									});
									break;
								default: // 默认跳转到支付结果页
									uni.navigateTo({
										url: '../pay/payRes?id=' + that.id
									});
							}
						}
					})
					.catch(err => {
						console.log(err);
					});
			}, 1500)
			setTimeout(() => {
				app.stopInterval();
				uni.navigateBack({
					delta: 1
				});
			}, 60000)
		}
	}
};