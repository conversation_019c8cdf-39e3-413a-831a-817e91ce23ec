<template>
  <view class="responsive-test-page">
    <back :showBackText="false" customClass="bg-gradual-blue text-white" title="响应式测试"></back>
    
    <!-- 使用CSS类的响应式布局 -->
    <view class="container">
      <view class="section">
        <view class="section-title">屏幕信息</view>
        <view class="info-card">
          <view class="info-item">
            <text class="label">当前断点:</text>
            <text class="value">{{ currentBreakpoint }}</text>
          </view>
          <view class="info-item">
            <text class="label">屏幕宽度:</text>
            <text class="value">{{ screenWidth }}px</text>
          </view>
          <view class="info-item">
            <text class="label">是否大屏:</text>
            <text class="value">{{ isLargeScreen ? '是' : '否' }}</text>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">响应式网格</view>
        <view class="test-grid grid grid-xs-1 grid-sm-2 grid-md-3 grid-lg-4 grid-xl-5">
          <view class="grid-item" v-for="n in 10" :key="n">
            <view class="item-content">{{ n }}</view>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">响应式显示/隐藏</view>
        <view class="display-test">
          <view class="test-item d-xs-block d-sm-none">
            <text class="test-text">只在超小屏显示 (xs)</text>
          </view>
          <view class="test-item d-xs-none d-sm-block d-md-none">
            <text class="test-text">只在小屏显示 (sm)</text>
          </view>
          <view class="test-item d-xs-none d-sm-none d-md-block d-lg-none">
            <text class="test-text">只在中屏显示 (md)</text>
          </view>
          <view class="test-item d-xs-none d-sm-none d-md-none d-lg-block d-xl-none">
            <text class="test-text">只在大屏显示 (lg)</text>
          </view>
          <view class="test-item d-xs-none d-sm-none d-md-none d-lg-none d-xl-block">
            <text class="test-text">只在超大屏显示 (xl)</text>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">响应式文本对齐</view>
        <view class="text-align-test">
          <text class="test-text text-xs-center text-sm-left text-md-center text-lg-right text-xl-center">
            这段文字在不同屏幕尺寸下有不同的对齐方式
          </text>
        </view>
      </view>

      <view class="section">
        <view class="section-title">响应式间距</view>
        <view class="spacing-test">
          <view class="spacing-item p-xs-1 p-sm-2 p-md-3 p-lg-3 p-xl-3">
            <text class="test-text">响应式内边距</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'responsive-test',
  data() {
    return {
      currentBreakpoint: 'xs',
      screenWidth: 375,
      isLargeScreen: false
    }
  },
  onLoad() {
    this.updateScreenInfo();
  },
  onResize() {
    this.updateScreenInfo();
  },
  methods: {
    updateScreenInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.screenWidth = res.windowWidth;
          
          if (res.windowWidth >= 1441) {
            this.currentBreakpoint = 'xl';
          } else if (res.windowWidth >= 1025) {
            this.currentBreakpoint = 'lg';
          } else if (res.windowWidth >= 769) {
            this.currentBreakpoint = 'md';
          } else if (res.windowWidth >= 577) {
            this.currentBreakpoint = 'sm';
          } else {
            this.currentBreakpoint = 'xs';
          }
          
          this.isLargeScreen = res.windowWidth >= 769;
        }
      });
    }
  }
}
</script>

<style scoped>
.responsive-test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #e0e0e0;
}

.info-card {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: bold;
  color: #666;
}

.value {
  color: #007aff;
  font-weight: bold;
}

.test-grid {
  gap: 20rpx;
}

.grid-item {
  background: white;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.item-content {
  padding: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.display-test {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.test-item {
  margin: 20rpx 0;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
}

.test-text {
  font-size: 28rpx;
  color: #333;
}

.text-align-test {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.spacing-test {
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.spacing-item {
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 2rpx solid #e9ecef;
}

/* 大屏优化 */
@media screen and (min-width: 1024rpx) {
  .container {
    padding: 40rpx;
  }
  
  .section-title {
    font-size: 36rpx;
  }
  
  .info-card,
  .display-test,
  .text-align-test,
  .spacing-test {
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  }
  
  .grid-item:hover .item-content {
    transform: scale(1.05);
    transition: transform 0.3s ease;
  }
  
  .test-item:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }
}
</style>
