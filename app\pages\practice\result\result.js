import {
	get,
	post,
} from "../../../common/js/http.js";

let app = getApp();
let that = null;

export default {
	data() {
		return {
			isLoad: false,
			responseData: {}
		};
	},
	onLoad(options) {
		that = this;
		that.caculateResult(options);
	},
	methods: {
		caculateResult(options) {
			let requestData = {
				id: options.id,
				extend_id: options.extend_id,
				type: options.mainType,
				question_type: options.question_type
			}
			post('question/answer_result', requestData)
				.then((res) => {
					that.isLoad = true;
					that.responseData = res.data;
				})
				.catch((err) => {
					console.log(err);
				});
		},
		redirectTap(options) {
			let action = options.currentTarget.dataset.action;
			let url = action == 1 ? '../../index/city/city' : '../../user/user';
			uni.reLaunch({
				url: url
			});
		}
	}
}; 