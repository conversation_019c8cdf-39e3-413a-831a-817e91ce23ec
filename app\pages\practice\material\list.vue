<template>
  <view class="material-list-ios">
    <!-- 顶部导航栏 -->
    <back :showBackText="false" customClass="bg-gradual-blue text-white" title="学习资料"></back>

    <view class="list-content" v-if="isLoad">
      <scroll-view scroll-y class="scroll-area">
        <!-- 空状态 -->
        <empty v-if="dataList.length == 0" info="暂时还没有资料" :showAd="false"></empty>
        <!-- 资料卡片列表 -->
        <view v-else>
          <view class="material-card shadow" v-for="(item, index) in dataList" :key="index" :data-id="item.id" @tap="itemTap">
            <view class="card-main">
              <view class="card-title-row">
                <text class="cuIcon-file text-blue"></text>
                <text class="card-title">{{ item.name }}</text>
              </view>
              <view class="card-tags">
                <view class="cu-tag bg-green light price-tag">
                  <text>{{ item.price }}</text>
                  <text class="cuIcon-coin text-yellow" style="margin-left: 4rpx; font-size: 24rpx;"></text>
                </view>
                <view class="cu-tag bg-blue light version-tag">
                  {{ item.version == 1 ? '在线文档' : '网盘资料' }}
                </view>
              </view>
            </view>
            <view class="card-meta-row">
              <view class="meta-item">
                <text class="cuIcon-hotfill text-red"></text>
                <text class="meta-label">兑换人数</text>
                <text class="meta-value center-value">{{ item.hot }}</text>
              </view>
              <view class="meta-action">
                <button class="cu-btn bg-blue radius detail-btn">查看详情</button>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部悬浮分享按钮 -->
    <view class="float-action-container" v-if="isLoad && !appIsAudit">
      <view class="share-button" @tap="shareTap">
        <text class="cuIcon-add"></text>
        <text>分享资料</text>
      </view>
    </view>
  </view>
</template>

<script>
	let that = null;
	let app = getApp();
	export default {
		data() {
			return {
				id: 0,
				isLoad: false,
				appIsAudit: app.globalData.checkAppIsAudit(),
				dataList: []
			};
		},
		onLoad(options) {
			that = this;
			that.id = options.id;
			that.getList();
		},
		methods: {
			itemTap(options) {
				console.log(options)
				let id = options.currentTarget.dataset.id;
				let url = `/pages/practice/material/detail?id=${id}`;
				uni.navigateTo({
					url: url
				});
			},
			shareTap(options) {
				let url = `/pages/practice/material/share?id=${that.id}`;
				uni.navigateTo({
					url: url
				});
			},
			getList() {
				if(that.appIsAudit){
					that.isLoad = true;
					return;
				}
				app.globalData.service.getMaterialList(that.id).then(res => {
						that.isLoad = true;
						that.dataList = res.data;
					})
					.catch(function(a) {
						app.showToast('获取资料失败');
					});
			},
		}
	};
</script>
<style src="./list.css"></style>
<style lang="scss" scoped>
	/* #ifndef H5 */
	page {
		height: 100%;
		background-color: #f5f7fa;
	}
	/* #endif */
	
	.material-list-ios {
		background: #f7f7fa;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}
	.list-content {
		flex: 1;
		padding: 32rpx 0 120rpx 0;
	}
	.scroll-area {
		min-height: 60vh;
	}
	.material-card {
		background: #fff;
		border-radius: 24rpx;
		margin: 32rpx 32rpx 0 32rpx;
		padding: 40rpx 32rpx 32rpx 32rpx;
		box-shadow: 0 4rpx 32rpx rgba(0,0,0,0.06);
		display: flex;
		flex-direction: column;
		gap: 24rpx;
	}
	.card-main {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
	}
	.card-title-row {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}
	.card-title {
		font-size: 36rpx;
		font-weight: 700;
		color: #222;
	}
	.card-tags {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		gap: 12rpx;
	}
	.price-tag {
		font-size: 28rpx;
		padding: 8rpx 24rpx;
	}
	.version-tag {
		font-size: 22rpx;
		padding: 4rpx 16rpx;
	}
	.card-meta-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 24rpx;
	}
	.meta-item {
		display: flex;
		align-items: baseline;
		gap: 8rpx;
		font-size: 26rpx;
		color: #888;
	}
	.center-value {
		display: flex;
		align-items: center;
		height: 100%;
	}
	.meta-label {
		margin-left: 4rpx;
	}
	.meta-value {
		color: #39b54a;
		font-weight: 600;
		margin-left: 8rpx;
	}
	.meta-action {
		display: flex;
		align-items: center;
	}
	.detail-btn {
		font-size: 28rpx;
		padding: 16rpx 40rpx;
		font-weight: 500;
		border-radius: 16rpx;
	}
	.float-action-container {
		position: fixed;
		bottom: 30rpx;
		right: 30rpx;
		z-index: 99;
	}
	.share-button {
		background: linear-gradient(135deg, #0081ff, #1cbbb4);
		border-radius: 50rpx;
		box-shadow: 0 6rpx 20rpx rgba(0, 129, 255, 0.3);
		padding: 16rpx 32rpx;
		display: flex;
		align-items: center;
	}
	.share-button text {
		color: #ffffff;
	}
	.share-button .cuIcon-add {
		font-size: 32rpx;
		margin-right: 8rpx;
	}
</style>
