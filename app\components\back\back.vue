<template>
  <view class="back-container" :class="[customClass]" :style="customStyle">
    <view class="back-bar">
      <view class="left-actions">
        <view class="action-wrapper" v-if="showBackLeft">
          <view class="left-action" @tap="BackPage" v-if="showBack">
            <text class="cuIcon-back" v-if="showBackIcon"></text>
            <text class="back-text" v-if="showBackText">{{backText}}</text>
          </view>
          <view class="divider-line" v-if="showBack && showHome"></view>
          <view class="home-action" @tap="HomePage" v-if="showHome">
            <text class="cuIcon-home" v-if="showHomeIcon"></text>
          </view>
        </view>
      </view>
      <view class="content text-white" v-if="showTitle">
        <text>{{title}}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'back',
  data() {
    return {
      StatusBar: this.StatusBar,
      CustomBar: this.CustomBar
    }
  },
  props: {
    showBack: {
      type: Boolean,
      default: true
    },
    showHome: {
      type: Boolean,
      default: true
    },
    showBackLeft: {
      type: Boolean,
      default: true
    },
    showBackIcon: {
      type: Boolean,
      default: true
    },
    showHomeIcon: {
      type: Boolean,
      default: true
    },
    showBackText: {
      type: Boolean,
      default: true
    },
    backText: {
      type: String,
      default: '返回'
    },
    customClass: {
      type: String,
      default: ''
    },
    homeUrl: {
      type: String,
      default: '/pages/index/index'
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '标题'
    },
    useDefaultBack: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    customStyle() {
      var StatusBar = this.StatusBar;
      var style = `padding-top:${StatusBar}px;`;
      return style;
    }
  },
  methods: {
    BackPage() {
      // 先触发自定义事件，让父组件有机会执行自己的逻辑
      this.$emit('beforeBack');
      
      // 如果设置了使用默认返回行为，则执行默认的返回逻辑
      if (this.useDefaultBack) {
        uni.navigateBack({
          delta: 1
        });
      }
    },
    HomePage() {
      uni.reLaunch({
        url: this.homeUrl
      });
    }
  }
}
</script>

<style>

.back-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 90rpx;
  padding: 0 10rpx;
  position: relative;
}

.left-actions {
  display: flex;
  align-items: center;
  height: 100%;
}

.action-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 30rpx;
  padding: 8rpx 20rpx;
}

.left-action {
  display: flex;
  align-items: center;
  height: 100%;
}

.home-action {
  display: flex;
  align-items: center;
  height: 100%;
  margin-left: 0;
}

.divider-line {
  height: 28rpx;
  width: 2rpx;
  background-color: rgba(255, 255, 255, 0.7);
  margin: 0 15rpx;
}

.back-text {
  font-size: 28rpx;
  margin-left: 10rpx;
  color: #ffffff;
}

.cuIcon-back, .cuIcon-home {
  font-size: 36rpx;
  color: #ffffff;
}

.content {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 1;
  width: 60%;
  margin: 0 auto;
}

.content text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: block;
}
</style>