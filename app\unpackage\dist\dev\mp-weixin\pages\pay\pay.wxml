<view><back vue-id="4fb6422e-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="收银台" bind:__l="__l"></back><block wx:if="{{isLoad}}"><view class="payment-container"><view class="order-card"><view class="order-header"><text class="order-title">订单信息</text></view><view class="order-content"><view class="order-item"><text class="cuIcon-formfill text-gray"></text><text class="item-label">订单编号：</text><text class="item-value">{{info.orderInfo.order_sn}}</text></view><view class="order-item"><text class="cuIcon-infofill text-gray"></text><text class="item-label">订单信息：</text><text class="item-value">{{info.orderInfo.remark}}</text></view><view class="order-item price-item"><text class="cuIcon-moneybagfill text-orange"></text><text class="item-label">支付金额：</text><text class="price-value">{{"¥ "+info.orderInfo.order_price}}</text></view></view></view><block wx:if="{{!useScanPay}}"><view class="payment-methods"><view class="section-title"><text class="cuIcon-pay text-blue"></text><text>选择支付方式</text></view><button data-event-opts="{{[['tap',[['confirmPayTap',['$event']]]]]}}" class="payment-btn" bindtap="__e"><text class="cuIcon-wechat text-green margin-right-xs"></text>微信支付</button></view></block><block wx:if="{{useScanPay}}"><view class="scan-pay-container"><view class="section-title"><text class="cuIcon-scan text-blue"></text><text>扫码支付</text></view><view data-event-opts="{{[['tap',[['onClickScanPay',['$event']]]]]}}" class="qrcode-container" bindtap="__e"><image class="qrcode-image" src="{{scanPayUrl}}" mode="widthFix"></image><view class="qrcode-tip"><text class="cuIcon-info text-blue margin-right-xs"></text><text>请使用微信扫一扫，扫描二维码完成支付</text></view></view><block wx:if="{{useCheckPay}}"><view class="padding-sm"><button data-event-opts="{{[['tap',[['paySuccessCheckTap',['$event']]]]]}}" class="confirm-btn" bindtap="__e"><text class="cuIcon-check margin-right-xs"></text>我已完成支付</button></view></block></view></block><view class="payment-tips"><text class="cuIcon-notificationfill text-gray"></text><text class="tips-text">付款后，系统将自动为您处理订单</text></view></view></block></view>