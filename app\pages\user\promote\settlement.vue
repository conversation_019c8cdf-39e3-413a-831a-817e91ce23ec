<template>
	<view v-if="load">
		<back :showBackText="false" customClass="bg-gradual-red text-white" title="结算记录"></back>
		<view class="cu-list sm-border menu" v-if="listData.length > 0">
			<view class="cu-item arrow" @tap="itemTap" :data-id="item.id" style="margin-top: 10rpx;"
				v-for="(item, index) in listData" :key="index">
				<view class="content " style="padding: 3rpx 5rpx 3rpx 5rpx; margin: 20rpx 0 20rpx 0;">
					<view class="text-cut"><text class="cuIcon-infofill text-blue margin-right-xs"></text>
						<text class="text-df">结算单：{{item.order_sn}} </text>
					</view>
					<view class="flex text-df">
						<view class="basis-df">
							<text class="cuIcon-rechargefill text-red margin-right-xs"></text>
							金额：<text class="text-red">{{item.real_amount}}</text>
						</view>
						<view class="basis-df">
							<text class="cuIcon-sponsorfill text-red margin-right-xs"></text>
							手续费：<text class="text-red">0.00</text>
						</view>
					</view>
					<view class="flex text-df">
						<view class="">
							<text class="cuIcon-infofill text-blue margin-right-xs"></text>
							状态：<text class="">{{item.status_name}}</text>
						</view>
					</view>
					<view class="flex text-df">
						<view class="">
							<text class="cuIcon-peoplefill text-blue margin-right-xs"></text>
							收款人：<text class="">{{item.settlement_username}}</text>
						</view>
					</view>
					<view class="flex text-df">
						<view class="">
							<text class="cuIcon-card text-blue margin-right-xs"></text>
							收款账号：<text class="">{{item.settlement_account}}</text>
						</view>
					</view>
					<view class="flex text-df" v-if="item.remark">
						<view class="">
							<text class="cuIcon-markfill text-blue margin-right-xs"></text>
							结算备注：<text class="">{{item.remark}}</text>
						</view>
					</view>
					<view class="flex text-df">
						<view class="">
							<text class="cuIcon-timefill text-blue margin-right-xs"></text>
							结算时间：<text class="">{{item.settlement_time_name}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<empty v-if="listData.length == 0" info="暂时没有结算记录"></empty>
	</view>
</template>
<style src="./settlement.css"></style>
<script src="./settlement.js"></script>