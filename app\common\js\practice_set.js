let cssObject = {
	// 动态传递的字体大小
	font: 1,
	// 动态传递的夜间模式
	isNight: false,
	// 刷题模式基础大小
	questionModelBasicFontSize: 28,
	// 题目类型Tag基础大小
	questionTypeTagBasicFontSize: 26,
	// 题目内容基础大小
	questionContentBasicFontSize: 30,
	// 题目其他标题基础大小
	questionOtherTitleBasicFontSize: 26,
	// 题目提交按钮基础高度
	questionCommitBasicHeight: 70,
	// 蓝色背景CSS
	bgBlueCss: 'background-color: #0081ff;',
	// 草灰色背景CSS
	bgGrayCss: 'background-color: #f0f0f0;',
	// 玄灰色背景CSS
	bgGreyCss: 'background-color: #8799a3;',
	// 白色背景CSS
	bgWhiteCss: 'background-color: #ffffff;',
	// 黑色背景CSS
	bgBlackCss: 'background-color: #333333;',
	// 草灰色CSS
	textGrayCss: 'color: #8799a3;',
	// 玄灰CSS
	textGreyCss: 'color: #8799a3;',
	// 白色CSS
	textWhiteCss: 'color:#ffffff;',
	// 黑色CSS
	textBlackCss: 'color:#333333;',
	// 蓝色CSS
	textBlueCss: 'color:#0081ff;',
	// 允许选择的字体选项
	selectFontList: [{
		'name': 'A',
		'value': '1',
	}, {
		'name': 'B',
		'value': '2',
	}, {
		'name': 'C',
		'value': '3',
	}],
	getCssData: function(font, isNight) {
		if (font) {
			cssObject.font = font;
		}
		cssObject.isNight = Boolean(isNight);
		return {
			setDialogCommitCss: cssObject.getSetDialogCommitCss(),
			setDialogSwitchCss: cssObject.getSetDialogSwitchCss(),
			questionModelCss: cssObject.getQuestionModelCss(),
			questionTypeTagCss: cssObject.getQuestionTypeTagCss(cssObject.isNight),
			questionTypeTagTypeCss: cssObject.getQuestionTypeTagTypeCss(),
			questionCommitCss: cssObject.getQuestionCommitCss(),
			questionContentCss: cssObject.getQuestionContentCss(),
			questionAnswerCss: cssObject.getQuestionAnswerCss(),
			questionOtherTitleCss: cssObject.getQuestionOtherTitleCss(),
			questionFootTabItemCss: cssObject.getQuestionFootTabItemCss(),
			questionTypeTagAndTitleCss: cssObject.getQuestionTypeTagAndTitleCss()
		}
	},
	getQuestionModelCss: function() {
		let fontSize = this.questionModelBasicFontSize + (cssObject.font - 1) * 2
		let css = `font-size:${fontSize}rpx;`;
		if (cssObject.isNight) {
			css += cssObject.bgBlackCss + cssObject.textGrayCss;
		} else {
			css += cssObject.bgWhiteCss;
		}
		return css
	},
	getQuestionTypeTagAndTitleCss: function() {
		let css = '';
		if (cssObject.isNight) {
			css = cssObject.bgBlackCss + cssObject.textGrayCss;
		} else {
			css = cssObject.bgWhiteCss;
		}
		return css
	},
	getQuestionTypeTagCss: function(isNight) {
		let fontSize = this.questionTypeTagBasicFontSize + (cssObject.font - 1) * 2
		let heightSize = fontSize * 2 - 1
		let css =
			`font-size:${fontSize}rpx;height:${heightSize}rpx;border-top-right-radius:3rpx;border-bottom-right-radius:3rpx;`;
		if (isNight) {
			css += cssObject.bgBlackCss + cssObject.textGrayCss;
		}
		return css
	},
	getQuestionTypeTagTypeCss: function() {
		let fontSize = this.questionTypeTagBasicFontSize + (cssObject.font - 1) * 2
		let heightSize = fontSize * 2 - 1
		let css = `font-size:${fontSize}rpx;height:${heightSize}rpx;border-top-left-radius:3rpx;border-bottom-left-radius:3rpx;`;
		
		if (cssObject.isNight) {
			css += cssObject.bgGrayCss + cssObject.textGrayCss;
		} else {
			css += cssObject.bgBlueCss + cssObject.textWhiteCss;
		}
		return css
	},
	getQuestionContentCss: function() {
		let fontSize = this.questionContentBasicFontSize + (cssObject.font - 1) * 2
		let css =
			`font-size:${fontSize}rpx;`;
		if (cssObject.isNight) {
			css += cssObject.bgBlackCss + cssObject.textGrayCss;
		}

		return css
	},
	getQuestionAnswerCss: function() {
		let fontSize = this.questionContentBasicFontSize + (cssObject.font - 1) * 2
		let css =
			`font-size:${fontSize}rpx;`;
		if (cssObject.isNight) {
			css += cssObject.bgBlackCss + cssObject.textGrayCss;
		} else {
			css += cssObject.bgWhiteCss;
		}

		return css
	},
	getQuestionCommitCss: function() {
		let fontSize = cssObject.questionContentBasicFontSize + (cssObject.font - 1) * 2
		let newHeight = cssObject.questionCommitBasicHeight + (cssObject.font - 1) * 2
		let css =
			`font-size:${fontSize}rpx;height:${newHeight}rpx;line-height:${newHeight}rpx;`;
		if (cssObject.isNight) {
			css += cssObject.bgGreyCss + cssObject.textBlackCss;
		} else {
			css += cssObject.bgBlueCss + cssObject.textWhiteCss;
		}

		return css
	},
	getQuestionContentCss: function() {
		let fontSize = this.questionContentBasicFontSize + (cssObject.font - 1) * 2
		let css =
			`font-size:${fontSize}rpx;`;
		if (cssObject.isNight) {
			css += cssObject.bgBlackCss + cssObject.textGrayCss;
		}
		return css
	},
	getQuestionOtherTitleCss: function(font) {
		let fontSize = this.questionOtherTitleBasicFontSize + (font - 1) * 2
		let css =
			`font-size:${fontSize}rpx;`;
		if (cssObject.isNight) {
			css += cssObject.textGrayCss;
		}
		return css
	},
	getSetDialogCommitCss: function() {
		let css = cssObject.bgGrayCss;
		if (cssObject.isNight) {
			css += cssObject.bgWhiteCss + cssObject.textGrayCss;
		}
		css += 'border-bottom: 0.5px solid #eee;'
		return css;
	},
	getQuestionFootTabItemCss: function(font) {
		let css = '';
		if (cssObject.isNight) {
			css += cssObject.textGreyCss;
		} else {
			css += cssObject.textBlueCss;
		}
		return css
	},
	getSetDialogSwitchCss: function() {
		let css = '';
		if (cssObject.isNight) {
			css += cssObject.bgBlackCss + cssObject.textGrayCss;
		} else {
			css += cssObject.bgWhiteCss;
		}
		return css;
	},
}
module.exports = {
	getCssData: cssObject.getCssData,
	selectFontList: cssObject.selectFontList
};
