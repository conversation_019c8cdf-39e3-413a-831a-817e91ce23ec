<template>
	<view class="page-container">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="我的题库"></back>
		<view class="content-container">
			<view class="course-list">
				<view @tap="courseTap" class="course-card" :data-item="course"
					v-for="(course, idx) in listData" :key="idx">
					<view class="course-info">
						<view class="course-title">{{ course.name }}{{ course.code ? ' (' + course.code + ')' : '' }}</view>
						<view class="course-expire">到期时间: {{ course.expire_name }}</view>
					</view>
					<view class="course-arrow">
						<text class="cuIcon-right"></text>
					</view>
				</view>
				<empty v-if="isLoad && listData.length === 0"></empty>
			</view>
		</view>
		<adfootbanner></adfootbanner>
	</view>
</template>

<script>
	const app = getApp();

	export default {
		data() {
			return {
				isLoad: false,
				listData: [],
				loading: false
			};
		},
		onLoad() {
			// 页面加载时获取课程列表
			this.getList();
		},
		onShow() {
			// 页面显示时刷新课程列表
			this.getList();
		},
		onPullDownRefresh() {
			// 下拉刷新
			this.getList(() => {
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			getList(callback) {
				if (this.loading) return;
				
				this.loading = true;
				
				app.globalData.server
					.postRequest('user/course', {})
					.then(res => {
						this.isLoad = true;
						this.listData = res.data;
						this.loading = false;
						
						if (typeof callback === 'function') {
							callback();
						}
					})
					.catch(err => {
						this.loading = false;
						app.showToast('获取列表失败');
						
						if (typeof callback === 'function') {
							callback();
						}
					});
			},

			courseTap(options) {
				const item = options.currentTarget.dataset.item;
				const url = `/pages/practice/course/detail?id=${item.course_id}`;
				
				uni.navigateTo({ url });
			}
		}
	};
</script>
<style src="./course.css"></style>