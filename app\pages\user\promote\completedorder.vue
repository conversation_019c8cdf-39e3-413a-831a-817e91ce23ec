<template>
	<view v-if="load">
		<back :showBackText="false" customClass="bg-gradual-red text-white" title="成交订单"></back>
		<view class="cu-list sm-border menu" v-if="listData.length > 0">
			<view class="cu-item arrow" @tap="itemTap" :data-id="item.id" style="margin-top: 10rpx;"
				v-for="(item, index) in listData" :key="index">
				<view class="content " style="padding: 3rpx 5rpx 3rpx 5rpx; margin: 20rpx 0 20rpx 0;">
					<view class="text-cut"><text class="cuIcon-infofill text-blue margin-right-xs"></text>
						<text class="text-df">订单编号：{{item.order_sn}} </text>
						<text class="text-sm text-gray">({{item.relation_level_name}})</text>
					</view>
					<view class="flex text-df">
						<view class="basis-df">
							<text class="cuIcon-rechargefill text-red margin-right-xs"></text>
							金额：<text class="text-red">{{item.order_amount}}</text>
						</view>
						<view class="basis-df">
							<text class="cuIcon-sponsorfill text-red margin-right-xs"></text>
							分红：<text class="text-red">{{item.bonus_amount}}</text> <text
								class="text-sm text-gray">({{item.bonus_rate_name}})</text>
						</view>
					</view>
					<view class="flex text-df">
						<view class="basis-df">
							<text class="cuIcon-tagfill text-blue margin-right-xs"></text>
							状态：<text class="">{{item.is_settlement==1 ? '已结算':'未结算'}}</text>
						</view>
						<view class="basis-df">
							<text class="cuIcon-timefill text-blue margin-right-xs"></text>
							时间：<text class="">{{item.add_date}}</text>
						</view>
					</view>
					<view class="flex text-df">
						<view class="">
							<text class="cuIcon-tagfill text-blue margin-right-xs"></text>
							结算单：<text class="">{{item.is_build_settlement_order==1 ? '已生成':'未生成'}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<empty v-if="listData.length == 0" info="暂时没有订单"></empty>
	</view>
</template>
<style src="./completedorder.css"></style>
<script src="./completedorder.js"></script>