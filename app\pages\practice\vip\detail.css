/* 网课视频播放界面样式 */
.video-container {
	width: 100%;
	background-color: #000;
	position: relative;
}

.video-player {
	width: 100%;
	height: 422rpx;
}

/* 顶部渐变线条 - 科技感 */
.video-container::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 6rpx;
	background: linear-gradient(90deg, #0081ff, #1cbbb4);
	z-index: 10;
}

/* 课程信息区域 */
.course-info {
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.course-title {
	font-size: 36rpx;
	color: #333;
	line-height: 1.4;
}

.course-desc {
	font-size: 28rpx;
	margin-top: 10rpx;
	line-height: 1.5;
}

/* 价格样式 */
.text-price {
	display: flex;
	align-items: center;
}

.text-del {
	text-decoration: line-through;
	font-size: 24rpx;
}

/* Tab切换区域 */
.tabs {
	display: flex;
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	background-color: #fff;
	position: relative;
	justify-content: center; /* 水平居中 */
}

.tab-item {
	flex: 0 0 auto;
	text-align: center;
	font-size: 30rpx;
	color: #666;
	position: relative;
	transition: all 0.3s;
	padding: 0 40rpx; /* 增加水平间距 */
	margin: 0 10rpx; /* 增加tab之间的间距 */
}

.tab-item.cur {
	color: #0081ff;
	font-weight: bold;
}

.tab-item.cur::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 6rpx;
	background-color: #0081ff;
	border-radius: 3rpx;
}

/* Tab内容区域 */
.tab-content {
	background-color: #f8f8f8;
	min-height: calc(100vh - 422rpx - 180rpx - 100rpx);
	overflow-y: auto;
	padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 富文本内容图片样式 */
.tab-content rich-text img {
	max-width: 100% !important;
	height: auto !important;
	display: block;
	margin: 0 auto;
}

/* 视频列表容器 */
.video-list-container {
	position: relative;
}

/* 视频列表样式 */
.video-item {
	padding: 24rpx 30rpx;
	transition: all 0.3s;
	display: flex;
	align-items: center;
}

.video-item .flex {
	width: 100%;
}

/* 底部安全区域，防止内容被底部按钮遮挡 */
.safe-bottom-area {
	height: 120rpx;
	width: 100%;
}

.video-item.active-video {
	background-color: rgba(0, 129, 255, 0.05);
}

.video-index {
	width: 56rpx;
	height: 56rpx;
	line-height: 56rpx;
	text-align: center;
	background-color: #f0f0f0;
	border-radius: 50%;
	color: #666;
	font-size: 26rpx;
}

.active-video .video-index {
	background-color: #0081ff;
	color: #fff;
}

/* 视频名称和标签样式优化 */
.text-cut {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 标签垂直对齐 */
.cu-tag.radius.sm {
	height: 36rpx;
	line-height: 36rpx;
	padding: 0 12rpx;
	font-size: 22rpx;
}

/* 课件列表样式 */
.material-item {
	padding: 30rpx;
	transition: all 0.3s;
}

.material-item:active {
	background-color: rgba(0, 129, 255, 0.05);
}

/* 底部工具栏 */
.bottom-layout {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1024;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	padding-bottom: env(safe-area-inset-bottom);
	background-color: #fff;
}

/* 购买按钮样式 */
.bottom-layout .bg-red {
	background: linear-gradient(90deg, #FF5722, #FF0000);
	border-radius: 40rpx;
	margin: 0 20rpx;
	height: 80rpx;
	line-height: 80rpx;
	box-shadow: 0 6rpx 10rpx rgba(255, 0, 0, 0.2);
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 学习按钮样式 */
.bottom-layout .bg-blue {
	background: linear-gradient(90deg, #0081FF, #1CBBB4);
	border-radius: 40rpx;
	margin: 0 20rpx;
	height: 80rpx;
	line-height: 80rpx;
	box-shadow: 0 6rpx 10rpx rgba(0, 129, 255, 0.2);
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 科技感按钮 */
.cu-btn.tech-btn {
	background: linear-gradient(90deg, #0081ff, #1cbbb4);
	color: #fff;
	border-radius: 10rpx;
}

/* 科技感边框 */
.tech-border {
	border: 1px solid transparent;
	border-image: linear-gradient(90deg, #0081ff, #1cbbb4);
	border-image-slice: 1;
}

/* 分隔线样式 */
.divider-line {
	height: 16rpx;
	background-color: #f5f5f5;
	position: relative;
}

.divider-line::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 1rpx;
	background-color: #eee;
}

.divider-line::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 1rpx;
	background-color: #eee;
}

/* 空状态样式 */
.empty-state-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 30rpx;
}

.empty-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background-color: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30rpx;
}

.empty-icon text {
	font-size: 60rpx;
	color: #8799a3;
}

.empty-text {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.empty-text .title {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.empty-text .subtitle {
	font-size: 26rpx;
	color: #8799a3;
	text-align: center;
}

/* 视频封面样式 */
.video-cover {
	position: relative;
	width: 100%;
	height: 422rpx;
	background-color: #000;
	overflow: hidden;
}

.video-cover image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.video-duration {
	position: absolute;
	bottom: 16rpx;
	right: 16rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
}

.video-tag {
	position: absolute;
	top: 16rpx;
	left: 16rpx;
	background-color: #FF6A00;
	color: #fff;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
}

.play-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
}

.play-icon .cuIcon-playfill {
	font-size: 50rpx;
	color: #fff;
	margin-left: 8rpx;
	/* 稍微偏右一点，视觉上更居中 */
}

/* 免费标签样式 */
.free-tag {
	display: inline-flex;
	align-items: center;
	margin-left: 0;
	vertical-align: middle;
	height: 36rpx;
	line-height: 36rpx;
}

/* 新的第三方平台标识 - 丝带式设计 */
.third-party-ribbon {
	position: absolute;
	top: 0;
	right: 0;
	width: 200rpx;
	height: 200rpx;
	overflow: hidden;
	z-index: 3;
	pointer-events: none;
}

.third-party-ribbon::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 40rpx;
	height: 40rpx;
	background: rgba(0, 0, 0, 0.4);
	box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.ribbon-content {
	position: absolute;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 280rpx;
	height: 50rpx;
	background: linear-gradient(135deg, #FF9800, #FF5722);
	box-shadow: 0 5rpx 10rpx rgba(0, 0, 0, 0.3);
	transform: rotate(45deg);
	right: -70rpx;
	top: 40rpx;
	text-align: center;
	line-height: 50rpx;
	color: #fff;
	font-size: 24rpx;
	font-weight: bold;
	z-index: 2;
}

.ribbon-content text {
	color: #fff;
	text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

.ribbon-content text.cuIcon-attention {
	margin-right: 6rpx;
	font-size: 24rpx;
}

/* 第三方平台提示标签 - 课程信息区域 */
.third-party-notice {
	margin: 16rpx 0;
}

.third-party-notice .cu-tag {
	padding: 8rpx 16rpx;
	font-size: 24rpx;
	line-height: 1.4;
	display: inline-flex;
	align-items: center;
	border: 1px solid rgba(255, 152, 0, 0.3);
}

/* 第三方平台详细说明框 */
.third-party-info-box {
	background-color: rgba(255, 152, 0, 0.1);
	border-left: 8rpx solid #FF9800;
	padding: 20rpx;
	margin-bottom: 30rpx;
	border-radius: 8rpx;
}

.third-party-info-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #FF9800;
	margin-bottom: 10rpx;
	display: flex;
	align-items: center;
}

.third-party-info-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 第三方平台提醒 - 视频列表上方 */
.third-party-reminder {
	display: flex;
	align-items: center;
	background-color: rgba(255, 152, 0, 0.1);
	padding: 16rpx 20rpx;
	font-size: 24rpx;
	color: #FF9800;
}

.third-party-reminder .cuIcon-info {
	margin-right: 10rpx;
	font-size: 32rpx;
}

/* 客服二维码弹窗样式 */
.qrcode-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.qrcode-container {
	width: 600rpx;
	background-color: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
	animation: modalIn 0.3s ease;
}

@keyframes modalIn {
	from {
		transform: scale(0.8);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

.qrcode-header {
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #eee;
}

.qrcode-title {
	font-size: 34rpx;
	font-weight: bold;
	color: #333;
}

.qrcode-header .cuIcon-close {
	font-size: 40rpx;
	color: #999;
	padding: 10rpx;
}

.qrcode-content {
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.qrcode-tips {
	display: flex;
	align-items: flex-start;
	background-color: #f0f9ff;
	padding: 20rpx;
	border-radius: 10rpx;
	margin-bottom: 30rpx;
	width: 100%;
}

.qrcode-tips .cuIcon-info {
	font-size: 36rpx;
	margin-right: 10rpx;
	flex-shrink: 0;
}

.qrcode-tips .tips-text {
	font-size: 26rpx;
	color: #333;
	line-height: 1.5;
}

.qrcode-image {
	width: 400rpx;
	height: 400rpx;
	margin: 20rpx 0;
	border: 1rpx solid #eee;
	padding: 10rpx;
}

.qrcode-instruction {
	font-size: 26rpx;
	color: #666;
	margin-top: 10rpx;
}

.qrcode-footer {
	padding: 30rpx;
	display: flex;
	justify-content: center;
	border-top: 1rpx solid #eee;
}

.qrcode-footer .cu-btn {
	width: 80%;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 40rpx;
	font-size: 30rpx;
	background: linear-gradient(90deg, #0081FF, #1CBBB4);
	box-shadow: 0 6rpx 10rpx rgba(0, 129, 255, 0.2);
}