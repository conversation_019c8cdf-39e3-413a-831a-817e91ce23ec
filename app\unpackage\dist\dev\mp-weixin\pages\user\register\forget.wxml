<view><back vue-id="d9164e90-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="找回密码" bind:__l="__l"></back><block wx:if="{{!checkAppIsAudit}}"><view><safe-check vue-id="d9164e90-2" sid="{{username}}" scene="{{40}}" status="{{showConfirm}}" confirmText="确认输入" confirmTextClass="cuIcon-roundcheck" data-event-opts="{{[['^updateSid',[['__set_sync',['$0','username','$event'],['']]]],['^updateStatus',[['__set_sync',['$0','showConfirm','$event'],['']]]],['^complete',[['safeCheckCompleteTap']]]]}}" bind:updateSid="__e" bind:updateStatus="__e" bind:complete="__e" bind:__l="__l"></safe-check><view class="cu-form-group margin-top"><view class="title">手机号码</view><input maxlength="32" placeholder="请输入手机号码" placeholderStyle="color:#999;" type="text" data-event-opts="{{[['input',[['usernameInputTap',['$event']]]]]}}" bindinput="__e"/></view><view class="cu-form-group margin-top"><view class="title">新的密码</view><input maxlength="32" password="{{true}}" placeholder="请输入新的密码" placeholderStyle="color:#999;" type="text" data-event-opts="{{[['input',[['passwordInputTap',['$event']]]]]}}" bindinput="__e"/></view><view class="cu-form-group margin-top"><view class="title">短信验证</view><input maxlength="8" placeholder="请输入短信验证码" placeholderStyle="color:#999;" type="number" data-event-opts="{{[['input',[['verifyCodeInputTap',['$event']]]]]}}" bindinput="__e"/><button data-event-opts="{{[['tap',[['sendVerifyTap',['$event']]]]]}}" class="cu-btn bg-green shadow" bindtap="__e">{{countDown}}</button></view><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['submitTap',['$event']]]]]}}" class="cu-btn bg-blue lg" bindtap="__e">确定</button></view></view></block></view>