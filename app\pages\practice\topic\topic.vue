<template>
	<view>
		<back :showBackText="false" customClass="bg-gradual-blue text-white" :title="title"></back>

		<view class="flex flex-direction" style="min-height: 100vh;" v-if="isLoad">
			<view class="flex-sub">
				<empty v-if="listData.length == 0" info="暂无题目"></empty>

				<view @tap="practiceTap" v-if="listData.length > 0" class="course-layout" :data-index="idx"
					v-for="(item, idx) in listData" :key="item.idx">
					<image class="course-icon" src="/static/img/ic_topic_1.png" v-if="item.question_type == 1"></image>

					<image class="course-icon" src="/static/img/ic_topic_2.png" v-else-if="item.question_type == 2">
					</image>

					<image class="course-icon" src="/static/img/ic_topic_3.png" v-else-if="item.question_type == 3">
					</image>

					<image class="course-icon" src="/static/img/ic_topic_4.png" v-else-if="item.question_type == 4">
					</image>

					<image class="course-icon" src="/static/img/ic_topic_5.png" v-else-if="item.question_type == 5">
					</image>

					<image class="course-icon" src="/static/img/ic_topic_8.png" v-else-if="item.question_type == 8">
					</image>

					<image class="course-icon" src="/static/img/ic_topic_8.png" v-else></image>

					<view class="course-name">{{ item.name }}</view>					<view class="count">{{ item.count }}题</view>

					<text class="arrow cuIcon-title"></text>
				</view>
			</view>
			<adfootbanner></adfootbanner>
		</view>
	</view>
</template>

<script>
	let app = getApp();
	let that = null;
	export default {
		data() {
			return {
				listData: [],
				title: '',
				isLoad: false,
				mainType: 0,
				id: '',
				extend_id: '',
				appIsAudit: true,
				isIosVirtualPay: true,
			};
		},
		onLoad(options) {
			that = this;
			that.id = options.id;
			that.extend_id = options.extend_id ? options.extend_id : 0;
			that.title = options.title;
			that.mainType = options.mainType;
			that.appIsAudit = app.globalData.checkAppIsAudit();
			that.isIosVirtualPay = app.globalData.checkIsIosVirtualPay();
		},
		onShow: function(e) {
			//mainType:1.普通题库 2.收藏题目 3.错误题目 4.历史题库
			that.getQuestionType();
		},
		methods: {
			getQuestionType() {
				app.globalData.server
					.getRequest('course/get_question_type', {
						id: that.id,
						type: that.mainType,
						extend_id: that.extend_id,

					})
					.then(function(e) {
						for (var o = [], i = 0; i < e.data.length; i++) {
							o.push(e.data[i]);
						}
						that.listData = o;
						that.isLoad = true;
					})
					.catch(function(a) {
						app.showToast('获取题型失败');
					});
			},
			practiceTap(t) {
				let e = that.listData[t.currentTarget.dataset.index];
				let o = that;
				let i = `../practice?title=${o.title}&id=${o.id}&extend_id=${o.extend_id}&mainType=${o.mainType}&max_page=${e.max_page}&question_type=${e.question_type}&question_count=${e.count}`;
				uni.navigateTo({
					url: i
				});
			},
			onBuyTap() {
				let url = '/pages/practice/course/buy?id=' + that.id;
				uni.navigateTo({
					url: url
				});
			},
			onCourseTap() {
				let url = '/pages/practice/course/detail?id=';
				uni.navigateTo({
					url: url + that.id
				});
			}
		}
	};
</script>
<style src="./topic.css"></style>