let app = getApp();
let that = null;
export default {
	data() {
		return {
			isLoad: false,
			chargeInfo: {},
			vipComboId: 0,
			appIsAudit: false,
			showConfirmNotice: false
		};
	},
	onLoad(options) {
		that = this;
	},
	onShow() {
		that.appIsAudit = app.globalData.checkAppIsAudit();
		if (that.appIsAudit) {
			uni.reLaunch({
				url: '/pages/index/index'
			});
		}
		that.getInfo();
	},
	methods: {
		getInfo() {
			app.globalData.server
				.postRequest('user/vip/chargeInfo', {})
				.then(function(res) {
					console.log(res);
					that.isLoad = true;
					that.chargeInfo = res.data;
				})
				.catch(function(err) {
					console.log(err);
				});
		},
		toCourseTap() {
			let url = '/pages/practice/vip/vip';
			uni.navigateTo({
				url: url
			});
		},
		onPriceClick(options) {
			let key = options.currentTarget.dataset.index;
			let item = that.chargeInfo.vipComboList[key];
			that.vipComboId = item.id;
		},

		/**
		 * 确认充值
		 * @param {Object} options
		 */
		onChargeCommit(options) {
			if (that.vipComboId === 0) {
				app.showToast('请选择充值套餐');
				return;
			}
			uni.showModal({
				title: '充值确认',
				cancelText: '我不同意',
				confirmText: '我同意',
				content: '我已阅读并同意充值说明条例中所有内容',
				success: (res) => {
					if (res.confirm) {
						app.globalData.server
							.getRequest('order/create', {
								type: 1,
								vip_comb_id: that.vipComboId
							})
							.then(function(res) {
								let id = res.data.order_id;
								let url = '/pages/pay/pay?id=' + id;
								uni.navigateTo({
									url: url
								});

							})
							.catch(function(res) {
								app.showToast('创建订单失败');
							});
					}
				}
			});
		}
	}
};